package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.nercar.ingredient.domain.po.MaterialElements;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 标准原料
 * @TableName standard_rwa_materials
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StandardRawMaterialsVO extends BaseVO {
    /**
     * 
     */
    @TableId
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @Schema(description = "计算结果ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long calculationResultId;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 主元素
     */
    @Schema(description = "主元素")
    private String element;

    /**
     * 次元素
     */
    @Schema(description = "次元素")
    private String secondaryElement;

    /**
     * C元素
     */
    @Schema(description = "C元素")
    private String carbonElement;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long departmentId;

    /**
     * 生产设备ID
     */
    @Schema(description = "生产设备ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long productionEquipmentId;

    /**
     * 基准品味%
     */
    @Schema(description = "基准品味%")
    private BigDecimal composition;

    /**
     * 收得率%
     */
    @Schema(description = "收得率%")
    private BigDecimal yieldRate;

    /**
     * 价格
     */
    @Schema(description = "价格")
    private BigDecimal price;

    /**
     * 是否自定义
     */
    @Schema(description = "是否自定义")
    private String isCustom;

    /**
     * 原料类别
     */
    @Schema(description = "原料类别")
    private String category;

    /**
     * 优先级
     */
    @Schema(description = "优先级")
    private Integer priority;

    /**
     * 碳含量
     */
    @Schema(description = "碳含量")
    private Integer cContent;

    @Schema(description = "创建人")
    private String createuser;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;

    /**
     * 配入量%
     */
    @Schema(description = "配入量%")
    private BigDecimal averageValue;
    /**
     * 单耗
     */
    @Schema(description = "单耗")
    private String singleConsume;
    /**
     * 重量
     */
    @Schema(description = "重量%")
    private BigDecimal wieght;

    @Schema(description = "部门名称")
    private String departmentName;

    @Schema(description = "设备名称")
    private String equipmentName;
    /**
     * 成本
     */
    @Schema(description = "成本")
    private BigDecimal cost;

    /**
     * 元素集合
     */
    private List<MaterialElements> materialElementsList;

    /**
     * 物料代码
     */
    @Schema(description = "物料代码")
    private String materialCode;

    /**
     * 标准号
     */
    @Schema(description = "标准号")
    private String standardNo;
}