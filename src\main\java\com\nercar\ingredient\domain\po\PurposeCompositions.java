package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 目标成分
 * @TableName purpose_compositions
 */
@TableName(value ="purpose_compositions")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PurposeCompositions extends BaseEntity {
    /**
     * 主键
     */
    @TableId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 标准配料ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long standardIngredientRecordId;

    /**
     * 元素名称
     */
    private String elementName;

    /**
     * 最小值
     */
    private BigDecimal minValue;

    /**
     * 最大值
     */
    private BigDecimal maxValue;

    /**
     * 平均值（目标值）
     */
    private BigDecimal averageValue;

    /**
     * 数学符号
     */
    private String code;

    /**
     * 成分类型：内控成分、配料内控
     */
    @Schema(description = "成分类型：内控成分、配料内控")
    private String type;

    /**
     * 排序序号
     */
    @Schema(description = "排序序号")
    private Integer sortOrder;

    /**
     *
     */
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    private String createuser;

    /**
     *
     */
    @TableField(value = "createtime",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     *
     */
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;



}