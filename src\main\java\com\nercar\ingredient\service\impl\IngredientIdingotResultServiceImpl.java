package com.nercar.ingredient.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.po.IngredientIdingotResult;
import com.nercar.ingredient.mapper.IngredientIdingotResultMapper;
import com.nercar.ingredient.service.IngredientIdingotResultService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【ingredient_idingot_result(配料单成锭率)】的数据库操作Service实现
* @createDate 2025-04-01 13:55:28
*/
@Service
public class IngredientIdingotResultServiceImpl extends ServiceImpl<IngredientIdingotResultMapper, IngredientIdingotResult>
    implements IngredientIdingotResultService{

}




