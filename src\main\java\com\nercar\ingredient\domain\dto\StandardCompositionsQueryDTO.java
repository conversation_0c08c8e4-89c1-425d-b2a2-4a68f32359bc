package com.nercar.ingredient.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StandardCompositionsQueryDTO {

    @Schema(description = "标准名称")
    private String standardName;
    /**
     * 元素名称
     */
    @Schema(description = "元素名称")
    private String elementName;

    /**
     * 最小值
     */
    @Schema(description = "最小值")
    private BigDecimal minValue;

    /**
     * 最大值
     */
    @Schema(description = "最大值")
    private BigDecimal maxValue;

    /**
     * 平均值（目标值）
     */
    @Schema(description = "平均值（目标值）")
    private BigDecimal averageValue;

    /**
     * 数学符号
     */
    @Schema(description = "数学符号")
    private String code;

    /**
     * 成分类型
     */
    @Schema(description = "成分类型：内控成分、配料内控")
    private String type;

    /*
     * 是否自定义
     */
    @Schema(description = "是否自定义")
    private String isCustomize;

    /**
     * 标准成分列表
     */
    @Schema(description = "标准成分列表")
    private List<StandardCompositionsDTO> standardCompositionsList;
}