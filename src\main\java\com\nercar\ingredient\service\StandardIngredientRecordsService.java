package com.nercar.ingredient.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.ingredient.domain.dto.*;
import com.nercar.ingredient.domain.po.StandardIngredientRecords;
import com.nercar.ingredient.domain.vo.AllDataVO;
import com.nercar.ingredient.domain.vo.HttpResVo;
import com.nercar.ingredient.domain.vo.StandardIngredientRecordsResultVO;
import com.nercar.ingredient.domain.vo.StandardIngredientRecordsVO;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【standard_ingredient_records(标准配料记录表)】的数据库操作Service
* @createDate 2025-04-01 13:55:28
*/
public interface StandardIngredientRecordsService extends IService<StandardIngredientRecords> {

    IPage<StandardIngredientRecordsVO> selectPage(SteelGradesDTO steelGradesDTO, LambdaQueryWrapper<StandardIngredientRecords> wrapper);

    IPage<StandardIngredientRecordsVO> selectRecords(StandardIngredientRecordsQueryDTO standardIngredientRecordsQueryDTO);

    StandardIngredientRecordsResultVO previewIngredient(Long id);


    IPage<StandardIngredientRecordsVO> selectDraft(StandardIngredientRecordsQueryDTO standardIngredientRecordsQueryDTO);

    void updateIngredientByResultId(IngredientResultDTO ingredientResultDTO);

    AllDataVO getRecords(Long id);

    Long saveOrUpdateMainTable(StandardIngredientRecordsDTO standardIngredientRecordsDTO);


    HttpResVo sendData(Long id);

    void download(Long id, HttpServletResponse response);

    void download(Long id, String calculationSequence, HttpServletResponse response);

    void batchDownload(List<Long> ids, HttpServletResponse response);

    void batchDownloadWithSequences(List<Long> ids, List<String> calculationSequences, HttpServletResponse response);

//    ResponseEntity<String> previewDownload(Long id);
    void previewDownload(Long id, HttpServletResponse response);

    void previewDownload(Long id, String calculationSequence, HttpServletResponse response);

    void updateIngredientByResultIds(IngredientResultIdsDTO ingredientResultIdsDTO);

    boolean updateByNewPrice(Long id);
}
