<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.ProcessPathStepsMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.ProcessPathSteps">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="stepNumber" column="step_number" jdbcType="VARCHAR"/>
            <result property="equipmentId" column="equipment_id" jdbcType="BIGINT"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="pathId" column="path_id" jdbcType="BIGINT"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,step_number,equipment_id,
        description,path_id,createtime,
        updatetime
    </sql>
</mapper>
