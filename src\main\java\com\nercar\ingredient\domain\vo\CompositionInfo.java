package com.nercar.ingredient.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 成分信息类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class CompositionInfo {
    /**
     * 元素
     */
    private String element;
    
    /**
     * 元素重量
     */
    private Double weight;
    
    /**
     * 元素重量百分比
     */
    private Double weightPercent;
}
