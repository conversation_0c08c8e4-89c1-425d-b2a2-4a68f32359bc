package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 成材率
 * @TableName material_yield_rates
 */
@TableName(value ="material_yield_rates")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class MaterialYieldRates extends BaseEntity {
    /**
     * ID
     */
    @TableId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private String device;

    /**
     * 生产车间
     */
    private String productionDept;

    /**
     * 作业线
     */
    private String lineName;

    /**
     * 供料类别
     */
    private String materialCategoryName;

    /**
     * 产品类别
     */
    private String productCategory;

    /**
     * 钢类
     */
    private String steelClass;

    /**
     * 钢号
     */
    private String steelGrade;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 成材率
     */
    private String materialYield;

    /**
     * 不定尺（黑皮）
     */
    private String unfixedLengthBlackSkin;

    /**
     * 不定尺成材率（磨光）
     */
    private String unfixedYieldPolished;

    /**
     * 不定尺成材率（削皮）
     */
    private String unfixedYieldPeeling;

    /**
     * 成材率（定尺+磨光）
     */
    private String fixedYieldPolished;

    /**
     * 成材率（定尺+车光）
     */
    private String fixedYieldLathe;

    /**
     * 成材率（定尺+削皮）
     */
    private String fixedYieldPeeling;

    /**
     * 不定尺磨光车光
     */
    private String unfixedPolishedLathe;

    /**
     * 不定尺磨光削皮
     */
    private String unfixedPolishedPeeling;

    /**
     * 定尺磨光车光
     */
    private String fixedPolishedLathe;

    /**
     * 定尺磨光削皮
     */
    private String fixedPolishedPeeling;

    /**
     * 定尺率
     */
    private String fixedLengthRate;

    /**
     * 磨光率
     */
    private String polishedRate;

    /**
     * 车光率
     */
    private String latheRate;

    /**
     * 削皮率
     */
    private String peelingRate;

    /**
     * 烧损
     */
    private String burningLoss;

    /**
     * 定尺（黑皮）
     */
    private String fixedLengthBlackSkin;

    /**
     * 不定尺成材率（车光）
     */
    private String unfixedYieldLathe;

    /**
     * 成材率（定尺+铣光）
     */
    private String fixedYieldMilling;

    /**
     * 定尺+铣光率
     */
    private String fixedMillingRate;

    /**
     * 成材率（调质）
     */
    private String quenchingYield;

    /**
     * 调质率
     */
    private String quenchingRate;

    /**
     * 成材率（定尺+调质）
     */
    private String fixedQuenchingYield;

    /**
     * 定制单位
     */
    private String specifiedUnit;

    /**
     * 修改内容及原因
     */
    private String modificationReason;

    /**
     * 锭型
     */
    private String ingotType;
}