package com.nercar.ingredient.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.ingredient.domain.dto.ContractReviewQueryDTO;
import com.nercar.ingredient.domain.po.ContractReviewInfoMerged;
import com.nercar.ingredient.domain.vo.ContractReviewInfoVO;
import com.nercar.ingredient.domain.vo.ContractReviewOptionVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【contract_review_info_merged(合同评审信息合并表)】的数据库操作Service
 * @createDate 2025-07-18 15:30:00
 */
public interface ContractReviewInfoMergedService extends IService<ContractReviewInfoMerged> {
    
    /**
     * 根据合同ID查询合同详情
     * @param contractId 合同ID
     * @return 合同详情信息
     */
    ContractReviewInfoVO getContractReviewById(Long contractId);
    
    /**
     * 分页查询合同评审信息
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<ContractReviewInfoVO> getContractReviewPage(ContractReviewQueryDTO queryDTO);

    // ==================== 下拉框数据查询方法 ====================

    /**
     * 获取顾客名称列表（用于下拉框）
     * @return 顾客名称选项列表
     */
    List<ContractReviewOptionVO> getCustomerNameOptions();

    /**
     * 获取钢种列表（用于下拉框）
     * @return 钢种选项列表
     */
    List<ContractReviewOptionVO> getSteelGradeNameOptions();

    /**
     * 获取钢类列表（用于下拉框）
     * @return 钢类选项列表
     */
    List<ContractReviewOptionVO> getSteelTypeNameOptions();
}
