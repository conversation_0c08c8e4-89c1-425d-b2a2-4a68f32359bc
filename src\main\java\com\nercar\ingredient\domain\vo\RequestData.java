package com.nercar.ingredient.domain.vo;

import com.nercar.ingredient.domain.po.CalculationResult;
import com.nercar.ingredient.domain.po.IngredientIdingotResult;
import com.nercar.ingredient.domain.po.IngredientYieldResult;
import com.nercar.ingredient.domain.po.PurposeCompositions;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RequestData extends BaseVO{
    private String costEstimationId;
    private  String id;
    private String  approveUserId;
    private List<PurposeCompositions> purposeCompositionsList;
    private List<CalculationResultVO> calculationResultList;
    private List<IngredientIdingotResult> ingredientIdingotResultList;
    private List<IngredientYieldResult> ingredientYieldResultList;
}
