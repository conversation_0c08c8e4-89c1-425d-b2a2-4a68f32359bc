package com.nercar.ingredient.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.dto.MaterialYieldRatesDTO;
import com.nercar.ingredient.domain.po.IngotYieldRates;
import com.nercar.ingredient.domain.po.IngredientYieldResult;
import com.nercar.ingredient.domain.po.MaterialYieldRates;
import com.nercar.ingredient.domain.vo.MaterialYieldRatesVO;
import com.nercar.ingredient.excel.MaterialYieldRatesExcel;
import com.nercar.ingredient.excel.listener.MaterialYieldRatesImportListener;
import com.nercar.ingredient.mapper.IngredientYieldResultMapper;
import com.nercar.ingredient.service.MaterialYieldRatesService;
import com.nercar.ingredient.mapper.MaterialYieldRatesMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【material_yield_rates(成材率)】的数据库操作Service实现
* @createDate 2025-04-07 15:15:46
*/
@Service
public class MaterialYieldRatesServiceImpl extends ServiceImpl<MaterialYieldRatesMapper, MaterialYieldRates>
    implements MaterialYieldRatesService{

    @Autowired
    private IngredientYieldResultMapper ingredientYieldResultMapper;

    @Override
    public IPage<MaterialYieldRates> selectMaterialYieldRates(MaterialYieldRatesDTO materialYieldRatesDTO) {
        //搜索条件有 productionDept,lineName,materialCategoryName,steelClass,specifications

        LambdaQueryWrapper<MaterialYieldRates> wrapper = new LambdaQueryWrapper<>();
        if( StringUtils.isNotEmpty(materialYieldRatesDTO.getLineName())){
            wrapper.like( MaterialYieldRates::getLineName, materialYieldRatesDTO.getLineName());
        }
        if (StringUtils.isNotEmpty(materialYieldRatesDTO.getProductionDept())) {
            wrapper.like(MaterialYieldRates::getProductionDept, materialYieldRatesDTO.getProductionDept());
        }
        if (StringUtils.isNotEmpty(materialYieldRatesDTO.getMaterialCategoryName())) {
            wrapper.like(MaterialYieldRates::getMaterialCategoryName, materialYieldRatesDTO.getMaterialCategoryName());
        }
        if (StringUtils.isNotEmpty(materialYieldRatesDTO.getSteelClass())) {
            wrapper.like(MaterialYieldRates::getSteelClass, materialYieldRatesDTO.getSteelClass());
        }
        if (StringUtils.isNotEmpty(materialYieldRatesDTO.getSpecifications())) {
            wrapper.like(MaterialYieldRates::getSpecifications, materialYieldRatesDTO.getSpecifications());
        }
        Page<MaterialYieldRates> page = new Page<>(materialYieldRatesDTO.getPageNo(), materialYieldRatesDTO.getPageSize());
        IPage<MaterialYieldRates> materialYieldRatesIPage = baseMapper.selectMaterialYieldRates(page, wrapper);

        return materialYieldRatesIPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> saveOrUpdateMaterialYieldRate(List<MaterialYieldRatesDTO> materialYieldRatesDTOList, String id) {
        List<Long> ids = new ArrayList<>();
        for (MaterialYieldRatesDTO materialYieldRatesDTO : materialYieldRatesDTOList) {
            if (Objects.isNull(materialYieldRatesDTO.getId())) {
                MaterialYieldRates materialYieldRates = MaterialYieldRates.builder()
                   .device(materialYieldRatesDTO.getDevice())
                   .productionDept(materialYieldRatesDTO.getProductionDept())
                    .materialYield(materialYieldRatesDTO.getMaterialYield())
                    .build();
                baseMapper.insert(materialYieldRates);
//                // 然后查询出来
//                MaterialYieldRates one = baseMapper.selectOne(new LambdaQueryWrapper<MaterialYieldRates>()
//                    .eq(MaterialYieldRates::getDevice, materialYieldRatesDTO.getDevice())
//                    .eq(MaterialYieldRates::getMaterialYield, materialYieldRatesDTO.getMaterialYield())
//                    .eq(MaterialYieldRates::getProductionDept, materialYieldRatesDTO.getProductionDept())
//            );
                IngredientYieldResult ingredientYieldResult = new IngredientYieldResult();
                ingredientYieldResult.setStandardIngredientRecordId(Long.parseLong(id));
                ingredientYieldResult.setYieldId(materialYieldRates.getId());
                ingredientYieldResult.setProductionDept(materialYieldRatesDTO.getProductionDept());
                ingredientYieldResult.setLineName(materialYieldRatesDTO.getLineName());
                ingredientYieldResult.setMaterialYield(materialYieldRatesDTO.getMaterialYield());
                ingredientYieldResultMapper.insert(ingredientYieldResult);
                ids.add(materialYieldRates.getId());
            } else {
                MaterialYieldRates materialYieldRates = MaterialYieldRates.builder()
                    .id(materialYieldRatesDTO.getId())
                    .device(materialYieldRatesDTO.getDevice())
                    .productionDept(materialYieldRatesDTO.getProductionDept())
                    .materialYield(materialYieldRatesDTO.getMaterialYield())
                    .build();
                baseMapper.updateById(materialYieldRates);
                IngredientYieldResult ingredientYieldResult = new IngredientYieldResult();
                ingredientYieldResult.setStandardIngredientRecordId(Long.parseLong(id));
                ingredientYieldResult.setYieldId(materialYieldRates.getId());
                ingredientYieldResult.setProductionDept(materialYieldRatesDTO.getProductionDept());
                ingredientYieldResult.setLineName(materialYieldRatesDTO.getLineName());
                ingredientYieldResult.setMaterialYield(materialYieldRatesDTO.getMaterialYield());
                ingredientYieldResultMapper.updateById(ingredientYieldResult);
                ids.add(materialYieldRatesDTO.getId());
            }
        }
        return ids;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcel(MultipartFile file){
        try {
            EasyExcel.read(file.getInputStream(),
                            MaterialYieldRatesExcel.class,
                            new MaterialYieldRatesImportListener(this))
                    .sheet()
                    .doRead();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(Collection<MaterialYieldRates> entityList, int batchSize) {
        if (CollectionUtils.isEmpty(entityList)) {
            return false;
        }
        
        try {
            // 使用 MyBatis-Plus 的批量插入
            return super.saveBatch(entityList, batchSize);
        } catch (Exception e) {
            log.error("批量保存数据失败", e);
            throw new RuntimeException("批量保存数据失败", e);
        }
    }
}
