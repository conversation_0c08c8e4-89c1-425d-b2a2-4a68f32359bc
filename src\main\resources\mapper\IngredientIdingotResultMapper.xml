<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.IngredientIdingotResultMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.IngredientIdingotResult">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="standardIngredientRecordId" column="standard_ingredient_record_id" jdbcType="BIGINT"/>
            <result property="igingotId" column="igingot_id" jdbcType="BIGINT"/>
            <result property="processPath" column="process_path" jdbcType="VARCHAR"/>
            <result property="departmentName" column="department_name" jdbcType="VARCHAR"/>
            <result property="ingotYield" column="ingot_yield" jdbcType="NUMERIC"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,standard_ingredient_record_id,igingot_id,
        process_path,department_name,ingot_yield
    </sql>
</mapper>
