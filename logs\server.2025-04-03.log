00:04:20.343 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 57268 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:04:20.347 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:04:20.348 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
00:04:21.588 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:04:22.244 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
00:04:22.246 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:04:22.247 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:04:22.299 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:04:22.507 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:04:22.544 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@5fa67d8b, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:04:22.657 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:04:22.686 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:04:22.704 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:04:22.724 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:04:22.743 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:04:22.762 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:04:22.777 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:04:22.781 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:04:22.781 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:04:22.790 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:04:22.808 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:04:22.821 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:04:22.833 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:04:22.846 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:04:22.858 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:04:22.869 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:04:22.895 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:04:22.907 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:04:22.917 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:04:22.928 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:04:22.931 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:04:22.932 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:04:22.937 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:04:22.942 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:04:22.942 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:04:22.949 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:04:22.961 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:04:22.972 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:04:23.102 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:04:23.110 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:04:23.116 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:1
00:04:24.231 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
00:04:24.262 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.502 seconds (process running for 5.767)
00:04:24.271 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
00:04:27.810 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:04:28.518 [http-nio-9090-exec-6] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 328 ms
00:04:33.615 [http-nio-9090-exec-10] INFO  c.n.i.c.StandardIngredientRecordsController - [getStandardIngredientRecords,37] - 分页查询标准配料记录
00:04:33.762 [http-nio-9090-exec-10] WARN  c.b.m.e.p.i.PaginationInnerInterceptor - [autoCountSql,356] - optimize this sql to a count sql has exception, sql:"select
            sg.name as steelGrade,
            es.std_name as standardName   id,user_name,department_id,
        steel_grade_id,calculation_process_no,execution_standard_id,
        raw_material_total,cost_price,mixing_date,
        release_date,category,calculation_result_id,
        process_path_id,special_notes,cost_estimattion_id,
        createuser,createtime,updatetime   from standard_ingredient_records as air
        left join steel_grades as sg on air.steel_grade_id = sg.id
        left join execution_standards as es on air.execution_standard_id = es.id
        WHERE (mixing_date = ?)", exception:
java.util.concurrent.ExecutionException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "id" <S_IDENTIFIER>
    at line 3, column 43.

Was expecting one of:

    <EOF>
    <ST_SEMICOLON>

00:04:33.774 [http-nio-9090-exec-10] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:04:34.037 [http-nio-9090-exec-10] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1c25a17a
00:04:34.038 [http-nio-9090-exec-10] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:04:34.045 [http-nio-9090-exec-10] DEBUG c.n.i.m.S.selectRecords_mpCount - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM (select sg.name as steelGrade, es.std_name as standardName id,user_name,department_id, steel_grade_id,calculation_process_no,execution_standard_id, raw_material_total,cost_price,mixing_date, release_date,category,calculation_result_id, process_path_id,special_notes,cost_estimattion_id, createuser,createtime,updatetime from standard_ingredient_records as air left join steel_grades as sg on air.steel_grade_id = sg.id left join execution_standards as es on air.execution_standard_id = es.id WHERE (mixing_date = ?)) TOTAL
00:04:34.058 [http-nio-9090-exec-10] DEBUG c.n.i.m.S.selectRecords_mpCount - [debug,135] - ==> Parameters: 2024-02-01T08:00(LocalDateTime)
00:04:34.155 [http-nio-9090-exec-10] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 
### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 语法错误 在 "id" 或附近的
  位置：107
### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) FROM (select             sg.name as steelGrade,             es.std_name as standardName   id,user_name,department_id,         steel_grade_id,calculation_process_no,execution_standard_id,         raw_material_total,cost_price,mixing_date,         release_date,category,calculation_result_id,         process_path_id,special_notes,cost_estimattion_id,         createuser,createtime,updatetime   from standard_ingredient_records as air         left join steel_grades as sg on air.steel_grade_id = sg.id         left join execution_standards as es on air.execution_standard_id = es.id         WHERE (mixing_date = ?)) TOTAL
### Cause: org.postgresql.util.PSQLException: 错误: 语法错误 在 "id" 或附近的
  位置：107
; bad SQL grammar []
00:04:34.163 [http-nio-9090-exec-10] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.jdbc.BadSqlGrammarException: <EOL><EOL>### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 语法错误 在 "id" 或附近的<EOL>  位置：107<EOL><EOL>### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]<EOL><EOL>### The error may involve defaultParameterMap<EOL><EOL>### The error occurred while setting parameters<EOL><EOL>### SQL: SELECT COUNT(*) FROM (select             sg.name as steelGrade,             es.std_name as standardName   id,user_name,department_id,         steel_grade_id,calculation_process_no,execution_standard_id,         raw_material_total,cost_price,mixing_date,         release_date,category,calculation_result_id,         process_path_id,special_notes,cost_estimattion_id,         createuser,createtime,updatetime   from standard_ingredient_records as air         left join steel_grades as sg on air.steel_grade_id = sg.id         left join execution_standards as es on air.execution_standard_id = es.id         WHERE (mixing_date = ?)) TOTAL<EOL><EOL>### Cause: org.postgresql.util.PSQLException: 错误: 语法错误 在 "id" 或附近的<EOL>  位置：107<EOL>; bad SQL grammar []]
00:05:30.025 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
00:05:30.028 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
00:05:35.997 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 19396 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:05:36.000 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:05:36.001 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
00:05:37.435 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:05:38.130 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
00:05:38.132 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:05:38.133 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:05:38.180 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:05:38.423 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:05:38.466 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@2f2f7643, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:05:38.608 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:05:38.642 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:05:38.664 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:05:38.689 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:05:38.724 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:05:38.760 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:05:38.784 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:05:38.789 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:05:38.790 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:05:38.799 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:05:38.824 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:05:38.841 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:05:38.864 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:05:38.888 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:05:38.925 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:05:38.945 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:05:38.990 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:05:39.010 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:05:39.026 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:05:39.043 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:05:39.047 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:05:39.047 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:05:39.055 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:05:39.060 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:05:39.061 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:05:39.072 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:05:39.087 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:05:39.102 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:05:39.335 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:05:39.346 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:05:39.351 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:23
00:05:41.006 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 5.559 seconds (process running for 6.838)
00:05:41.021 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
00:05:59.464 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 53184 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:05:59.467 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:05:59.468 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
00:06:00.824 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:06:01.855 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
00:06:01.856 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:06:01.856 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:06:01.903 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:06:02.093 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:06:02.130 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@31c810d3, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:06:02.256 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:06:02.288 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:06:02.306 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:06:02.321 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:06:02.340 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:06:02.359 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:06:02.377 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:06:02.382 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:06:02.382 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:06:02.388 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:06:02.401 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:06:02.413 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:06:02.432 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:06:02.456 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:06:02.469 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:06:02.486 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:06:02.518 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:06:02.535 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:06:02.545 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:06:02.556 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:06:02.559 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:06:02.559 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:06:02.565 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:06:02.568 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:06:02.569 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:06:02.573 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:06:02.587 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:06:02.600 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:06:02.751 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:06:02.760 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:06:02.765 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:30
00:06:04.281 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
00:06:04.313 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 5.289 seconds (process running for 7.234)
00:06:04.323 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
00:06:07.548 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:06:07.699 [http-nio-9090-exec-1] INFO  c.n.i.c.StandardIngredientRecordsController - [getStandardIngredientRecords,37] - 分页查询标准配料记录
00:06:07.977 [http-nio-9090-exec-1] WARN  c.b.m.e.p.i.PaginationInnerInterceptor - [autoCountSql,356] - optimize this sql to a count sql has exception, sql:"select
            sg.steel_grade as steelGrade,
            es.standard_name as standardName   id,user_name,department_id,
        steel_grade_id,calculation_process_no,execution_standard_id,
        raw_material_total,cost_price,mixing_date,
        release_date,category,calculation_result_id,
        process_path_id,special_notes,cost_estimattion_id,
        createuser,createtime,updatetime   from standard_ingredient_records as air
        left join steel_grades as sg on air.steel_grade_id = sg.id
        left join execution_standards as es on air.execution_standard_id = es.id
        WHERE (mixing_date = ?)", exception:
java.util.concurrent.ExecutionException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "id" <S_IDENTIFIER>
    at line 3, column 48.

Was expecting one of:

    <EOF>
    <ST_SEMICOLON>

00:06:07.994 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:06:08.171 [http-nio-9090-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@72fca446
00:06:08.173 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:06:08.182 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectRecords_mpCount - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM (select sg.steel_grade as steelGrade, es.standard_name as standardName id,user_name,department_id, steel_grade_id,calculation_process_no,execution_standard_id, raw_material_total,cost_price,mixing_date, release_date,category,calculation_result_id, process_path_id,special_notes,cost_estimattion_id, createuser,createtime,updatetime from standard_ingredient_records as air left join steel_grades as sg on air.steel_grade_id = sg.id left join execution_standards as es on air.execution_standard_id = es.id WHERE (mixing_date = ?)) TOTAL
00:06:08.204 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectRecords_mpCount - [debug,135] - ==> Parameters: 2024-02-01T08:00(LocalDateTime)
00:06:08.363 [http-nio-9090-exec-1] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 
### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 语法错误 在 "id" 或附近的
  位置：119
### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) FROM (select             sg.steel_grade as steelGrade,             es.standard_name as standardName   id,user_name,department_id,         steel_grade_id,calculation_process_no,execution_standard_id,         raw_material_total,cost_price,mixing_date,         release_date,category,calculation_result_id,         process_path_id,special_notes,cost_estimattion_id,         createuser,createtime,updatetime   from standard_ingredient_records as air         left join steel_grades as sg on air.steel_grade_id = sg.id         left join execution_standards as es on air.execution_standard_id = es.id         WHERE (mixing_date = ?)) TOTAL
### Cause: org.postgresql.util.PSQLException: 错误: 语法错误 在 "id" 或附近的
  位置：119
; bad SQL grammar []
00:06:08.389 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.jdbc.BadSqlGrammarException: <EOL><EOL>### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 语法错误 在 "id" 或附近的<EOL>  位置：119<EOL><EOL>### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]<EOL><EOL>### The error may involve defaultParameterMap<EOL><EOL>### The error occurred while setting parameters<EOL><EOL>### SQL: SELECT COUNT(*) FROM (select             sg.steel_grade as steelGrade,             es.standard_name as standardName   id,user_name,department_id,         steel_grade_id,calculation_process_no,execution_standard_id,         raw_material_total,cost_price,mixing_date,         release_date,category,calculation_result_id,         process_path_id,special_notes,cost_estimattion_id,         createuser,createtime,updatetime   from standard_ingredient_records as air         left join steel_grades as sg on air.steel_grade_id = sg.id         left join execution_standards as es on air.execution_standard_id = es.id         WHERE (mixing_date = ?)) TOTAL<EOL><EOL>### Cause: org.postgresql.util.PSQLException: 错误: 语法错误 在 "id" 或附近的<EOL>  位置：119<EOL>; bad SQL grammar []]
00:12:52.928 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
00:12:52.936 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
00:13:02.942 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 52976 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:13:02.944 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:13:02.945 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
00:13:05.795 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:13:06.545 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
00:13:06.547 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:13:06.547 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:13:06.606 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:13:06.851 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:13:06.885 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@59ba77e2, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:13:07.032 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:13:07.065 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:13:07.106 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:13:07.137 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:13:07.167 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:13:07.195 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:13:07.221 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:13:07.247 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:13:07.248 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:13:07.257 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:13:07.295 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:13:07.323 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:13:07.367 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:13:07.402 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:13:07.422 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:13:07.445 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:13:07.474 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:13:07.497 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:13:07.523 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:13:07.552 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:13:07.565 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:13:07.565 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:13:07.577 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:13:07.591 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:13:07.591 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:13:07.600 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:13:07.630 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:13:07.656 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:13:07.810 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:13:07.818 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:13:07.826 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:10
00:13:08.994 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
00:13:09.024 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 6.546 seconds (process running for 14.243)
00:13:09.033 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
00:13:12.419 [http-nio-9090-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:13:13.235 [http-nio-9090-exec-9] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 375 ms
00:13:17.148 [http-nio-9090-exec-8] INFO  c.n.i.c.StandardIngredientRecordsController - [getStandardIngredientRecords,37] - 分页查询标准配料记录
00:13:17.343 [http-nio-9090-exec-8] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:13:17.592 [http-nio-9090-exec-8] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@200e546b
00:13:17.594 [http-nio-9090-exec-8] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:13:17.611 [http-nio-9090-exec-8] DEBUG c.n.i.m.S.selectRecords_mpCount - [debug,135] - ==>  Preparing: SELECT COUNT(*) AS total FROM standard_ingredient_records AS air WHERE (mixing_date = ?)
00:13:17.626 [http-nio-9090-exec-8] DEBUG c.n.i.m.S.selectRecords_mpCount - [debug,135] - ==> Parameters: 2024-02-01T08:00(LocalDateTime)
00:13:17.659 [http-nio-9090-exec-8] DEBUG c.n.i.m.S.selectRecords_mpCount - [debug,135] - <==      Total: 1
00:13:17.670 [http-nio-9090-exec-8] DEBUG c.n.i.m.S.selectRecords - [debug,135] - ==>  Preparing: select air.id, air.user_name, air.department_id, air.steel_grade_id, air.calculation_process_no, air.execution_standard_id, air.raw_material_total, air.cost_price, air.mixing_date, air.release_date, air.category, air.calculation_result_id, air.process_path_id, air.special_notes, air.cost_estimattion_id, air.createuser, air.createtime, air.updatetime, sg.steel_grade as steelGrade, es.standard_name as standardName from standard_ingredient_records as air left join steel_grades as sg on air.steel_grade_id = sg.id left join execution_standards as es on air.execution_standard_id = es.id WHERE (mixing_date = ?) LIMIT ?
00:13:17.671 [http-nio-9090-exec-8] DEBUG c.n.i.m.S.selectRecords - [debug,135] - ==> Parameters: 2024-02-01T08:00(LocalDateTime), 3(Long)
00:13:17.767 [http-nio-9090-exec-8] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 
### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 关系 "execution_standards" 不存在
  位置：790
### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select             air.id,             air.user_name,             air.department_id,             air.steel_grade_id,             air.calculation_process_no,             air.execution_standard_id,             air.raw_material_total,             air.cost_price,             air.mixing_date,             air.release_date,             air.category,             air.calculation_result_id,             air.process_path_id,             air.special_notes,             air.cost_estimattion_id,             air.createuser,             air.createtime,             air.updatetime,             sg.steel_grade as steelGrade,             es.standard_name as standardName         from standard_ingredient_records as air         left join steel_grades as sg on air.steel_grade_id = sg.id         left join execution_standards as es on air.execution_standard_id = es.id         WHERE (mixing_date = ?) LIMIT ?
### Cause: org.postgresql.util.PSQLException: 错误: 关系 "execution_standards" 不存在
  位置：790
; bad SQL grammar []
00:13:17.773 [http-nio-9090-exec-8] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.jdbc.BadSqlGrammarException: <EOL><EOL>### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 关系 "execution_standards" 不存在<EOL>  位置：790<EOL><EOL>### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]<EOL><EOL>### The error may involve defaultParameterMap<EOL><EOL>### The error occurred while setting parameters<EOL><EOL>### SQL: select             air.id,             air.user_name,             air.department_id,             air.steel_grade_id,             air.calculation_process_no,             air.execution_standard_id,             air.raw_material_total,             air.cost_price,             air.mixing_date,             air.release_date,             air.category,             air.calculation_result_id,             air.process_path_id,             air.special_notes,             air.cost_estimattion_id,             air.createuser,             air.createtime,             air.updatetime,             sg.steel_grade as steelGrade,             es.standard_name as standardName         from standard_ingredient_records as air         left join steel_grades as sg on air.steel_grade_id = sg.id         left join execution_standards as es on air.execution_standard_id = es.id         WHERE (mixing_date = ?) LIMIT ?<EOL><EOL>### Cause: org.postgresql.util.PSQLException: 错误: 关系 "execution_standards" 不存在<EOL>  位置：790<EOL>; bad SQL grammar []]
00:13:59.314 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
00:13:59.317 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
00:14:03.159 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 49244 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:14:03.163 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:14:03.165 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
00:14:04.296 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:14:04.993 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
00:14:04.994 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:14:04.995 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:14:05.069 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:14:05.373 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:14:05.419 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@c1f9724, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:14:05.584 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:14:05.617 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:14:05.634 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:14:05.656 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:14:05.672 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:14:05.701 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:14:05.718 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:14:05.723 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:14:05.723 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:14:05.730 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:14:05.746 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:14:05.760 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:14:05.777 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:14:05.796 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:14:05.813 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:14:05.831 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:14:05.870 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:14:05.890 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:14:05.905 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:14:05.920 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:14:05.924 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:14:05.924 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:14:05.932 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:14:05.937 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:14:05.937 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:14:05.943 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:14:05.959 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:14:05.971 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:14:06.113 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:14:06.124 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:14:06.130 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:28
00:14:07.933 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
00:14:07.979 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 5.64 seconds (process running for 6.974)
00:14:07.996 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
00:14:12.083 [http-nio-9090-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:14:12.246 [http-nio-9090-exec-2] INFO  c.n.i.c.StandardIngredientRecordsController - [getStandardIngredientRecords,37] - 分页查询标准配料记录
00:14:12.520 [http-nio-9090-exec-2] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:14:12.716 [http-nio-9090-exec-2] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@70acfd1b
00:14:12.722 [http-nio-9090-exec-2] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:14:12.740 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectRecords_mpCount - [debug,135] - ==>  Preparing: SELECT COUNT(*) AS total FROM standard_ingredient_records AS air WHERE (mixing_date = ?)
00:14:12.779 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectRecords_mpCount - [debug,135] - ==> Parameters: 2024-02-01T08:00(LocalDateTime)
00:14:12.817 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectRecords_mpCount - [debug,135] - <==      Total: 1
00:14:12.836 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectRecords - [debug,135] - ==>  Preparing: select air.id, air.user_name, air.department_id, air.steel_grade_id, air.calculation_process_no, air.execution_standard_id, air.raw_material_total, air.cost_price, air.mixing_date, air.release_date, air.category, air.calculation_result_id, air.process_path_id, air.special_notes, air.cost_estimattion_id, air.createuser, air.createtime, air.updatetime, sg.steel_grade as steelGrade, es.standard_name as standardName from standard_ingredient_records as air left join steel_grades as sg on air.steel_grade_id = sg.id left join execution_standard as es on air.execution_standard_id = es.id WHERE (mixing_date = ?) LIMIT ?
00:14:12.839 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectRecords - [debug,135] - ==> Parameters: 2024-02-01T08:00(LocalDateTime), 3(Long)
00:14:12.849 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectRecords - [debug,135] - <==      Total: 3
00:26:34.691 [Thread-5] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
00:26:34.915 [Thread-5] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
00:26:34.922 [Thread-5] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
00:26:35.110 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 49244 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:26:35.110 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:26:35.110 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
00:26:36.077 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
00:26:36.077 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:26:36.078 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:26:36.102 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:26:36.214 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:26:36.242 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@46748b1, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:26:36.253 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - [refresh,633] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'ingredientCalculationController': Unsatisfied dependency expressed through field 'steelGradesService': Error creating bean with name 'steelGradesServiceImpl': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'steelGradesMapper' defined in file [E:\fushun\fushun-ingredient\target\classes\com\nercar\ingredient\mapper\SteelGradesMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:26:36.256 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
00:26:36.314 [restartedMain] ERROR o.s.b.SpringApplication - [reportFailure,859] - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'ingredientCalculationController': Unsatisfied dependency expressed through field 'steelGradesService': Error creating bean with name 'steelGradesServiceImpl': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'steelGradesMapper' defined in file [E:\fushun\fushun-ingredient\target\classes\com\nercar\ingredient\mapper\SteelGradesMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.nercar.ingredient.Application.main(Application.java:16)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'steelGradesServiceImpl': Unsatisfied dependency expressed through field 'baseMapper': Error creating bean with name 'steelGradesMapper' defined in file [E:\fushun\fushun-ingredient\target\classes\com\nercar\ingredient\mapper\SteelGradesMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1448)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1358)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 25 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'steelGradesMapper' defined in file [E:\fushun\fushun-ingredient\target\classes\com\nercar\ingredient\mapper\SteelGradesMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1536)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1430)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1448)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1358)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 39 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:648)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:636)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1448)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1358)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1521)
	... 50 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:178)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:644)
	... 63 common frames omitted
Caused by: java.io.IOException: Failed to parse mapping resource: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:670)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.afterPropertiesSet(MybatisSqlSessionFactoryBean.java:543)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.getObject(MybatisSqlSessionFactoryBean.java:701)
	at com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(MybatisPlusAutoConfiguration.java:213)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:146)
	... 64 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.nercar.ingredient.domain.po.CalculationResult'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.nercar.ingredient.domain.po.CalculationResult
	at com.baomidou.mybatisplus.core.MybatisXMLMapperBuilder.configurationElement(MybatisXMLMapperBuilder.java:129)
	at com.baomidou.mybatisplus.core.MybatisXMLMapperBuilder.parse(MybatisXMLMapperBuilder.java:102)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:668)
	... 72 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.nercar.ingredient.domain.po.CalculationResult'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.nercar.ingredient.domain.po.CalculationResult
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:103)
	at com.baomidou.mybatisplus.core.MybatisXMLMapperBuilder.resultMapElement(MybatisXMLMapperBuilder.java:226)
	at com.baomidou.mybatisplus.core.MybatisXMLMapperBuilder.resultMapElement(MybatisXMLMapperBuilder.java:218)
	at com.baomidou.mybatisplus.core.MybatisXMLMapperBuilder.resultMapElements(MybatisXMLMapperBuilder.java:210)
	at com.baomidou.mybatisplus.core.MybatisXMLMapperBuilder.configurationElement(MybatisXMLMapperBuilder.java:125)
	... 74 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.nercar.ingredient.domain.po.CalculationResult'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.nercar.ingredient.domain.po.CalculationResult
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:128)
	at org.apache.ibatis.builder.BaseBuilder.resolveAlias(BaseBuilder.java:132)
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:101)
	... 78 common frames omitted
Caused by: java.lang.ClassNotFoundException: Cannot find class: com.nercar.ingredient.domain.po.CalculationResult
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:226)
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:103)
	at org.apache.ibatis.io.Resources.classForName(Resources.java:322)
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:124)
	... 80 common frames omitted
00:26:38.945 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 49244 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:26:38.946 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:26:38.946 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
00:26:39.488 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
00:26:39.488 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:26:39.488 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:26:39.504 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:26:39.566 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:26:39.582 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@28ece64b, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:26:39.603 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:26:39.624 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:26:39.634 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:26:39.646 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:26:39.657 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:26:39.667 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:26:39.676 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:26:39.679 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:26:39.679 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:26:39.683 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:26:39.695 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:26:39.705 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:26:39.716 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:26:39.731 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:26:39.742 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:26:39.755 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:26:39.770 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:26:39.782 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:26:39.791 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:26:39.800 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:26:39.803 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:26:39.804 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:26:39.807 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:26:39.811 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:26:39.811 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:26:39.816 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:26:39.825 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:26:39.832 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:26:39.961 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:26:39.970 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:26:39.976 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:28
00:26:40.415 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
00:26:40.423 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 1.514 seconds (process running for 759.417)
00:26:40.429 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
09:08:47.602 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 26148 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
09:08:47.605 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
09:08:47.606 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
09:08:50.049 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:08:50.550 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
09:08:50.551 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:08:50.552 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
09:08:50.594 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:08:50.773 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:08:50.819 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@6f18a3dc, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
09:08:50.926 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
09:08:50.961 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
09:08:50.991 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
09:08:51.025 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
09:08:51.064 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
09:08:51.094 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
09:08:51.119 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
09:08:51.129 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
09:08:51.130 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:08:51.137 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
09:08:51.162 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
09:08:51.185 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
09:08:51.213 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
09:08:51.247 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
09:08:51.279 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
09:08:51.305 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
09:08:51.336 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
09:08:51.362 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
09:08:51.382 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
09:08:51.403 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
09:08:51.413 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
09:08:51.413 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:08:51.422 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
09:08:51.433 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
09:08:51.434 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:08:51.442 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
09:08:51.466 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
09:08:51.494 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
09:08:51.622 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
09:08:51.631 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
09:08:51.637 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:0
09:08:52.816 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
09:08:52.845 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 5.632 seconds (process running for 8.705)
09:08:52.855 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
09:19:51.100 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 32900 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
09:19:51.103 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
09:19:51.104 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
09:19:52.098 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:19:52.597 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
09:19:52.598 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:19:52.599 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
09:19:52.647 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:19:52.827 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:19:52.879 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@63c3f9ca, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
09:19:52.986 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
09:19:53.012 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
09:19:53.031 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
09:19:53.052 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
09:19:53.070 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
09:19:53.091 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
09:19:53.110 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
09:19:53.116 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
09:19:53.117 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:19:53.124 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
09:19:53.139 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
09:19:53.152 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
09:19:53.165 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
09:19:53.176 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
09:19:53.188 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
09:19:53.201 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
09:19:53.219 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
09:19:53.252 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
09:19:53.269 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
09:19:53.289 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
09:19:53.293 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
09:19:53.294 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:19:53.304 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
09:19:53.308 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
09:19:53.308 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:19:53.316 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
09:19:53.335 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
09:19:53.349 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
09:19:53.487 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
09:19:53.497 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
09:19:53.503 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:9
09:19:54.668 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
09:19:54.696 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.984 seconds (process running for 4.93)
09:19:54.705 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
09:19:59.643 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:20:00.382 [http-nio-9090-exec-10] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 319 ms
09:31:52.793 [Thread-5] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
09:31:53.036 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 32900 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
09:31:53.037 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
09:31:53.038 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
09:31:53.541 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
09:31:53.542 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:31:53.542 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
09:31:53.564 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:31:53.627 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:31:53.647 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@2e8410f7, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
09:31:53.664 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
09:31:53.679 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
09:31:53.692 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
09:31:53.700 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
09:31:53.714 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
09:31:53.725 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
09:31:53.733 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
09:31:53.737 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
09:31:53.738 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:31:53.743 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
09:31:53.758 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
09:31:53.774 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
09:31:53.791 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
09:31:53.806 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
09:31:53.817 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
09:31:53.830 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
09:31:53.848 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
09:31:53.858 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
09:31:53.867 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
09:31:53.876 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
09:31:53.880 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
09:31:53.881 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:31:53.884 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
09:31:53.887 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
09:31:53.887 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:31:53.890 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
09:31:53.899 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
09:31:53.907 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
09:31:54.053 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
09:31:54.066 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
09:31:54.074 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:9
09:31:54.645 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
09:31:54.655 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 1.667 seconds (process running for 724.889)
09:31:54.662 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
09:31:56.039 [Thread-7] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
09:31:56.223 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 32900 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
09:31:56.223 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
09:31:56.223 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
09:31:56.628 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
09:31:56.629 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:31:56.629 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
09:31:56.647 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:31:56.708 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:31:56.728 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@110fa11d, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
09:31:56.741 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
09:31:56.750 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
09:31:56.758 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
09:31:56.767 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
09:31:56.775 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
09:31:56.787 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
09:31:56.797 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
09:31:56.801 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
09:31:56.801 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:31:56.805 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
09:31:56.815 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
09:31:56.825 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
09:31:56.837 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
09:31:56.849 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
09:31:56.863 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
09:31:56.876 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
09:31:56.892 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
09:31:56.904 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
09:31:56.916 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
09:31:56.931 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
09:31:56.936 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
09:31:56.936 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:31:56.944 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
09:31:56.948 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
09:31:56.949 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:31:56.956 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
09:31:56.967 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
09:31:56.979 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
09:31:57.136 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
09:31:57.147 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
09:31:57.153 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:9
09:31:57.809 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 1.613 seconds (process running for 728.043)
09:31:57.810 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
11:25:54.026 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 54572 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
11:25:54.028 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
11:25:54.028 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
11:25:55.256 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
11:25:55.979 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
11:25:55.981 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:25:55.981 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
11:25:56.025 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:25:56.209 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
11:25:56.247 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@127c2460, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
11:25:56.367 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
11:25:56.409 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
11:25:56.433 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
11:25:56.459 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
11:25:56.485 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
11:25:56.509 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
11:25:56.533 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
11:25:56.540 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
11:25:56.540 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:25:56.552 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
11:25:56.579 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
11:25:56.605 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
11:25:56.635 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
11:25:56.660 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
11:25:56.684 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
11:25:56.708 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
11:25:56.748 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
11:25:56.761 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
11:25:56.774 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
11:25:56.786 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
11:25:56.790 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
11:25:56.791 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:25:56.797 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
11:25:56.801 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
11:25:56.801 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:25:56.807 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
11:25:56.820 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
11:25:56.834 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
11:25:56.991 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
11:25:57.003 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
11:25:57.009 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:0
11:25:57.136 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - [refresh,633] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'ingredientCalculationController': Unsatisfied dependency expressed through field 'purposeCompositionsService': Error creating bean with name 'purposeCompositionsServiceImpl': Unsatisfied dependency expressed through field 'standardIngredientRecordsService': Error creating bean with name 'standardIngredientRecordsServiceImpl': Unsatisfied dependency expressed through field 'purposeCompositionsService': Error creating bean with name 'purposeCompositionsServiceImpl': Requested bean is currently in creation: Is there an unresolvable circular reference?
11:25:57.138 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
11:25:57.167 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - [report,40] - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The dependencies of some of the beans in the application context form a cycle:

   ingredientCalculationController (field private com.nercar.ingredient.service.PurposeCompositionsService com.nercar.ingredient.controller.IngredientCalculationController.purposeCompositionsService)
┌─────┐
|  purposeCompositionsServiceImpl (field private com.nercar.ingredient.service.StandardIngredientRecordsService com.nercar.ingredient.service.impl.PurposeCompositionsServiceImpl.standardIngredientRecordsService)
↑     ↓
|  standardIngredientRecordsServiceImpl (field private com.nercar.ingredient.service.PurposeCompositionsService com.nercar.ingredient.service.impl.StandardIngredientRecordsServiceImpl.purposeCompositionsService)
└─────┘


Action:

Relying upon circular references is discouraged and they are prohibited by default. Update your application to remove the dependency cycle between beans. As a last resort, it may be possible to break the cycle automatically by setting spring.main.allow-circular-references to true.

11:52:22.388 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 42956 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
11:52:22.390 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
11:52:22.391 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
11:52:23.424 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
11:52:23.935 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
11:52:23.936 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:52:23.936 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
11:52:23.981 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:52:24.151 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
11:52:24.195 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@53a037fc, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
11:52:24.294 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
11:52:24.323 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
11:52:24.343 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
11:52:24.356 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
11:52:24.375 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
11:52:24.395 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
11:52:24.414 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
11:52:24.419 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
11:52:24.420 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:52:24.431 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
11:52:24.453 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
11:52:24.469 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
11:52:24.486 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
11:52:24.503 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
11:52:24.516 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
11:52:24.530 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
11:52:24.554 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
11:52:24.573 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
11:52:24.595 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
11:52:24.614 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
11:52:24.618 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
11:52:24.619 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:52:24.628 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
11:52:24.635 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
11:52:24.636 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:52:24.645 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
11:52:24.663 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
11:52:24.678 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
11:52:24.805 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
11:52:24.815 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
11:52:24.820 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:11
11:52:24.930 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - [refresh,633] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'ingredientCalculationController': Unsatisfied dependency expressed through field 'purposeCompositionsService': Error creating bean with name 'purposeCompositionsServiceImpl': Unsatisfied dependency expressed through field 'commonService': Error creating bean with name 'commonServiceImpl': Unsatisfied dependency expressed through field 'standardIngredientRecordsService': Error creating bean with name 'standardIngredientRecordsServiceImpl': Unsatisfied dependency expressed through field 'purposeCompositionsService': Error creating bean with name 'purposeCompositionsServiceImpl': Requested bean is currently in creation: Is there an unresolvable circular reference?
11:52:24.933 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
11:52:24.961 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - [report,40] - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The dependencies of some of the beans in the application context form a cycle:

   ingredientCalculationController (field private com.nercar.ingredient.service.PurposeCompositionsService com.nercar.ingredient.controller.IngredientCalculationController.purposeCompositionsService)
┌─────┐
|  purposeCompositionsServiceImpl (field private com.nercar.ingredient.service.CommonService com.nercar.ingredient.service.impl.PurposeCompositionsServiceImpl.commonService)
↑     ↓
|  commonServiceImpl (field com.nercar.ingredient.service.StandardIngredientRecordsService com.nercar.ingredient.service.impl.CommonServiceImpl.standardIngredientRecordsService)
↑     ↓
|  standardIngredientRecordsServiceImpl (field private com.nercar.ingredient.service.PurposeCompositionsService com.nercar.ingredient.service.impl.StandardIngredientRecordsServiceImpl.purposeCompositionsService)
└─────┘


Action:

Relying upon circular references is discouraged and they are prohibited by default. Update your application to remove the dependency cycle between beans. As a last resort, it may be possible to break the cycle automatically by setting spring.main.allow-circular-references to true.

13:41:01.871 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 46288 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
13:41:01.898 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
13:41:01.898 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
13:41:06.324 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
13:41:07.566 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
13:41:07.568 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:41:07.568 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
13:41:07.709 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:41:08.160 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
13:41:08.202 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@4684ad5, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
13:41:08.530 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
13:41:08.562 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
13:41:08.611 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
13:41:08.632 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
13:41:08.655 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
13:41:08.680 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
13:41:08.704 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
13:41:08.715 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
13:41:08.716 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
13:41:08.724 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
13:41:08.753 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
13:41:08.775 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
13:41:08.802 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
13:41:08.832 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
13:41:08.866 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
13:41:08.886 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
13:41:08.930 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
13:41:08.953 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
13:41:08.988 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
13:41:09.017 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
13:41:09.030 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
13:41:09.030 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
13:41:09.039 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
13:41:09.045 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
13:41:09.046 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
13:41:09.054 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
13:41:09.088 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
13:41:09.114 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
13:41:09.263 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
13:41:09.273 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
13:41:09.280 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:31
13:41:11.419 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
13:41:11.457 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 10.605 seconds (process running for 13.182)
13:41:11.519 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
13:43:23.695 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 38592 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
13:43:23.697 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
13:43:23.698 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
13:43:24.752 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
13:43:25.259 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
13:43:25.261 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:43:25.261 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
13:43:25.313 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:43:25.516 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
13:43:25.560 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@276c7e56, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
13:43:25.662 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
13:43:25.694 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
13:43:25.709 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
13:43:25.727 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
13:43:25.749 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
13:43:25.766 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
13:43:25.785 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
13:43:25.789 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
13:43:25.790 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
13:43:25.799 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
13:43:25.818 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
13:43:25.833 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
13:43:25.850 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
13:43:25.865 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
13:43:25.878 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
13:43:25.891 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
13:43:25.912 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
13:43:25.926 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
13:43:25.941 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
13:43:25.956 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
13:43:25.961 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
13:43:25.961 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
13:43:25.969 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
13:43:25.974 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
13:43:25.974 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
13:43:25.981 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
13:43:25.997 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
13:43:26.010 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
13:43:26.137 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
13:43:26.146 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
13:43:26.151 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:24
13:43:27.237 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
13:43:27.268 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.949 seconds (process running for 4.904)
13:43:27.277 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
13:44:05.939 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 39076 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
13:44:05.942 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
13:44:05.943 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
13:44:07.050 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
13:44:07.572 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
13:44:07.574 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:44:07.574 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
13:44:07.622 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:44:07.805 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
13:44:07.851 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@56027e61, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
13:44:07.959 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
13:44:07.988 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
13:44:08.004 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
13:44:08.022 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
13:44:08.038 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
13:44:08.059 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
13:44:08.079 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
13:44:08.085 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
13:44:08.085 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
13:44:08.094 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
13:44:08.110 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
13:44:08.123 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
13:44:08.137 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
13:44:08.150 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
13:44:08.162 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
13:44:08.176 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
13:44:08.197 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
13:44:08.212 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
13:44:08.226 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
13:44:08.240 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
13:44:08.243 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
13:44:08.244 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
13:44:08.252 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
13:44:08.255 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
13:44:08.256 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
13:44:08.262 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
13:44:08.279 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
13:44:08.292 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
13:44:08.424 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
13:44:08.433 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
13:44:08.438 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:24
13:44:09.718 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
13:44:09.748 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.255 seconds (process running for 5.193)
13:44:09.760 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
13:44:31.542 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:44:33.229 [http-nio-9090-exec-9] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 500 ms
13:58:02.646 [http-nio-9090-exec-4] INFO  c.n.i.c.StandardIngredientRecordsController - [previewIngredient,50] - 预览配料信息
13:58:02.755 [http-nio-9090-exec-4] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
13:58:03.262 [http-nio-9090-exec-4] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4477941a
13:58:03.263 [http-nio-9090-exec-4] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
13:58:03.271 [http-nio-9090-exec-4] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
13:58:03.308 [http-nio-9090-exec-4] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
13:58:03.326 [http-nio-9090-exec-4] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
13:58:03.533 [http-nio-9090-exec-4] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (id = ?)
13:58:03.533 [http-nio-9090-exec-4] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
13:58:03.535 [http-nio-9090-exec-4] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
13:58:03.800 [http-nio-9090-exec-4] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from standard_raw_materials as srm left join calculation_result as cr on srm.id = cr.standard_raw_materials_id WHERE (id = ?)
13:58:03.800 [http-nio-9090-exec-4] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long)
13:58:04.138 [http-nio-9090-exec-4] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 
### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 字段 cr.standard_raw_materials_id 不存在
  位置：230
### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select             srm.name,             srm.composition,             srm.yield_rate,             srm.price,             cr.wieght         from standard_raw_materials as srm         left join calculation_result as cr on srm.id = cr.standard_raw_materials_id         WHERE (id = ?)
### Cause: org.postgresql.util.PSQLException: 错误: 字段 cr.standard_raw_materials_id 不存在
  位置：230
; bad SQL grammar []
13:58:04.155 [http-nio-9090-exec-4] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.jdbc.BadSqlGrammarException: <EOL><EOL>### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 字段 cr.standard_raw_materials_id 不存在<EOL>  位置：230<EOL><EOL>### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]<EOL><EOL>### The error may involve defaultParameterMap<EOL><EOL>### The error occurred while setting parameters<EOL><EOL>### SQL: select             srm.name,             srm.composition,             srm.yield_rate,             srm.price,             cr.wieght         from standard_raw_materials as srm         left join calculation_result as cr on srm.id = cr.standard_raw_materials_id         WHERE (id = ?)<EOL><EOL>### Cause: org.postgresql.util.PSQLException: 错误: 字段 cr.standard_raw_materials_id 不存在<EOL>  位置：230<EOL>; bad SQL grammar []]
13:59:06.779 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
13:59:06.783 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
13:59:09.727 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 32156 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
13:59:09.730 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
13:59:09.731 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
13:59:10.662 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
13:59:11.204 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
13:59:11.205 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:59:11.205 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
13:59:11.253 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:59:11.438 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
13:59:11.487 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@29bd36da, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
13:59:11.594 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
13:59:11.617 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
13:59:11.630 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
13:59:11.649 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
13:59:11.670 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
13:59:11.691 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
13:59:11.708 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
13:59:11.712 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
13:59:11.712 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
13:59:11.720 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
13:59:11.740 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
13:59:11.755 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
13:59:11.769 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
13:59:11.784 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
13:59:11.797 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
13:59:11.807 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
13:59:11.835 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
13:59:11.848 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
13:59:11.859 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
13:59:11.876 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
13:59:11.881 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
13:59:11.882 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
13:59:11.887 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
13:59:11.892 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
13:59:11.892 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
13:59:11.898 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
13:59:11.913 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
13:59:11.926 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
13:59:12.055 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
13:59:12.063 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
13:59:12.068 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:2
13:59:13.180 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
13:59:13.207 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.846 seconds (process running for 5.032)
13:59:13.216 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
13:59:16.345 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:59:16.382 [http-nio-9090-exec-1] INFO  c.n.i.c.StandardIngredientRecordsController - [previewIngredient,50] - 预览配料信息
13:59:16.402 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
13:59:16.545 [http-nio-9090-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7e2dba61
13:59:16.546 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
13:59:16.554 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
13:59:16.572 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
13:59:16.595 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
13:59:16.670 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (id = ?)
13:59:16.671 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
13:59:16.674 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
13:59:16.734 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from standard_raw_materials as srm left join calculation_result as cr on srm.id = cr.raw_material_id WHERE (id = ?)
13:59:16.734 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long)
13:59:16.846 [http-nio-9090-exec-1] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 
### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 字段关联 "id" 是不明确的
  位置：264
### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select             srm.name,             srm.composition,             srm.yield_rate,             srm.price,             cr.wieght         from standard_raw_materials as srm         left join calculation_result as cr on srm.id = cr.raw_material_id         WHERE (id = ?)
### Cause: org.postgresql.util.PSQLException: 错误: 字段关联 "id" 是不明确的
  位置：264
; bad SQL grammar []
13:59:16.918 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.jdbc.BadSqlGrammarException: <EOL><EOL>### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 字段关联 "id" 是不明确的<EOL>  位置：264<EOL><EOL>### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]<EOL><EOL>### The error may involve defaultParameterMap<EOL><EOL>### The error occurred while setting parameters<EOL><EOL>### SQL: select             srm.name,             srm.composition,             srm.yield_rate,             srm.price,             cr.wieght         from standard_raw_materials as srm         left join calculation_result as cr on srm.id = cr.raw_material_id         WHERE (id = ?)<EOL><EOL>### Cause: org.postgresql.util.PSQLException: 错误: 字段关联 "id" 是不明确的<EOL>  位置：264<EOL>; bad SQL grammar []]
14:04:45.340 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
14:04:45.343 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
14:04:48.232 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 27520 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:04:48.234 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:04:48.235 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:04:49.161 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:04:49.645 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:04:49.646 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:04:49.647 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:04:49.692 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:04:49.871 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:04:49.913 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@29a2e4f7, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:04:50.014 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:04:50.040 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:04:50.054 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:04:50.067 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:04:50.081 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:04:50.104 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:04:50.117 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:04:50.121 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:04:50.121 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:04:50.129 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:04:50.147 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:04:50.160 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:04:50.174 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:04:50.187 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:04:50.200 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:04:50.211 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:04:50.238 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:04:50.249 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:04:50.259 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:04:50.270 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:04:50.272 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:04:50.272 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:04:50.276 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:04:50.279 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:04:50.280 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:04:50.285 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:04:50.298 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:04:50.311 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:04:50.439 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:04:50.448 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:04:50.453 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:29
14:04:51.611 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:04:51.639 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.766 seconds (process running for 4.899)
14:04:51.648 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:04:55.171 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:04:55.206 [http-nio-9090-exec-1] INFO  c.n.i.c.StandardIngredientRecordsController - [previewIngredient,50] - 预览配料信息
14:04:55.226 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
14:04:55.403 [http-nio-9090-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7e2dba61
14:04:55.405 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
14:04:55.415 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
14:04:55.433 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
14:04:55.453 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
14:04:55.537 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (id = ?)
14:04:55.537 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
14:04:55.541 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
14:04:55.595 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from calucation_result as cr left join standard_raw_materials as srm on cr.raw_material_id = srm.id WHERE (id = ?)
14:04:55.596 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long)
14:04:55.700 [http-nio-9090-exec-1] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 
### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 关系 "calucation_result" 不存在
  位置：145
### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select             srm.name,             srm.composition,             srm.yield_rate,             srm.price,             cr.wieght         from calucation_result as cr         left join standard_raw_materials as srm on cr.raw_material_id = srm.id         WHERE (id = ?)
### Cause: org.postgresql.util.PSQLException: 错误: 关系 "calucation_result" 不存在
  位置：145
; bad SQL grammar []
14:04:55.769 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.jdbc.BadSqlGrammarException: <EOL><EOL>### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 关系 "calucation_result" 不存在<EOL>  位置：145<EOL><EOL>### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]<EOL><EOL>### The error may involve defaultParameterMap<EOL><EOL>### The error occurred while setting parameters<EOL><EOL>### SQL: select             srm.name,             srm.composition,             srm.yield_rate,             srm.price,             cr.wieght         from calucation_result as cr         left join standard_raw_materials as srm on cr.raw_material_id = srm.id         WHERE (id = ?)<EOL><EOL>### Cause: org.postgresql.util.PSQLException: 错误: 关系 "calucation_result" 不存在<EOL>  位置：145<EOL>; bad SQL grammar []]
14:05:58.896 [Thread-5] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
14:05:59.001 [Thread-5] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
14:05:59.004 [Thread-5] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
14:05:59.128 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 27520 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:05:59.128 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:05:59.128 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:05:59.495 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:05:59.495 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:05:59.496 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:05:59.519 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:05:59.582 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:05:59.618 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@22666c20, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:05:59.656 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:05:59.694 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:05:59.725 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:05:59.750 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:05:59.780 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:05:59.816 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:05:59.841 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:05:59.846 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:05:59.847 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:05:59.858 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:05:59.879 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:05:59.896 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:05:59.915 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:05:59.926 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:05:59.939 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:05:59.950 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:05:59.975 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:05:59.984 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:05:59.992 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:06:00.005 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:06:00.009 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:06:00.010 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:06:00.016 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:06:00.021 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:06:00.021 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:06:00.028 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:06:00.041 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:06:00.054 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:06:00.266 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:06:00.277 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:06:00.288 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:29
14:06:01.231 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:06:01.244 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 2.147 seconds (process running for 74.504)
14:06:01.247 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:06:05.266 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 48660 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:06:05.269 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:06:05.270 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:06:06.217 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:06:06.717 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:06:06.718 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:06:06.719 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:06:06.765 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:06:06.941 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:06:06.980 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@5ce3273d, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:06:07.087 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:06:07.112 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:06:07.128 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:06:07.142 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:06:07.160 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:06:07.175 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:06:07.188 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:06:07.193 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:06:07.193 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:06:07.201 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:06:07.217 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:06:07.229 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:06:07.244 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:06:07.262 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:06:07.274 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:06:07.285 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:06:07.304 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:06:07.316 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:06:07.325 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:06:07.338 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:06:07.340 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:06:07.341 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:06:07.350 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:06:07.353 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:06:07.354 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:06:07.359 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:06:07.370 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:06:07.381 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:06:07.515 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:06:07.523 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:06:07.528 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:27
14:06:08.636 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:06:08.662 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.792 seconds (process running for 5.053)
14:06:08.672 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:06:12.232 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:06:12.264 [http-nio-9090-exec-1] INFO  c.n.i.c.StandardIngredientRecordsController - [previewIngredient,50] - 预览配料信息
14:06:12.282 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
14:06:12.425 [http-nio-9090-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2a257319
14:06:12.426 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
14:06:12.437 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
14:06:12.457 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
14:06:12.495 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
14:06:12.588 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (id = ?)
14:06:12.589 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
14:06:12.591 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
14:06:12.658 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from calculation_result as cr left join standard_raw_materials as srm on cr.raw_material_id = srm.id WHERE (id = ?)
14:06:12.658 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long)
14:06:12.776 [http-nio-9090-exec-1] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 
### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 字段关联 "id" 是不明确的
  位置：264
### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select             srm.name,             srm.composition,             srm.yield_rate,             srm.price,             cr.wieght         from calculation_result as cr         left join standard_raw_materials as srm on cr.raw_material_id = srm.id         WHERE (id = ?)
### Cause: org.postgresql.util.PSQLException: 错误: 字段关联 "id" 是不明确的
  位置：264
; bad SQL grammar []
14:06:12.835 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.jdbc.BadSqlGrammarException: <EOL><EOL>### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 字段关联 "id" 是不明确的<EOL>  位置：264<EOL><EOL>### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]<EOL><EOL>### The error may involve defaultParameterMap<EOL><EOL>### The error occurred while setting parameters<EOL><EOL>### SQL: select             srm.name,             srm.composition,             srm.yield_rate,             srm.price,             cr.wieght         from calculation_result as cr         left join standard_raw_materials as srm on cr.raw_material_id = srm.id         WHERE (id = ?)<EOL><EOL>### Cause: org.postgresql.util.PSQLException: 错误: 字段关联 "id" 是不明确的<EOL>  位置：264<EOL>; bad SQL grammar []]
14:07:22.167 [Thread-5] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
14:07:22.302 [Thread-5] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
14:07:22.306 [Thread-5] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
14:07:22.405 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 48660 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:07:22.406 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:07:22.406 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:07:22.865 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:07:22.865 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:07:22.865 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:07:22.881 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:07:22.924 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:07:22.943 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@1f42ae37, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:07:22.960 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:07:22.976 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:07:22.987 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:07:22.998 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:07:23.011 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:07:23.020 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:07:23.029 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:07:23.031 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:07:23.031 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:07:23.036 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:07:23.048 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:07:23.056 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:07:23.064 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:07:23.076 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:07:23.086 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:07:23.096 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:07:23.117 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:07:23.126 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:07:23.134 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:07:23.144 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:07:23.146 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:07:23.146 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:07:23.150 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:07:23.152 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:07:23.152 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:07:23.156 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:07:23.164 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:07:23.171 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:07:23.297 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:07:23.305 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:07:23.312 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:27
14:07:23.794 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:07:23.804 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 1.432 seconds (process running for 80.194)
14:07:23.807 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:08:32.209 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 53572 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:08:32.211 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:08:32.212 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:08:33.213 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:08:33.723 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:08:33.724 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:08:33.724 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:08:33.772 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:08:33.954 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:08:34.000 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@32ef8f4c, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:08:34.115 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:08:34.145 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:08:34.163 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:08:34.182 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:08:34.203 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:08:34.219 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:08:34.235 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:08:34.238 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:08:34.239 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:08:34.246 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:08:34.262 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:08:34.274 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:08:34.288 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:08:34.306 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:08:34.317 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:08:34.329 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:08:34.350 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:08:34.363 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:08:34.377 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:08:34.393 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:08:34.397 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:08:34.397 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:08:34.409 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:08:34.412 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:08:34.413 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:08:34.422 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:08:34.443 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:08:34.460 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:08:34.594 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:08:34.602 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:08:34.608 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:1
14:08:35.750 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:08:35.779 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.935 seconds (process running for 4.9)
14:08:35.788 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:08:40.535 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:08:40.577 [http-nio-9090-exec-1] INFO  c.n.i.c.StandardIngredientRecordsController - [previewIngredient,50] - 预览配料信息
14:08:40.600 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
14:08:40.769 [http-nio-9090-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4146727f
14:08:40.771 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
14:08:40.778 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
14:08:40.795 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
14:08:40.814 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
14:08:40.892 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (id = ?)
14:08:40.892 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
14:08:40.895 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
14:08:40.957 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select cr.id, srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from calculation_result as cr left join standard_raw_materials as srm on cr.raw_material_id = srm.id WHERE (id = ?)
14:08:40.958 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long)
14:08:41.076 [http-nio-9090-exec-1] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 
### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 字段关联 "id" 是不明确的
  位置：283
### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select             cr.id,             srm.name,             srm.composition,             srm.yield_rate,             srm.price,             cr.wieght         from calculation_result as cr         left join standard_raw_materials as srm on cr.raw_material_id = srm.id         WHERE (id = ?)
### Cause: org.postgresql.util.PSQLException: 错误: 字段关联 "id" 是不明确的
  位置：283
; bad SQL grammar []
14:08:41.150 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.jdbc.BadSqlGrammarException: <EOL><EOL>### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 字段关联 "id" 是不明确的<EOL>  位置：283<EOL><EOL>### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]<EOL><EOL>### The error may involve defaultParameterMap<EOL><EOL>### The error occurred while setting parameters<EOL><EOL>### SQL: select             cr.id,             srm.name,             srm.composition,             srm.yield_rate,             srm.price,             cr.wieght         from calculation_result as cr         left join standard_raw_materials as srm on cr.raw_material_id = srm.id         WHERE (id = ?)<EOL><EOL>### Cause: org.postgresql.util.PSQLException: 错误: 字段关联 "id" 是不明确的<EOL>  位置：283<EOL>; bad SQL grammar []]
14:10:02.760 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
14:10:02.763 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
14:10:05.466 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 39476 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:10:05.468 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:10:05.469 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:10:06.473 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:10:06.964 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:10:06.966 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:10:06.966 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:10:07.017 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:10:07.191 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:10:07.238 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@299dae12, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:10:07.351 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:10:07.380 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:10:07.395 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:10:07.411 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:10:07.432 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:10:07.449 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:10:07.462 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:10:07.467 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:10:07.468 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:10:07.476 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:10:07.491 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:10:07.504 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:10:07.516 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:10:07.534 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:10:07.544 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:10:07.555 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:10:07.614 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:10:07.630 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:10:07.647 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:10:07.662 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:10:07.669 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:10:07.669 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:10:07.675 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:10:07.678 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:10:07.679 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:10:07.685 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:10:07.697 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:10:07.710 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:10:07.840 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:10:07.848 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:10:07.855 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:28
14:10:09.014 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:10:09.039 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.955 seconds (process running for 4.916)
14:10:09.047 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:10:13.644 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:10:13.694 [http-nio-9090-exec-1] INFO  c.n.i.c.StandardIngredientRecordsController - [previewIngredient,50] - 预览配料信息
14:10:13.719 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
14:10:13.901 [http-nio-9090-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4dc9df58
14:10:13.903 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
14:10:13.912 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
14:10:13.927 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
14:10:13.947 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
14:10:13.995 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (id = ?)
14:10:13.996 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
14:10:13.999 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
14:10:14.056 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from calculation_result as cr left join standard_raw_materials as srm on cr.raw_material_id = srm.id where cr.id=?
14:10:14.057 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: null
14:10:14.059 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 0
14:10:14.061 [http-nio-9090-exec-1] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 未找到目标成分记录，calculationResultId=1
14:10:14.107 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [java.lang.RuntimeException: 未找到目标成分记录，calculationResultId=1]
14:10:27.007 [http-nio-9090-exec-2] INFO  c.n.i.c.StandardIngredientRecordsController - [previewIngredient,50] - 预览配料信息
14:10:27.008 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
14:10:27.008 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
14:10:27.010 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
14:10:27.011 [http-nio-9090-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (id = ?)
14:10:27.012 [http-nio-9090-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
14:10:27.013 [http-nio-9090-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
14:10:27.015 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from calculation_result as cr left join standard_raw_materials as srm on cr.raw_material_id = srm.id where cr.id=?
14:10:27.015 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: null
14:10:27.016 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 0
14:10:27.017 [http-nio-9090-exec-2] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 未找到目标成分记录，calculationResultId=1
14:10:27.019 [http-nio-9090-exec-2] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [java.lang.RuntimeException: 未找到目标成分记录，calculationResultId=1]
14:15:16.886 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
14:15:16.890 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
14:15:21.435 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 56028 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:15:21.438 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:15:21.439 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:15:22.410 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:15:22.904 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:15:22.906 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:15:22.906 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:15:22.953 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:15:23.121 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:15:23.163 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@5b09c28f, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:15:23.265 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:15:23.290 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:15:23.305 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:15:23.318 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:15:23.333 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:15:23.349 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:15:23.367 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:15:23.375 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:15:23.376 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:15:23.384 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:15:23.403 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:15:23.418 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:15:23.436 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:15:23.451 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:15:23.464 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:15:23.476 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:15:23.519 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:15:23.531 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:15:23.543 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:15:23.556 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:15:23.559 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:15:23.559 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:15:23.566 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:15:23.569 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:15:23.570 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:15:23.577 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:15:23.589 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:15:23.604 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:15:23.733 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:15:23.741 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:15:23.746 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:4
14:15:24.853 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:15:24.879 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.866 seconds (process running for 4.988)
14:15:24.888 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:15:28.750 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:15:28.787 [http-nio-9090-exec-1] INFO  c.n.i.c.StandardIngredientRecordsController - [previewIngredient,50] - 预览配料信息
14:15:28.806 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
14:15:28.946 [http-nio-9090-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4b3b5b0f
14:15:28.947 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
14:15:28.954 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
14:15:28.972 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
14:15:28.991 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
14:15:29.035 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
14:15:29.035 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
14:15:29.038 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 6
14:15:29.093 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from calculation_result as cr left join standard_raw_materials as srm on cr.raw_material_id = srm.id where cr.id=?
14:15:29.093 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: null
14:15:29.096 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 0
14:15:29.098 [http-nio-9090-exec-1] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 未找到目标成分记录，calculationResultId=1
14:15:29.143 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [java.lang.RuntimeException: 未找到目标成分记录，calculationResultId=1]
14:17:45.125 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
14:17:45.129 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
14:17:50.449 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 49128 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:17:50.452 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:17:50.452 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:17:51.448 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:17:51.963 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:17:51.965 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:17:51.966 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:17:52.018 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:17:52.237 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:17:52.286 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@276c7e56, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:17:52.399 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:17:52.426 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:17:52.442 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:17:52.457 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:17:52.476 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:17:52.496 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:17:52.508 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:17:52.513 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:17:52.514 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:17:52.519 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:17:52.535 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:17:52.548 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:17:52.562 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:17:52.578 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:17:52.592 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:17:52.603 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:17:52.656 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:17:52.670 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:17:52.681 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:17:52.693 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:17:52.697 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:17:52.697 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:17:52.705 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:17:52.709 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:17:52.709 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:17:52.714 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:17:52.727 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:17:52.738 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:17:52.871 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:17:52.880 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:17:52.885 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:1
14:17:54.028 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:17:54.054 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.041 seconds (process running for 5.271)
14:17:54.062 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:18:00.019 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:18:00.071 [http-nio-9090-exec-1] INFO  c.n.i.c.StandardIngredientRecordsController - [previewIngredient,50] - 预览配料信息
14:18:00.096 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
14:18:00.248 [http-nio-9090-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@72568bd
14:18:00.250 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
14:18:00.258 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
14:18:00.276 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
14:18:00.293 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
14:18:00.341 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
14:18:00.341 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
14:18:00.344 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 6
14:18:00.394 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from calculation_result as cr left join standard_raw_materials as srm on cr.raw_material_id = srm.id where cr.id=?
14:18:00.394 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long)
14:18:00.397 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 0
14:18:00.398 [http-nio-9090-exec-1] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 未找到计算结果记录，calculationResultId=1
14:18:00.445 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [java.lang.RuntimeException: 未找到计算结果记录，calculationResultId=1]
14:18:46.105 [http-nio-9090-exec-2] INFO  c.n.i.c.StandardIngredientRecordsController - [previewIngredient,50] - 预览配料信息
14:18:46.106 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
14:18:46.107 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
14:18:46.109 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
14:18:46.110 [http-nio-9090-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
14:18:46.111 [http-nio-9090-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
14:18:46.113 [http-nio-9090-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 6
14:18:46.116 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from calculation_result as cr left join standard_raw_materials as srm on cr.raw_material_id = srm.id where cr.id=?
14:18:46.116 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long)
14:18:46.118 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 0
14:18:46.119 [http-nio-9090-exec-2] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 未找到计算结果记录，calculationResultId=1
14:18:46.123 [http-nio-9090-exec-2] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [java.lang.RuntimeException: 未找到计算结果记录，calculationResultId=1]
14:18:47.071 [Thread-5] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
14:18:47.244 [Thread-5] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
14:18:47.250 [Thread-5] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
14:18:47.370 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 49128 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:18:47.370 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:18:47.370 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:18:47.952 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:18:47.952 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:18:47.952 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:18:47.969 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:18:48.027 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:18:48.052 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@10646dca, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:18:48.071 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:18:48.088 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:18:48.104 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:18:48.117 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:18:48.132 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:18:48.145 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:18:48.161 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:18:48.165 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:18:48.166 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:18:48.172 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:18:48.196 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:18:48.212 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:18:48.228 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:18:48.239 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:18:48.253 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:18:48.269 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:18:48.289 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:18:48.312 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:18:48.326 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:18:48.338 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:18:48.343 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:18:48.343 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:18:48.347 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:18:48.350 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:18:48.351 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:18:48.355 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:18:48.366 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:18:48.380 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:18:48.536 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:18:48.548 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:18:48.555 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:1
14:18:48.627 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - [refresh,633] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'ingredientCalculationController': Unsatisfied dependency expressed through field 'purposeCompositionsService': Error creating bean with name 'purposeCompositionsServiceImpl': Unsatisfied dependency expressed through field 'commonService': Error creating bean with name 'commonServiceImpl': Unsatisfied dependency expressed through field 'standardIngredientRecordsService': No qualifying bean of type 'com.nercar.ingredient.service.StandardIngredientRecordsService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
14:18:48.628 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:18:48.765 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - [report,40] - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Field standardIngredientRecordsService in com.nercar.ingredient.service.impl.CommonServiceImpl required a bean of type 'com.nercar.ingredient.service.StandardIngredientRecordsService' that could not be found.

The injection point has the following annotations:
	- @org.springframework.beans.factory.annotation.Autowired(required=true)


Action:

Consider defining a bean of type 'com.nercar.ingredient.service.StandardIngredientRecordsService' in your configuration.

14:18:50.246 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 49128 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:18:50.247 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:18:50.248 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:18:50.600 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:18:50.601 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:18:50.601 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:18:50.615 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:18:50.665 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:18:50.687 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@e991988, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:18:50.698 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:18:50.713 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:18:50.729 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:18:50.742 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:18:50.751 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:18:50.759 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:18:50.769 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:18:50.772 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:18:50.773 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:18:50.777 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:18:50.786 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:18:50.794 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:18:50.804 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:18:50.815 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:18:50.825 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:18:50.834 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:18:50.853 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:18:50.864 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:18:50.875 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:18:50.885 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:18:50.888 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:18:50.889 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:18:50.896 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:18:50.899 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:18:50.899 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:18:50.905 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:18:50.915 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:18:50.924 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:18:51.064 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:18:51.073 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:18:51.078 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:1
14:18:51.519 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:18:51.528 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 1.31 seconds (process running for 62.746)
14:18:51.530 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:18:53.805 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:18:53.808 [http-nio-9090-exec-1] INFO  c.n.i.c.StandardIngredientRecordsController - [previewIngredient,50] - 预览配料信息
14:18:53.809 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-2 - Starting...
14:18:53.873 [http-nio-9090-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@9ff0ce9
14:18:53.873 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-2 - Start completed.
14:18:53.874 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
14:18:53.874 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
14:18:53.878 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
14:18:53.881 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
14:18:53.881 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
14:18:53.884 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 6
14:19:55.391 [HikariPool-2 housekeeper] WARN  c.z.h.p.HikariPool - [run,797] - HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=1m31s516ms815µs200ns).
14:19:55.396 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from calculation_result as cr left join standard_raw_materials as srm on cr.raw_material_id = srm.id where cr.id=?
14:19:55.399 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long)
14:19:55.405 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 1
14:19:55.573 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-2 - Shutdown initiated...
14:19:55.632 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-2 - Shutdown completed.
14:19:58.116 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 54412 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:19:58.119 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:19:58.120 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:19:59.507 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:20:00.054 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:20:00.055 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:20:00.055 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:20:00.100 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:20:00.277 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:20:00.323 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@32ef8f4c, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:20:00.433 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:20:00.465 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:20:00.481 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:20:00.497 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:20:00.516 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:20:00.536 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:20:00.553 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:20:00.559 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:20:00.560 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:20:00.571 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:20:00.590 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:20:00.604 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:20:00.620 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:20:00.637 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:20:00.651 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:20:00.663 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:20:00.708 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:20:00.726 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:20:00.740 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:20:00.755 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:20:00.760 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:20:00.760 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:20:00.768 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:20:00.772 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:20:00.772 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:20:00.778 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:20:00.793 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:20:00.809 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:20:00.937 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:20:00.946 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:20:00.951 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:5
14:20:02.298 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:20:02.325 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.583 seconds (process running for 5.754)
14:20:02.334 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:23:00.917 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 55212 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:23:00.919 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:23:00.920 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:23:01.887 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:23:02.410 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:23:02.411 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:23:02.412 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:23:02.458 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:23:02.643 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:23:02.691 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@53a037fc, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:23:02.797 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:23:02.824 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:23:02.840 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:23:02.856 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:23:02.869 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:23:02.890 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:23:02.903 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:23:02.907 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:23:02.908 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:23:02.914 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:23:02.930 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:23:02.943 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:23:02.958 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:23:02.969 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:23:02.984 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:23:02.995 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:23:03.034 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:23:03.046 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:23:03.056 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:23:03.068 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:23:03.072 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:23:03.072 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:23:03.077 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:23:03.080 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:23:03.081 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:23:03.087 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:23:03.100 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:23:03.111 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:23:03.239 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:23:03.247 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:23:03.252 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:2
14:23:04.391 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:23:04.420 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.92 seconds (process running for 5.137)
14:23:04.429 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:23:10.586 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:23:10.624 [http-nio-9090-exec-1] INFO  c.n.i.c.StandardIngredientRecordsController - [previewIngredient,50] - 预览配料信息
14:23:10.643 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
14:23:10.778 [http-nio-9090-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@443ff495
14:23:10.779 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
14:23:10.786 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
14:23:10.803 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
14:23:10.825 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
14:23:10.874 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
14:23:10.874 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
14:23:10.877 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 6
14:23:10.928 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from calculation_result as cr left join standard_raw_materials as srm on cr.raw_material_id = srm.id where cr.id=?
14:23:10.929 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: null
14:23:10.931 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 0
14:23:10.933 [http-nio-9090-exec-1] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 未找到计算结果记录，calculationResultId=1
14:23:10.977 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [java.lang.RuntimeException: 未找到计算结果记录，calculationResultId=1]
14:24:36.502 [http-nio-9090-exec-3] INFO  c.n.i.c.StandardIngredientRecordsController - [previewIngredient,50] - 预览配料信息
14:24:36.503 [http-nio-9090-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
14:24:36.503 [http-nio-9090-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
14:24:36.505 [http-nio-9090-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
14:24:36.508 [http-nio-9090-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
14:24:36.508 [http-nio-9090-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
14:24:36.511 [http-nio-9090-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 6
14:24:36.513 [http-nio-9090-exec-3] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from calculation_result as cr left join standard_raw_materials as srm on cr.raw_material_id = srm.id where cr.id=?
14:24:36.514 [http-nio-9090-exec-3] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: null
14:24:36.515 [http-nio-9090-exec-3] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 0
14:24:36.516 [http-nio-9090-exec-3] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 未找到计算结果记录，calculationResultId=1
14:24:36.518 [http-nio-9090-exec-3] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [java.lang.RuntimeException: 未找到计算结果记录，calculationResultId=1]
14:26:23.553 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
14:26:23.556 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
14:26:28.985 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 46200 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:26:28.987 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:26:28.988 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:26:30.018 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:26:30.515 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:26:30.517 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:26:30.517 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:26:30.563 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:26:30.737 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:26:30.782 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@77f09ce3, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:26:30.903 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:26:30.927 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:26:30.945 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:26:30.963 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:26:30.982 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:26:31.003 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:26:31.020 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:26:31.024 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:26:31.025 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:26:31.031 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:26:31.048 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:26:31.062 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:26:31.076 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:26:31.091 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:26:31.103 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:26:31.114 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:26:31.158 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:26:31.174 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:26:31.187 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:26:31.200 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:26:31.204 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:26:31.204 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:26:31.210 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:26:31.214 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:26:31.214 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:26:31.221 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:26:31.234 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:26:31.245 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:26:31.376 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:26:31.385 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:26:31.391 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:31
14:26:32.561 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:26:32.593 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.982 seconds (process running for 4.987)
14:26:32.602 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:26:49.030 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:26:49.066 [http-nio-9090-exec-1] INFO  c.n.i.c.StandardIngredientRecordsController - [previewIngredient,50] - 预览配料信息
14:26:49.085 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
14:26:49.223 [http-nio-9090-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@567b1661
14:26:49.224 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
14:26:49.233 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
14:26:49.250 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
14:26:49.271 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
14:26:49.322 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
14:26:49.322 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
14:26:49.325 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 6
14:26:49.381 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from calculation_result as cr left join standard_raw_materials as srm on cr.raw_material_id = srm.id where cr.id=?
14:26:49.382 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long)
14:26:49.386 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 1
14:28:30.254 [http-nio-9090-exec-3] INFO  c.n.i.c.StandardIngredientRecordsController - [getStandardIngredientRecords,35] - 分页查询标准配料记录
14:28:30.538 [http-nio-9090-exec-3] DEBUG c.n.i.m.S.selectRecords_mpCount - [debug,135] - ==>  Preparing: SELECT COUNT(*) AS total FROM standard_ingredient_records AS air WHERE (mixing_date = ?)
14:28:30.539 [http-nio-9090-exec-3] DEBUG c.n.i.m.S.selectRecords_mpCount - [debug,135] - ==> Parameters: 2024-02-01T08:00(LocalDateTime)
14:28:30.558 [http-nio-9090-exec-3] DEBUG c.n.i.m.S.selectRecords_mpCount - [debug,135] - <==      Total: 1
14:28:30.562 [http-nio-9090-exec-3] DEBUG c.n.i.m.S.selectRecords - [debug,135] - ==>  Preparing: select air.id, air.user_name, air.department_id, air.steel_grade_id, air.calculation_process_no, air.execution_standard_id, air.raw_material_total, air.cost_price, air.mixing_date, air.release_date, air.category, air.calculation_result_id, air.process_path_id, air.special_notes, air.cost_estimattion_id, air.createuser, air.createtime, air.updatetime, sg.steel_grade as steelGrade, es.standard_name as standardName from standard_ingredient_records as air left join steel_grades as sg on air.steel_grade_id = sg.id left join execution_standard as es on air.execution_standard_id = es.id WHERE (mixing_date = ?) LIMIT ?
14:28:30.562 [http-nio-9090-exec-3] DEBUG c.n.i.m.S.selectRecords - [debug,135] - ==> Parameters: 2024-02-01T08:00(LocalDateTime), 3(Long)
14:28:30.566 [http-nio-9090-exec-3] DEBUG c.n.i.m.S.selectRecords - [debug,135] - <==      Total: 3
14:28:58.741 [Thread-5] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
14:28:58.877 [Thread-5] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
14:28:58.881 [Thread-5] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
14:28:59.037 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 46200 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:28:59.037 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:28:59.037 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:28:59.599 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:28:59.599 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:28:59.599 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:28:59.617 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:28:59.679 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:28:59.702 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@336fc957, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:28:59.717 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:28:59.739 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:28:59.752 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:28:59.762 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:28:59.774 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:28:59.785 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:28:59.795 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:28:59.799 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:28:59.800 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:28:59.806 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:28:59.821 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:28:59.834 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:28:59.846 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:28:59.860 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:28:59.876 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:28:59.891 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:28:59.910 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:28:59.920 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:28:59.929 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:28:59.939 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:28:59.943 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:28:59.943 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:28:59.947 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:28:59.950 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:28:59.950 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:28:59.954 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:28:59.962 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:28:59.970 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:29:00.141 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:29:00.149 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:29:00.155 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:31
14:29:00.696 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:29:00.704 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 1.722 seconds (process running for 153.098)
14:29:00.710 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:31:50.848 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 57004 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:31:50.850 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:31:50.851 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:31:53.546 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:31:54.080 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:31:54.081 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:31:54.081 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:31:54.127 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:31:54.302 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:31:54.346 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@b48b214, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:31:54.494 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:31:54.540 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:31:54.571 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:31:54.597 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:31:54.622 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:31:54.647 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:31:54.667 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:31:54.678 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:31:54.678 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:31:54.686 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:31:54.711 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:31:54.731 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:31:54.756 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:31:54.779 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:31:54.803 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:31:54.828 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:31:54.877 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:31:54.896 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:31:54.912 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:31:54.931 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:31:54.940 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:31:54.940 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:31:54.945 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:31:54.955 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:31:54.955 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:31:54.960 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:31:54.982 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:31:55.002 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:31:55.134 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:31:55.142 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:31:55.148 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:1
14:31:56.270 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:31:56.299 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 5.808 seconds (process running for 7.047)
14:31:56.310 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:32:13.461 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:32:14.279 [http-nio-9090-exec-2] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 357 ms
14:38:10.641 [Thread-5] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
14:38:10.899 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 57004 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:38:10.900 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:38:10.900 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:38:11.348 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:38:11.348 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:38:11.349 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:38:11.363 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:38:11.414 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:38:11.437 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@20a22f78, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:38:11.452 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:38:11.469 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:38:11.487 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:38:11.502 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:38:11.515 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:38:11.526 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:38:11.535 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:38:11.538 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:38:11.539 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:38:11.544 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:38:11.563 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:38:11.574 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:38:11.586 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:38:11.600 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:38:11.610 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:38:11.624 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:38:11.642 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:38:11.657 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:38:11.667 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:38:11.681 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:38:11.684 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:38:11.684 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:38:11.689 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:38:11.693 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:38:11.693 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:38:11.703 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:38:11.717 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:38:11.729 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:38:11.883 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:38:11.895 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:38:11.902 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:1
14:38:12.430 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:38:12.438 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 1.59 seconds (process running for 383.187)
14:38:12.445 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:46:32.548 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 28452 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:46:32.550 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:46:32.550 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:46:33.633 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:46:34.187 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:46:34.189 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:46:34.189 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:46:34.241 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:46:34.412 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:46:34.452 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@29a2e4f7, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:46:34.572 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:46:34.609 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:46:34.631 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:46:34.652 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:46:34.670 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:46:34.689 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:46:34.717 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:46:34.723 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:46:34.726 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:46:34.736 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:46:34.760 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:46:34.778 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:46:34.795 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:46:34.811 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:46:34.823 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:46:34.834 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:46:34.884 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:46:34.904 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:46:34.919 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:46:34.936 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:46:34.940 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:46:34.940 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:46:34.948 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:46:34.952 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:46:34.952 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:46:34.960 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:46:34.979 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:46:34.995 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:46:35.135 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:46:35.145 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:46:35.152 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:26
14:46:36.443 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:46:36.470 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.27 seconds (process running for 5.145)
14:46:36.478 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
17:14:22.399 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 27676 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
17:14:22.402 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
17:14:22.402 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
17:14:23.553 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
17:14:24.150 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
17:14:24.152 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:14:24.152 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
17:14:24.200 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:14:24.370 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
17:14:24.412 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@7c22bb48, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
17:14:24.516 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
17:14:24.544 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
17:14:24.559 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
17:14:24.576 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
17:14:24.595 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
17:14:24.626 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
17:14:24.651 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
17:14:24.658 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
17:14:24.658 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:14:24.670 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
17:14:24.699 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
17:14:24.721 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
17:14:24.738 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
17:14:24.760 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
17:14:24.781 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
17:14:24.799 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
17:14:24.857 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
17:14:24.887 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
17:14:24.902 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
17:14:24.926 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
17:14:24.932 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
17:14:24.933 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:14:24.945 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
17:14:24.950 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
17:14:24.950 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:14:24.958 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
17:14:24.975 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
17:14:24.992 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
17:14:25.133 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
17:14:25.143 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
17:14:25.149 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:31
17:14:26.400 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
17:14:26.431 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.501 seconds (process running for 5.701)
17:14:26.442 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
17:14:36.034 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:14:37.100 [http-nio-9090-exec-4] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 397 ms
17:15:50.823 [http-nio-9090-exec-7] INFO  c.n.i.c.StandardRawMaterialsController - [getStandardRawMaterials,32] - 分页查询标准原料
17:15:50.920 [http-nio-9090-exec-7] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
17:15:51.074 [http-nio-9090-exec-7] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4c38c01b
17:15:51.075 [http-nio-9090-exec-7] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
17:15:51.082 [http-nio-9090-exec-7] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (department_name = ?)
17:15:51.097 [http-nio-9090-exec-7] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 0(Long)
17:15:51.240 [http-nio-9090-exec-7] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 
### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 操作符不存在: character varying = bigint
  建议：没有匹配指定名称和参数类型的操作符. 您也许需要增加明确的类型转换.
  位置：108
### The error may exist in com/nercar/ingredient/mapper/DepartmentsMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT  id,department_name,createuser,createtime,updatetime  FROM departments      WHERE  (department_name = ?)
### Cause: org.postgresql.util.PSQLException: 错误: 操作符不存在: character varying = bigint
  建议：没有匹配指定名称和参数类型的操作符. 您也许需要增加明确的类型转换.
  位置：108
; bad SQL grammar []
17:15:51.248 [http-nio-9090-exec-7] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.jdbc.BadSqlGrammarException: <EOL><EOL>### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 操作符不存在: character varying = bigint<EOL>  建议：没有匹配指定名称和参数类型的操作符. 您也许需要增加明确的类型转换.<EOL>  位置：108<EOL><EOL>### The error may exist in com/nercar/ingredient/mapper/DepartmentsMapper.java (best guess)<EOL><EOL>### The error may involve defaultParameterMap<EOL><EOL>### The error occurred while setting parameters<EOL><EOL>### SQL: SELECT  id,department_name,createuser,createtime,updatetime  FROM departments      WHERE  (department_name = ?)<EOL><EOL>### Cause: org.postgresql.util.PSQLException: 错误: 操作符不存在: character varying = bigint<EOL>  建议：没有匹配指定名称和参数类型的操作符. 您也许需要增加明确的类型转换.<EOL>  位置：108<EOL>; bad SQL grammar []]
17:17:08.891 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
17:17:08.894 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
17:17:14.184 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 46000 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
17:17:14.186 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
17:17:14.186 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
17:17:15.117 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
17:17:15.617 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
17:17:15.618 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:17:15.618 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
17:17:15.662 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:17:15.831 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
17:17:15.877 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@2092a6fe, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
17:17:15.987 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
17:17:16.012 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
17:17:16.027 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
17:17:16.042 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
17:17:16.062 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
17:17:16.081 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
17:17:16.097 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
17:17:16.102 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
17:17:16.103 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:17:16.111 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
17:17:16.128 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
17:17:16.142 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
17:17:16.154 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
17:17:16.166 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
17:17:16.178 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
17:17:16.188 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
17:17:16.225 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
17:17:16.238 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
17:17:16.249 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
17:17:16.261 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
17:17:16.264 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
17:17:16.265 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:17:16.275 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
17:17:16.278 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
17:17:16.279 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:17:16.284 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
17:17:16.297 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
17:17:16.307 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
17:17:16.437 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
17:17:16.446 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
17:17:16.451 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:29
17:17:17.567 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
17:17:17.594 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.749 seconds (process running for 4.976)
17:17:17.602 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
17:17:20.994 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:17:21.862 [http-nio-9090-exec-4] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 380 ms
17:17:48.467 [http-nio-9090-exec-7] INFO  c.n.i.c.StandardRawMaterialsController - [getStandardRawMaterials,32] - 分页查询标准原料
17:17:48.599 [http-nio-9090-exec-7] WARN  c.b.m.e.p.i.PaginationInnerInterceptor - [autoCountSql,356] - optimize this sql to a count sql has exception, sql:"select
            srm.id,srm.name,srm.element,
            srm.secondary_element,srm.carbon_element,srm.department_id,
            srm.production_equipment_id,srm.composition,srm.yield_rate,
            srm.price,srm.is_custom,srm.category,
            srm.createuser,srm.createtime,srm.updatetime,
        from standard_raw_materials as srm", exception:
java.util.concurrent.ExecutionException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "," ","
    at line 6, column 57.

Was expecting one of:

    <EOF>
    <ST_SEMICOLON>

17:17:48.610 [http-nio-9090-exec-7] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
17:17:48.807 [http-nio-9090-exec-7] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@77dc05d5
17:17:48.810 [http-nio-9090-exec-7] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
17:17:48.817 [http-nio-9090-exec-7] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM (select srm.id,srm.name,srm.element, srm.secondary_element,srm.carbon_element,srm.department_id, srm.production_equipment_id,srm.composition,srm.yield_rate, srm.price,srm.is_custom,srm.category, srm.createuser,srm.createtime,srm.updatetime, from standard_raw_materials as srm) TOTAL
17:17:48.830 [http-nio-9090-exec-7] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==> Parameters: 
17:17:48.922 [http-nio-9090-exec-7] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 
### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 语法错误 在 "from" 或附近的
  位置：331
### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) FROM (select             srm.id,srm.name,srm.element,             srm.secondary_element,srm.carbon_element,srm.department_id,             srm.production_equipment_id,srm.composition,srm.yield_rate,             srm.price,srm.is_custom,srm.category,             srm.createuser,srm.createtime,srm.updatetime,         from standard_raw_materials as srm) TOTAL
### Cause: org.postgresql.util.PSQLException: 错误: 语法错误 在 "from" 或附近的
  位置：331
; bad SQL grammar []
17:17:48.927 [http-nio-9090-exec-7] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.jdbc.BadSqlGrammarException: <EOL><EOL>### Error querying database.  Cause: org.postgresql.util.PSQLException: 错误: 语法错误 在 "from" 或附近的<EOL>  位置：331<EOL><EOL>### The error may exist in file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]<EOL><EOL>### The error may involve defaultParameterMap<EOL><EOL>### The error occurred while setting parameters<EOL><EOL>### SQL: SELECT COUNT(*) FROM (select             srm.id,srm.name,srm.element,             srm.secondary_element,srm.carbon_element,srm.department_id,             srm.production_equipment_id,srm.composition,srm.yield_rate,             srm.price,srm.is_custom,srm.category,             srm.createuser,srm.createtime,srm.updatetime,         from standard_raw_materials as srm) TOTAL<EOL><EOL>### Cause: org.postgresql.util.PSQLException: 错误: 语法错误 在 "from" 或附近的<EOL>  位置：331<EOL>; bad SQL grammar []]
17:18:51.758 [Thread-5] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
17:18:51.920 [Thread-5] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
17:18:51.924 [Thread-5] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
17:18:52.049 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 46000 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
17:18:52.049 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
17:18:52.049 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
17:18:52.493 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
17:18:52.494 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:18:52.494 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
17:18:52.507 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:18:52.550 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
17:18:52.569 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@7a963343, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
17:18:52.582 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
17:18:52.596 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
17:18:52.607 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
17:18:52.624 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
17:18:52.639 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
17:18:52.648 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
17:18:52.662 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
17:18:52.665 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
17:18:52.665 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:18:52.670 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
17:18:52.683 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
17:18:52.694 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
17:18:52.705 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
17:18:52.714 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
17:18:52.724 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
17:18:52.732 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
17:18:52.746 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
17:18:52.763 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
17:18:52.771 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
17:18:52.780 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
17:18:52.782 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
17:18:52.782 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:18:52.786 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
17:18:52.789 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
17:18:52.789 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:18:52.793 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
17:18:52.801 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
17:18:52.810 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
17:18:52.949 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
17:18:52.957 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
17:18:52.963 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:29
17:18:53.412 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
17:18:53.423 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 1.429 seconds (process running for 100.804)
17:18:53.428 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
17:19:31.939 [Thread-9] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
17:19:32.141 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 46000 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
17:19:32.142 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
17:19:32.142 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
17:19:32.452 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
17:19:32.452 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:19:32.452 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
17:19:32.464 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:19:32.504 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
17:19:32.521 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@44877128, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
17:19:32.529 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
17:19:32.538 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
17:19:32.546 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
17:19:32.552 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
17:19:32.560 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
17:19:32.567 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
17:19:32.578 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
17:19:32.580 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
17:19:32.581 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:19:32.585 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
17:19:32.593 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
17:19:32.601 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
17:19:32.608 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
17:19:32.614 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
17:19:32.621 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
17:19:32.629 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
17:19:32.643 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
17:19:32.662 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
17:19:32.673 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
17:19:32.683 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
17:19:32.686 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
17:19:32.686 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:19:32.690 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
17:19:32.692 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
17:19:32.693 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:19:32.697 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
17:19:32.704 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
17:19:32.711 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
17:19:32.842 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
17:19:32.850 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
17:19:32.856 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:29
17:19:33.228 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
17:19:33.235 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 1.119 seconds (process running for 140.617)
17:19:33.237 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
17:20:44.790 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 32744 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
17:20:44.793 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
17:20:44.793 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
17:20:45.960 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
17:20:46.478 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
17:20:46.480 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:20:46.480 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
17:20:46.528 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:20:46.702 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
17:20:46.743 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@19eed4a9, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
17:20:46.859 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
17:20:46.886 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
17:20:46.901 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
17:20:46.923 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
17:20:46.942 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
17:20:46.959 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
17:20:46.973 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
17:20:46.977 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
17:20:46.977 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:20:46.985 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
17:20:47.002 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
17:20:47.017 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
17:20:47.033 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
17:20:47.053 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
17:20:47.070 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
17:20:47.081 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
17:20:47.127 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
17:20:47.154 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
17:20:47.168 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
17:20:47.186 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
17:20:47.189 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
17:20:47.190 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:20:47.196 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
17:20:47.200 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
17:20:47.201 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:20:47.208 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
17:20:47.226 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
17:20:47.241 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
17:20:47.392 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
17:20:47.404 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
17:20:47.410 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:7
17:20:48.616 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
17:20:48.644 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.24 seconds (process running for 5.348)
17:20:48.654 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
17:20:52.079 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:20:52.176 [http-nio-9090-exec-1] INFO  c.n.i.c.StandardRawMaterialsController - [getStandardRawMaterials,32] - 分页查询标准原料
17:20:52.318 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
17:20:52.568 [http-nio-9090-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@62f47a2d
17:20:52.570 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
17:20:52.578 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==>  Preparing: SELECT COUNT(*) AS total FROM standard_raw_materials AS srm
17:20:52.592 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==> Parameters: 
17:20:52.607 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - <==      Total: 1
17:20:52.619 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.id,srm.name,srm.element, srm.secondary_element,srm.carbon_element,srm.department_id, srm.production_equipment_id,srm.composition,srm.yield_rate, srm.price,srm.is_custom,srm.category, srm.createuser,srm.createtime,srm.updatetime from standard_raw_materials as srm LIMIT ?
17:20:52.620 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 3(Long)
17:20:52.624 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 3
17:20:52.641 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (id = ?)
17:20:52.641 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(Long)
17:20:52.738 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 0
17:20:52.739 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
17:20:52.740 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 101(Long)
17:20:52.759 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 0
17:20:52.760 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (id = ?)
17:20:52.761 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 2(Long)
17:20:52.761 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 0
17:20:52.762 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
17:20:52.763 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 102(Long)
17:20:52.764 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 0
17:20:52.765 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (id = ?)
17:20:52.766 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 3(Long)
17:20:52.766 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 0
17:20:52.767 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
17:20:52.768 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 103(Long)
17:20:52.768 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 0
17:24:51.685 [http-nio-9090-exec-4] INFO  c.n.i.c.StandardRawMaterialsController - [getStandardRawMaterials,32] - 分页查询标准原料
17:24:51.687 [http-nio-9090-exec-4] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (department_name = ?)
17:24:51.688 [http-nio-9090-exec-4] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(String)
17:24:51.788 [http-nio-9090-exec-4] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:24:51.823 [http-nio-9090-exec-4] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==>  Preparing: SELECT COUNT(*) AS total FROM standard_raw_materials AS srm WHERE (department_id = ? AND name = ? AND category = ?)
17:24:51.823 [http-nio-9090-exec-4] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String)
17:24:51.824 [http-nio-9090-exec-4] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - <==      Total: 1
17:24:51.825 [http-nio-9090-exec-4] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.id,srm.name,srm.element, srm.secondary_element,srm.carbon_element,srm.department_id, srm.production_equipment_id,srm.composition,srm.yield_rate, srm.price,srm.is_custom,srm.category, srm.createuser,srm.createtime,srm.updatetime from standard_raw_materials as srm WHERE (department_id = ? AND name = ? AND category = ?) LIMIT ?
17:24:51.825 [http-nio-9090-exec-4] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String), 3(Long)
17:24:51.827 [http-nio-9090-exec-4] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 1
17:24:51.828 [http-nio-9090-exec-4] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (id = ?)
17:24:51.828 [http-nio-9090-exec-4] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(Long)
17:24:51.829 [http-nio-9090-exec-4] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:24:51.830 [http-nio-9090-exec-4] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
17:24:51.830 [http-nio-9090-exec-4] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 101(Long)
17:24:51.830 [http-nio-9090-exec-4] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 0
17:25:33.718 [http-nio-9090-exec-2] INFO  c.n.i.c.StandardRawMaterialsController - [getStandardRawMaterials,32] - 分页查询标准原料
17:25:33.719 [http-nio-9090-exec-2] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (department_name = ?)
17:25:33.720 [http-nio-9090-exec-2] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(String)
17:25:33.721 [http-nio-9090-exec-2] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:25:33.738 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==>  Preparing: SELECT COUNT(*) AS total FROM standard_raw_materials AS srm WHERE (department_id = ? AND name = ? AND category = ?)
17:25:33.739 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String)
17:25:33.740 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - <==      Total: 1
17:25:33.741 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.id,srm.name,srm.element, srm.secondary_element,srm.carbon_element,srm.department_id, srm.production_equipment_id,srm.composition,srm.yield_rate, srm.price,srm.is_custom,srm.category, srm.createuser,srm.createtime,srm.updatetime from standard_raw_materials as srm WHERE (department_id = ? AND name = ? AND category = ?) LIMIT ?
17:25:33.743 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String), 3(Long)
17:25:33.745 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 2
17:25:33.747 [http-nio-9090-exec-2] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (id = ?)
17:25:33.747 [http-nio-9090-exec-2] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(Long)
17:25:33.748 [http-nio-9090-exec-2] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:25:33.749 [http-nio-9090-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
17:25:33.749 [http-nio-9090-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 101(Long)
17:25:33.749 [http-nio-9090-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 0
17:25:33.751 [http-nio-9090-exec-2] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (id = ?)
17:25:33.752 [http-nio-9090-exec-2] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(Long)
17:25:33.755 [http-nio-9090-exec-2] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:25:33.757 [http-nio-9090-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
17:25:33.758 [http-nio-9090-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 101(Long)
17:25:33.758 [http-nio-9090-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 0
17:25:40.283 [http-nio-9090-exec-5] INFO  c.n.i.c.StandardRawMaterialsController - [getStandardRawMaterials,32] - 分页查询标准原料
17:25:40.284 [http-nio-9090-exec-5] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (department_name = ?)
17:25:40.285 [http-nio-9090-exec-5] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(String)
17:25:40.285 [http-nio-9090-exec-5] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:25:40.316 [http-nio-9090-exec-5] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==>  Preparing: SELECT COUNT(*) AS total FROM standard_raw_materials AS srm WHERE (department_id = ? AND name = ? AND category = ?)
17:25:40.317 [http-nio-9090-exec-5] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String)
17:25:40.318 [http-nio-9090-exec-5] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - <==      Total: 1
17:25:40.318 [http-nio-9090-exec-5] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.id,srm.name,srm.element, srm.secondary_element,srm.carbon_element,srm.department_id, srm.production_equipment_id,srm.composition,srm.yield_rate, srm.price,srm.is_custom,srm.category, srm.createuser,srm.createtime,srm.updatetime from standard_raw_materials as srm WHERE (department_id = ? AND name = ? AND category = ?) LIMIT ?
17:25:40.318 [http-nio-9090-exec-5] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String), 3(Long)
17:25:40.320 [http-nio-9090-exec-5] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 2
17:25:40.323 [http-nio-9090-exec-5] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (id = ?)
17:25:40.323 [http-nio-9090-exec-5] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(Long)
17:25:40.324 [http-nio-9090-exec-5] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:25:40.325 [http-nio-9090-exec-5] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
17:25:40.325 [http-nio-9090-exec-5] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 101(Long)
17:25:40.326 [http-nio-9090-exec-5] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 0
17:25:40.327 [http-nio-9090-exec-5] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (id = ?)
17:25:40.327 [http-nio-9090-exec-5] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(Long)
17:25:40.329 [http-nio-9090-exec-5] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:25:40.329 [http-nio-9090-exec-5] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
17:25:40.329 [http-nio-9090-exec-5] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 101(Long)
17:25:40.330 [http-nio-9090-exec-5] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 0
17:25:47.482 [http-nio-9090-exec-6] INFO  c.n.i.c.StandardRawMaterialsController - [getStandardRawMaterials,32] - 分页查询标准原料
17:25:47.483 [http-nio-9090-exec-6] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (department_name = ?)
17:25:47.483 [http-nio-9090-exec-6] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(String)
17:25:47.484 [http-nio-9090-exec-6] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:25:47.499 [http-nio-9090-exec-6] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==>  Preparing: SELECT COUNT(*) AS total FROM standard_raw_materials AS srm WHERE (department_id = ? AND name = ? AND category = ?)
17:25:47.500 [http-nio-9090-exec-6] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String)
17:25:47.501 [http-nio-9090-exec-6] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - <==      Total: 1
17:25:47.501 [http-nio-9090-exec-6] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.id,srm.name,srm.element, srm.secondary_element,srm.carbon_element,srm.department_id, srm.production_equipment_id,srm.composition,srm.yield_rate, srm.price,srm.is_custom,srm.category, srm.createuser,srm.createtime,srm.updatetime from standard_raw_materials as srm WHERE (department_id = ? AND name = ? AND category = ?) LIMIT ?
17:25:47.502 [http-nio-9090-exec-6] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String), 1(Long)
17:25:47.503 [http-nio-9090-exec-6] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 1
17:25:47.504 [http-nio-9090-exec-6] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (id = ?)
17:25:47.504 [http-nio-9090-exec-6] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(Long)
17:25:47.506 [http-nio-9090-exec-6] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:25:47.507 [http-nio-9090-exec-6] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
17:25:47.507 [http-nio-9090-exec-6] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 101(Long)
17:25:47.507 [http-nio-9090-exec-6] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 0
17:25:49.803 [http-nio-9090-exec-7] INFO  c.n.i.c.StandardRawMaterialsController - [getStandardRawMaterials,32] - 分页查询标准原料
17:25:49.805 [http-nio-9090-exec-7] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (department_name = ?)
17:25:49.805 [http-nio-9090-exec-7] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(String)
17:25:49.806 [http-nio-9090-exec-7] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:25:49.819 [http-nio-9090-exec-7] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==>  Preparing: SELECT COUNT(*) AS total FROM standard_raw_materials AS srm WHERE (department_id = ? AND name = ? AND category = ?)
17:25:49.819 [http-nio-9090-exec-7] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String)
17:25:49.820 [http-nio-9090-exec-7] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - <==      Total: 1
17:25:49.821 [http-nio-9090-exec-7] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.id,srm.name,srm.element, srm.secondary_element,srm.carbon_element,srm.department_id, srm.production_equipment_id,srm.composition,srm.yield_rate, srm.price,srm.is_custom,srm.category, srm.createuser,srm.createtime,srm.updatetime from standard_raw_materials as srm WHERE (department_id = ? AND name = ? AND category = ?) LIMIT ?
17:25:49.821 [http-nio-9090-exec-7] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String), 1(Long)
17:25:49.823 [http-nio-9090-exec-7] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 1
17:25:49.825 [http-nio-9090-exec-7] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (id = ?)
17:25:49.825 [http-nio-9090-exec-7] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(Long)
17:25:49.826 [http-nio-9090-exec-7] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:25:49.827 [http-nio-9090-exec-7] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
17:25:49.827 [http-nio-9090-exec-7] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 101(Long)
17:25:49.828 [http-nio-9090-exec-7] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 0
17:26:21.708 [http-nio-9090-exec-8] INFO  c.n.i.c.StandardRawMaterialsController - [getStandardRawMaterials,32] - 分页查询标准原料
17:26:21.710 [http-nio-9090-exec-8] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (department_name = ?)
17:26:21.711 [http-nio-9090-exec-8] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(String)
17:26:21.712 [http-nio-9090-exec-8] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:26:21.730 [http-nio-9090-exec-8] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==>  Preparing: SELECT COUNT(*) AS total FROM standard_raw_materials AS srm WHERE (department_id = ? AND name = ? AND category = ?)
17:26:21.730 [http-nio-9090-exec-8] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String)
17:26:21.731 [http-nio-9090-exec-8] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - <==      Total: 1
17:26:21.731 [http-nio-9090-exec-8] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.id,srm.name,srm.element, srm.secondary_element,srm.carbon_element,srm.department_id, srm.production_equipment_id,srm.composition,srm.yield_rate, srm.price,srm.is_custom,srm.category, srm.createuser,srm.createtime,srm.updatetime from standard_raw_materials as srm WHERE (department_id = ? AND name = ? AND category = ?) LIMIT ?
17:26:21.732 [http-nio-9090-exec-8] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String), 1(Long)
17:26:21.733 [http-nio-9090-exec-8] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 1
17:26:21.733 [http-nio-9090-exec-8] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (id = ?)
17:26:21.734 [http-nio-9090-exec-8] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(Long)
17:26:21.735 [http-nio-9090-exec-8] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:26:21.736 [http-nio-9090-exec-8] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
17:26:21.737 [http-nio-9090-exec-8] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 101(Long)
17:26:21.738 [http-nio-9090-exec-8] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 0
17:26:44.099 [http-nio-9090-exec-9] INFO  c.n.i.c.StandardRawMaterialsController - [getStandardRawMaterials,32] - 分页查询标准原料
17:26:44.100 [http-nio-9090-exec-9] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (department_name = ?)
17:26:44.101 [http-nio-9090-exec-9] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(String)
17:26:44.101 [http-nio-9090-exec-9] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:26:44.117 [http-nio-9090-exec-9] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==>  Preparing: SELECT COUNT(*) AS total FROM standard_raw_materials AS srm WHERE (department_id = ? AND name = ? AND category = ?)
17:26:44.118 [http-nio-9090-exec-9] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String)
17:26:44.119 [http-nio-9090-exec-9] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - <==      Total: 1
17:26:44.119 [http-nio-9090-exec-9] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.id,srm.name,srm.element, srm.secondary_element,srm.carbon_element,srm.department_id, srm.production_equipment_id,srm.composition,srm.yield_rate, srm.price,srm.is_custom,srm.category, srm.createuser,srm.createtime,srm.updatetime from standard_raw_materials as srm WHERE (department_id = ? AND name = ? AND category = ?) LIMIT ?
17:26:44.120 [http-nio-9090-exec-9] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String), 3(Long)
17:26:44.122 [http-nio-9090-exec-9] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 2
17:26:44.122 [http-nio-9090-exec-9] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (id = ?)
17:26:44.123 [http-nio-9090-exec-9] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(Long)
17:26:44.127 [http-nio-9090-exec-9] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:26:44.128 [http-nio-9090-exec-9] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
17:26:44.129 [http-nio-9090-exec-9] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 101(Long)
17:26:44.129 [http-nio-9090-exec-9] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 0
17:26:44.130 [http-nio-9090-exec-9] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (id = ?)
17:26:44.131 [http-nio-9090-exec-9] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(Long)
17:26:44.131 [http-nio-9090-exec-9] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:26:44.132 [http-nio-9090-exec-9] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
17:26:44.133 [http-nio-9090-exec-9] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 101(Long)
17:26:44.134 [http-nio-9090-exec-9] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 0
17:27:26.212 [http-nio-9090-exec-10] INFO  c.n.i.c.StandardRawMaterialsController - [getStandardRawMaterials,32] - 分页查询标准原料
17:27:26.214 [http-nio-9090-exec-10] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (department_name = ?)
17:27:26.214 [http-nio-9090-exec-10] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(String)
17:27:26.215 [http-nio-9090-exec-10] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:27:26.229 [http-nio-9090-exec-10] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==>  Preparing: SELECT COUNT(*) AS total FROM standard_raw_materials AS srm WHERE (department_id = ? AND name = ? AND category = ?)
17:27:26.230 [http-nio-9090-exec-10] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String)
17:27:26.231 [http-nio-9090-exec-10] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - <==      Total: 1
17:27:26.232 [http-nio-9090-exec-10] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.id,srm.name,srm.element, srm.secondary_element,srm.carbon_element,srm.department_id, srm.production_equipment_id,srm.composition,srm.yield_rate, srm.price,srm.is_custom,srm.category, srm.createuser,srm.createtime,srm.updatetime from standard_raw_materials as srm WHERE (department_id = ? AND name = ? AND category = ?) LIMIT ?
17:27:26.232 [http-nio-9090-exec-10] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String), 3(Long)
17:27:26.233 [http-nio-9090-exec-10] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 2
17:27:26.234 [http-nio-9090-exec-10] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (id = ?)
17:27:26.235 [http-nio-9090-exec-10] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(Long)
17:27:26.235 [http-nio-9090-exec-10] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:27:26.236 [http-nio-9090-exec-10] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
17:27:26.237 [http-nio-9090-exec-10] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 101(Long)
17:27:26.238 [http-nio-9090-exec-10] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
17:27:26.240 [http-nio-9090-exec-10] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (id = ?)
17:27:26.240 [http-nio-9090-exec-10] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(Long)
17:27:26.241 [http-nio-9090-exec-10] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:27:26.242 [http-nio-9090-exec-10] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
17:27:26.242 [http-nio-9090-exec-10] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 101(Long)
17:27:26.242 [http-nio-9090-exec-10] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
17:27:33.113 [http-nio-9090-exec-1] INFO  c.n.i.c.StandardRawMaterialsController - [getStandardRawMaterials,32] - 分页查询标准原料
17:27:33.114 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (department_name = ?)
17:27:33.114 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(String)
17:27:33.114 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:27:33.129 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==>  Preparing: SELECT COUNT(*) AS total FROM standard_raw_materials AS srm WHERE (department_id = ? AND name = ? AND category = ?)
17:27:33.130 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String)
17:27:33.131 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials_mpCount - [debug,135] - <==      Total: 1
17:27:33.131 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.id,srm.name,srm.element, srm.secondary_element,srm.carbon_element,srm.department_id, srm.production_equipment_id,srm.composition,srm.yield_rate, srm.price,srm.is_custom,srm.category, srm.createuser,srm.createtime,srm.updatetime from standard_raw_materials as srm WHERE (department_id = ? AND name = ? AND category = ?) LIMIT ?
17:27:33.133 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long), 铁矿石 A(String), 矿石类(String), 1(Long)
17:27:33.137 [http-nio-9090-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 1
17:27:33.138 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (id = ?)
17:27:33.138 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(Long)
17:27:33.139 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
17:27:33.140 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
17:27:33.140 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 101(Long)
17:27:33.140 [http-nio-9090-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
18:38:30.395 [HikariPool-1 housekeeper] WARN  c.z.h.p.HikariPool - [run,797] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h6m37s543ms170µs600ns).
18:38:47.550 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
18:38:47.560 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
22:40:14.922 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 39076 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
22:40:14.924 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
22:40:14.925 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
22:40:18.763 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
22:40:19.381 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
22:40:19.383 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:40:19.383 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
22:40:19.430 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:40:19.618 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
22:40:19.664 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@61691801, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
22:40:19.776 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
22:40:19.814 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
22:40:19.846 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
22:40:19.867 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
22:40:19.897 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
22:40:19.936 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
22:40:19.970 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
22:40:19.987 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
22:40:19.987 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
22:40:19.995 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
22:40:20.019 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
22:40:20.038 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
22:40:20.051 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
22:40:20.074 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
22:40:20.098 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
22:40:20.114 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
22:40:20.166 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
22:40:20.189 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
22:40:20.209 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
22:40:20.230 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
22:40:20.240 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
22:40:20.240 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
22:40:20.246 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
22:40:20.257 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
22:40:20.257 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
22:40:20.264 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
22:40:20.280 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
22:40:20.299 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
22:40:20.436 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
22:40:20.446 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
22:40:20.453 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:24
22:40:21.619 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
22:40:21.644 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 7.126 seconds (process running for 14.071)
22:40:21.653 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
22:40:39.297 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:40:41.202 [http-nio-9090-exec-5] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 338 ms
22:54:50.151 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 35068 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
22:54:50.154 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
22:54:50.155 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
22:54:51.179 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
22:54:51.831 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
22:54:51.833 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:54:51.833 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
22:54:51.901 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:54:52.110 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
22:54:52.191 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@58a46502, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
22:54:52.345 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
22:54:52.375 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
22:54:52.393 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
22:54:52.407 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
22:54:52.423 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
22:54:52.446 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
22:54:52.468 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
22:54:52.474 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
22:54:52.474 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
22:54:52.486 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
22:54:52.507 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
22:54:52.522 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
22:54:52.538 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
22:54:52.552 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
22:54:52.563 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
22:54:52.577 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
22:54:52.615 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
22:54:52.627 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
22:54:52.637 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
22:54:52.647 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
22:54:52.650 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
22:54:52.651 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
22:54:52.656 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
22:54:52.660 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
22:54:52.660 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
22:54:52.665 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
22:54:52.675 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
22:54:52.688 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
22:54:52.819 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
22:54:52.827 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
22:54:52.832 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:31
22:54:53.941 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
22:54:53.970 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.181 seconds (process running for 5.057)
22:54:53.978 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
22:54:58.126 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:54:58.950 [http-nio-9090-exec-4] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 393 ms
23:22:54.599 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 40576 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
23:22:54.601 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
23:22:54.602 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
23:22:55.631 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
23:22:56.160 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
23:22:56.161 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:22:56.161 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
23:22:56.206 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:22:56.400 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
23:22:56.443 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@10d546e6, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
23:22:56.558 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
23:22:56.590 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
23:22:56.610 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
23:22:56.632 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
23:22:56.649 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
23:22:56.668 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
23:22:56.686 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
23:22:56.691 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
23:22:56.692 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
23:22:56.702 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
23:22:56.719 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
23:22:56.734 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
23:22:56.749 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
23:22:56.764 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
23:22:56.775 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
23:22:56.786 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
23:22:56.830 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
23:22:56.872 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
23:22:56.913 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
23:22:56.947 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
23:22:56.952 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
23:22:56.953 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
23:22:56.961 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
23:22:56.968 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
23:22:56.968 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
23:22:56.975 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
23:22:56.990 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
23:22:57.007 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
23:22:57.238 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
23:22:57.252 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
23:22:57.266 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:7
23:22:58.985 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
23:22:59.009 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.839 seconds (process running for 6.533)
23:22:59.018 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
23:23:37.360 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:23:38.139 [http-nio-9090-exec-5] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 369 ms
