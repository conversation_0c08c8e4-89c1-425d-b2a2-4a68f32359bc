package com.nercar.ingredient.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nercar.ingredient.domain.po.CalculationResult;
import com.nercar.ingredient.domain.po.StandardIngredientRecords;
import com.nercar.ingredient.domain.vo.StandardIngredientRecordsVO;
import com.nercar.ingredient.domain.vo.StandardRawMaterialsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【standard_ingredient_records(标准配料记录表)】的数据库操作Mapper
* @createDate 2025-04-01 13:55:28
* @Entity com.nercar.ingredient.domain.po.StandardIngredientRecords
*/
@Mapper
public interface StandardIngredientRecordsMapper extends BaseMapper<StandardIngredientRecords> {

    IPage<StandardIngredientRecordsVO> search(Page<StandardIngredientRecordsVO> page, @Param(Constants.WRAPPER) LambdaQueryWrapper<StandardIngredientRecords> wrapper);

    IPage<StandardIngredientRecordsVO> selectRecords(Page<StandardIngredientRecordsVO> page, @Param(Constants.WRAPPER) LambdaQueryWrapper<StandardIngredientRecords> wrapper);


    List<StandardRawMaterialsVO> selectMaterials( Long calculationResultId);
}





