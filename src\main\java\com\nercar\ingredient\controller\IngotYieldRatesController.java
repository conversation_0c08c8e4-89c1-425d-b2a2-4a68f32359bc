package com.nercar.ingredient.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.nercar.ingredient.domain.dto.IngotYieldRatesPageDTO;
import com.nercar.ingredient.domain.dto.MaterialYieldRatesIdsDTO;
import com.nercar.ingredient.domain.po.IngotYieldRates;
import com.nercar.ingredient.excel.IngotYieldRatesExcel;
import com.nercar.ingredient.response.PageDataResult;
import com.nercar.ingredient.response.Result;
import com.nercar.ingredient.service.IngotYieldRatesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Tag(name = "成锭率")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/ingotYieldRates")
public class IngotYieldRatesController {

   @Resource
   private IngotYieldRatesService ingotYieldRatesService;

    @Operation(summary = "成锭率查询")
    @PostMapping("/getIngotYieldRates")
    public PageDataResult<IngotYieldRates> getIngotYieldRates(@RequestBody IngotYieldRatesPageDTO ingotYieldRatesPageDTO) {
        log.info("接收到成锭率分页查询请求，参数：{}", ingotYieldRatesPageDTO);
        IPage<IngotYieldRates> page = ingotYieldRatesService.getIngotYieldRatesPage(ingotYieldRatesPageDTO);
        log.info("成锭率分页查询完成，总记录数：{}", page.getTotal());
        return PageDataResult.success(page.getRecords(), (int) page.getTotal());
    }

    @Operation(summary = "成锭率新增")
    @PostMapping("/aveIngotYieldRates")
    public Result<Boolean> getIngotYieldRatessave(@RequestBody IngotYieldRates ingotYieldRates) {
        log.info("接收到成锭率新增请求，参数：{}", ingotYieldRates);
        boolean result = ingotYieldRatesService.save(ingotYieldRates);
        log.info("成锭率新增完成，结果：{}", result);
        return Result.success(result);
    }


    @Operation(summary = "成锭率详情")
    @GetMapping("/getIngotYieldRates/{id}")
    public Result<IngotYieldRates> getIngotYieldRatesId(@PathVariable String id) {
        log.info("接收到成锭率详情查询请求，ID：{}", id);
        IngotYieldRates result = ingotYieldRatesService.getById(Long.parseLong(id));
        log.info("成锭率详情查询完成，结果：{}", result);
        return Result.success(result);
    }

    @Operation(summary = "成锭率编辑")
    @PostMapping("/updateIngotYieldRates")
    public Result<Boolean> updateIngotYieldRates(@RequestBody IngotYieldRates ingotYieldRates) {
        log.info("接收到成锭率更新请求，参数：{}", ingotYieldRates);
        boolean result = ingotYieldRatesService.updateById(ingotYieldRates);
        log.info("成锭率更新完成，结果：{}", result);
        return Result.success(result);
    }


    @Operation(summary = "成锭率删除")
    @GetMapping("/deleteIngotYieldRates/{id}")
    public Result<Boolean> deleteIngotYieldRates(@PathVariable String id) {
        log.info("接收到成锭率删除请求，ID：{}", id);
        boolean result = ingotYieldRatesService.removeById(Long.parseLong(id));
        log.info("成锭率删除完成，结果：{}", result);
        return Result.success(result);
    }

    @Operation(summary = "导出")
    @PostMapping("/excel/export")
    public ResponseEntity<byte[]> export(@RequestBody MaterialYieldRatesIdsDTO materialYieldRatesIdsDTO) throws IOException {
        log.info("接收到成锭率导出请求，参数：{}", materialYieldRatesIdsDTO);
        if (CollectionUtils.isEmpty(materialYieldRatesIdsDTO.getIds())) {
            log.error("导出失败：ID列表为空");
            throw new RuntimeException("不能传空");
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
        String timeStamp = dateFormat.format(new Date());
        String filename = "excel_" + timeStamp;

        List<IngotYieldRates> resList = ingotYieldRatesService.listByIds(materialYieldRatesIdsDTO.getIds());
        log.info("查询到待导出数据条数：{}", resList.size());

        List<IngotYieldRatesExcel> newList = new ArrayList<>();
        for (IngotYieldRates item : resList) {
            IngotYieldRatesExcel excelItem = new IngotYieldRatesExcel();
            BeanUtils.copyProperties(item, excelItem);
            if (item.getId() != null) {
                excelItem.setId(String.valueOf(item.getId()));
            }
            newList.add(excelItem);
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        EasyExcel.write(outputStream, IngotYieldRatesExcel.class).sheet("成锭率").doWrite(newList);
        byte[] excelByteArray = outputStream.toByteArray();

        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.CONTENT_ENCODING, "UTF-8");
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", filename + ".xlsx");

        log.info("成锭率导出完成，文件名：{}.xlsx", filename);
        return new ResponseEntity<>(excelByteArray, headers, HttpStatus.OK);
    }

}
