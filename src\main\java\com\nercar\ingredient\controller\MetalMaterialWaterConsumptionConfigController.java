package com.nercar.ingredient.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nercar.ingredient.response.PageDataResult;
import com.nercar.ingredient.response.Result;
import com.nercar.ingredient.domain.dto.MetalMaterialWaterConsumptionConfigDTO;
import com.nercar.ingredient.domain.vo.MetalMaterialWaterConsumptionConfigVO;
import com.nercar.ingredient.service.MetalMaterialWaterConsumptionConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 金属料吨水单耗配置Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping("/metalMaterialWaterConsumptionConfig")
@Tag(name = "金属料吨水单耗配置管理", description = "金属料吨水单耗配置的增删改查接口")
@Slf4j
public class MetalMaterialWaterConsumptionConfigController {

    @Autowired
    private MetalMaterialWaterConsumptionConfigService configService;

    @Operation(summary = "新增金属料吨水单耗配置")
    @PostMapping("/add")
    public Result<String> addConfig(@RequestBody MetalMaterialWaterConsumptionConfigDTO configDTO) {
        try {
            log.info("接收到新增金属料吨水单耗配置请求，参数: {}", configDTO);
            
            boolean result = configService.addConfig(configDTO);
            
            if (result) {
                log.info("新增金属料吨水单耗配置成功");
                return Result.ok("新增配置成功");
            } else {
                log.error("新增金属料吨水单耗配置失败");
                return Result.failed("新增配置失败");
            }
        } catch (Exception e) {
            log.error("新增金属料吨水单耗配置异常", e);
            return Result.failed("新增配置失败: " + e.getMessage());
        }
    }

    @Operation(summary = "删除金属料吨水单耗配置")
    @PostMapping("/delete/{id}")
    public Result<String> deleteConfig(
            @Parameter(description = "配置ID", required = true)
            @PathVariable("id") Long id) {
        try {
            log.info("接收到删除金属料吨水单耗配置请求，ID: {}", id);
            
            boolean result = configService.deleteConfig(id);
            
            if (result) {
                log.info("删除金属料吨水单耗配置成功，ID: {}", id);
                return Result.ok("删除配置成功");
            } else {
                log.error("删除金属料吨水单耗配置失败，ID: {}", id);
                return Result.failed("删除配置失败");
            }
        } catch (Exception e) {
            log.error("删除金属料吨水单耗配置异常，ID: {}", id, e);
            return Result.failed("删除配置失败: " + e.getMessage());
        }
    }

    @Operation(summary = "更新金属料吨水单耗配置")
    @PostMapping("/update")
    public Result<String> updateConfig(@RequestBody MetalMaterialWaterConsumptionConfigDTO configDTO) {
        try {
            log.info("接收到更新金属料吨水单耗配置请求，参数: {}", configDTO);
            
            boolean result = configService.updateConfig(configDTO);
            
            if (result) {
                log.info("更新金属料吨水单耗配置成功，ID: {}", configDTO.getId());
                return Result.ok("更新配置成功");
            } else {
                log.error("更新金属料吨水单耗配置失败，ID: {}", configDTO.getId());
                return Result.failed("更新配置失败");
            }
        } catch (Exception e) {
            log.error("更新金属料吨水单耗配置异常", e);
            return Result.failed("更新配置失败: " + e.getMessage());
        }
    }

    @Operation(summary = "分页查询金属料吨水单耗配置列表")
    @PostMapping("/list")
    public PageDataResult<MetalMaterialWaterConsumptionConfigVO> getConfigList(
            @RequestBody MetalMaterialWaterConsumptionConfigDTO configDTO) {
        try {
            log.info("接收到分页查询金属料吨水单耗配置列表请求，参数: {}", configDTO);
            
            IPage<MetalMaterialWaterConsumptionConfigVO> result = configService.getConfigList(configDTO);
            
            List<MetalMaterialWaterConsumptionConfigVO> records = result.getRecords();
            long total = result.getTotal();
            
            log.info("分页查询金属料吨水单耗配置列表成功，总数: {}", total);
            return PageDataResult.success(records, total);
            
        } catch (Exception e) {
            log.error("分页查询金属料吨水单耗配置列表异常", e);
            return PageDataResult.success("查询配置列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据ID查询金属料吨水单耗配置详情")
    @GetMapping("/detail/{id}")
    public Result<MetalMaterialWaterConsumptionConfigVO> getConfigById(
            @Parameter(description = "配置ID", required = true) 
            @PathVariable("id") Long id) {
        try {
            log.info("接收到查询金属料吨水单耗配置详情请求，ID: {}", id);
            
            MetalMaterialWaterConsumptionConfigVO result = configService.getConfigById(id);
            
            if (result != null) {
                log.info("查询金属料吨水单耗配置详情成功，ID: {}", id);
                return Result.ok(result);
            } else {
                log.warn("配置不存在，ID: {}", id);
                return Result.failed("配置不存在");
            }
        } catch (Exception e) {
            log.error("查询金属料吨水单耗配置详情异常，ID: {}", id, e);
            return Result.failed("查询配置详情失败: " + e.getMessage());
        }
    }
}
