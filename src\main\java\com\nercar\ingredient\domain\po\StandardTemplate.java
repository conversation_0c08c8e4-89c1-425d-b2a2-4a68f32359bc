package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 标准配料模版表
 * @TableName standard_template
 */
@TableName(value ="standard_template")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StandardTemplate extends BaseEntity {
    /**
     * 主键
     */
    @TableId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 路径名称
     */
    private String pathName;

    /**
     * 备注
     */
    private String remark;

    private String device;
    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 模板评级：1-5星级
     */
    @Schema(description = "模板评级：1-5星级")
    private Integer rating;

    private String departmentName;

    /**
     * 配料人
     */
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    private String createuser;

    /**
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;



}