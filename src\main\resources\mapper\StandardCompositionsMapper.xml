<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.StandardCompositionsMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.StandardCompositions">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="elementName" column="element_name" jdbcType="VARCHAR"/>
            <result property="minValue" column="min_value" jdbcType="NUMERIC"/>
            <result property="maxValue" column="max_value" jdbcType="NUMERIC"/>
            <result property="standardId" column="standard_id" jdbcType="BIGINT"/>
            <result property="isCustomize" column="is_customize" jdbcType="VARCHAR"/>
            <result property="createuser" column="createuser" jdbcType="VARCHAR"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,element_name,min_value,
        max_value,standard_id,is_customize,
        createuser,createtime,updatetime
    </sql>
</mapper>
