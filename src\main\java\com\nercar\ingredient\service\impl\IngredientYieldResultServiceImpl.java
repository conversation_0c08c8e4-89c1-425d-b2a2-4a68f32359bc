package com.nercar.ingredient.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.po.IngredientYieldResult;
import com.nercar.ingredient.mapper.IngredientYieldResultMapper;
import com.nercar.ingredient.service.IngredientYieldResultService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【ingredient_yield_result(配料单成材率)】的数据库操作Service实现
* @createDate 2025-04-01 13:55:28
*/
@Service
public class IngredientYieldResultServiceImpl extends ServiceImpl<IngredientYieldResultMapper, IngredientYieldResult>
    implements IngredientYieldResultService{

}




