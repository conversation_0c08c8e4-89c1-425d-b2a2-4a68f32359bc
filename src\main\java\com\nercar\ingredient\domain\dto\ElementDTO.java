package com.nercar.ingredient.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ElementDTO extends BaseDTO{
    /**
     * 类型(成分上限/成分下限/目标值)
     */
    @Schema(description = "类型(成分上限/成分下限/目标值)")
    private String type;

    @JsonProperty("C")
    @TableField("\"C\"")
    @Schema(description = "C元素含量", name = "C")
    private BigDecimal C;

    @JsonProperty("Mn")
    @TableField("\"Mn\"")
    @Schema(description = "Mn元素含量", name = "Mn")
    private BigDecimal Mn;

    @JsonProperty("Si")
    @TableField("\"Si\"")
    @Schema(description = "Si元素含量", name = "Si")
    private BigDecimal Si;

    @JsonProperty("P")
    @TableField("\"P\"")
    @Schema(description = "P元素含量", name = "P")
    private BigDecimal P;

    @JsonProperty("Cr")
    @TableField("\"Cr\"")
    @Schema(description = "Cr元素含量", name = "Cr")
    private BigDecimal Cr;

    @JsonProperty("V")
    @TableField("\"V\"")
    @Schema(description = "V元素含量", name = "V")
    private BigDecimal V;

    @JsonProperty("Mo")
    @TableField("\"Mo\"")
    @Schema(description = "Mo元素含量", name = "Mo")
    private BigDecimal Mo;

    @JsonProperty("Ni")
    @TableField("\"Ni\"")
    @Schema(description = "Ni元素含量", name = "Ni")
    private BigDecimal Ni;

    @JsonProperty("W")
    @TableField("\"W\"")
    @Schema(description = "W元素含量", name = "W")
    private BigDecimal W;

    @JsonProperty("Cu")
    @TableField("\"Cu\"")
    @Schema(description = "Cu元素含量", name = "Cu")
    private BigDecimal Cu;

    @JsonProperty("Ti")
    @TableField("\"Ti\"")
    @Schema(description = "Ti元素含量", name = "Ti")
    private BigDecimal Ti;

    @JsonProperty("Nb")
    @TableField("\"Nb\"")
    @Schema(description = "Nb元素含量", name = "Nb")
    private BigDecimal Nb;

    @JsonProperty("Co")
    @TableField("\"Co\"")
    @Schema(description = "Co元素含量", name = "Co")
    private BigDecimal Co;

    @JsonProperty("S")
    @TableField("\"S\"")
    @Schema(description = "S元素含量", name = "S")
    private BigDecimal S;

    @JsonProperty("Sn")
    @TableField("\"Sn\"")
    @Schema(description = "Sn元素含量", name = "Sn")
    private BigDecimal Sn;

    @JsonProperty("Al")
    @TableField("\"Al\"")
    @Schema(description = "Al元素含量", name = "Al")
    private BigDecimal Al;

    @JsonProperty("Fe")
    @TableField("\"Fe\"")
    @Schema(description = "Fe元素含量", name = "Fe")
    private BigDecimal Fe;

    @JsonProperty("B")
    @TableField("\"B\"")
    @Schema(description = "B元素含量", name = "B")
    private BigDecimal B;

    @JsonProperty("Zr")
    @TableField("\"Zr\"")
    @Schema(description = "Zr元素含量", name = "Zr")
    private BigDecimal Zr;

    @JsonProperty("La")
    @TableField("\"La\"")
    @Schema(description = "La元素含量", name = "La")
    private BigDecimal La;

    @JsonProperty("Ce")
    @TableField("\"Ce\"")
    @Schema(description = "Ce元素含量", name = "Ce")
    private BigDecimal Ce;

    @JsonProperty("Ca")
    @TableField("\"Ca\"")
    @Schema(description = "Ca元素含量", name = "Ca")
    private BigDecimal Ca;

    @JsonProperty("Pb")
    @TableField("\"Pb\"")
    @Schema(description = "Pb元素含量", name = "Pb")
    private BigDecimal Pb;

    @JsonProperty("Bi")
    @TableField("\"Bi\"")
    @Schema(description = "Bi元素含量", name = "Bi")
    private BigDecimal Bi;

    @JsonProperty("Sb")
    @TableField("\"Sb\"")
    @Schema(description = "Sb元素含量", name = "Sb")
    private BigDecimal Sb;

    @JsonProperty("As")
    @TableField("\"As\"")
    @Schema(description = "As元素含量", name = "As")
    private BigDecimal As;

    @JsonProperty("Als")
    @TableField("\"Als\"")
    @Schema(description = "Als元素含量", name = "Als")
    private BigDecimal Als;

    @JsonProperty("Ta")
    @TableField("\"Ta\"")
    @Schema(description = "Ta元素含量", name = "Ta")
    private BigDecimal Ta;

    @JsonProperty("Mg")
    @TableField("\"Mg\"")
    @Schema(description = "Mg元素含量", name = "Mg")
    private BigDecimal Mg;

    @JsonProperty("Ag")
    @TableField("\"Ag\"")
    @Schema(description = "Ag元素含量", name = "Ag")
    private BigDecimal Ag;

    @JsonProperty("Hg")
    @TableField("\"Hg\"")
    @Schema(description = "Hg元素含量", name = "Hg")
    private BigDecimal Hg;

    @JsonProperty("Cd")
    @TableField("\"Cd\"")
    @Schema(description = "Cd元素含量", name = "Cd")
    private BigDecimal Cd;

    @JsonProperty("Zn")
    @TableField("\"Zn\"")
    @Schema(description = "Zn元素含量", name = "Zn")
    private BigDecimal Zn;

    @JsonProperty("Te")
    @TableField("\"Te\"")
    @Schema(description = "Te元素含量", name = "Te")
    private BigDecimal Te;

    @JsonProperty("Se")
    @TableField("\"Se\"")
    @Schema(description = "Se元素含量", name = "Se")
    private BigDecimal Se;

    @JsonProperty("Pr")
    @TableField("\"Pr\"")
    @Schema(description = "Pr元素含量", name = "Pr")
    private BigDecimal Pr;

    @JsonProperty("Nd")
    @TableField("\"Nd\"")
    @Schema(description = "Nd元素含量", name = "Nd")
    private BigDecimal Nd;

    @JsonProperty("Sc")
    @TableField("\"Sc\"")
    @Schema(description = "Sc元素含量", name = "Sc")
    private BigDecimal Sc;

    @JsonProperty("Y")
    @TableField("\"Y\"")
    @Schema(description = "Y元素含量", name = "Y")
    private BigDecimal Y;

    @JsonProperty("Hf")
    @TableField("\"Hf\"")
    @Schema(description = "Hf元素含量", name = "Hf")
    private BigDecimal Hf;

    @JsonProperty("Pcm")
    @TableField("\"Pcm\"")
    @Schema(description = "Pcm元素含量", name = "Pcm")
    private BigDecimal Pcm;

    @JsonProperty("H")
    @TableField("\"H\"")
    @Schema(description = "H元素含量", name = "H")
    private BigDecimal H;

    @JsonProperty("O")
    @TableField("\"O\"")
    @Schema(description = "O元素含量", name = "O")
    private BigDecimal O;

    @JsonProperty("N")
    @TableField("\"N\"")
    @Schema(description = "N元素含量", name = "N")
    private BigDecimal N;
}
