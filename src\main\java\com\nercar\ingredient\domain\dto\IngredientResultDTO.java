package com.nercar.ingredient.domain.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IngredientResultDTO extends BaseDTO {

    /**
     * standardIngredientRecordId 配料单ID
     */
    private Long standardIngredientRecordId;
    /**
     * calculationResultId 计算结果ID
     */
    private Long calculationResultId;
}
