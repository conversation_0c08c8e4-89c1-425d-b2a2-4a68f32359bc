package com.nercar.ingredient.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.ingredient.domain.dto.MetalMaterialWaterConsumptionConfigDTO;
import com.nercar.ingredient.domain.po.MetalMaterialWaterConsumptionConfig;
import com.nercar.ingredient.domain.vo.MetalMaterialWaterConsumptionConfigVO;

/**
 * 金属料吨水单耗配置Service接口
 * <AUTHOR>
 */
public interface MetalMaterialWaterConsumptionConfigService extends IService<MetalMaterialWaterConsumptionConfig> {

    /**
     * 新增金属料吨水单耗配置
     * @param configDTO 配置信息
     * @return 是否成功
     */
    boolean addConfig(MetalMaterialWaterConsumptionConfigDTO configDTO);

    /**
     * 删除金属料吨水单耗配置
     * @param id 配置ID
     * @return 是否成功
     */
    boolean deleteConfig(Long id);

    /**
     * 更新金属料吨水单耗配置
     * @param configDTO 配置信息
     * @return 是否成功
     */
    boolean updateConfig(MetalMaterialWaterConsumptionConfigDTO configDTO);

    /**
     * 分页查询金属料吨水单耗配置列表
     * @param configDTO 查询条件
     * @return 分页结果
     */
    IPage<MetalMaterialWaterConsumptionConfigVO> getConfigList(MetalMaterialWaterConsumptionConfigDTO configDTO);

    /**
     * 根据ID获取配置详情
     * @param id 配置ID
     * @return 配置详情
     */
    MetalMaterialWaterConsumptionConfigVO getConfigById(Long id);
}
