package com.nercar.ingredient.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.dto.MetalMaterialWaterConsumptionConfigDTO;
import com.nercar.ingredient.domain.po.MetalMaterialWaterConsumptionConfig;
import com.nercar.ingredient.domain.vo.MetalMaterialWaterConsumptionConfigVO;
import com.nercar.ingredient.mapper.MetalMaterialWaterConsumptionConfigMapper;
import com.nercar.ingredient.service.MetalMaterialWaterConsumptionConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 金属料吨水单耗配置Service实现类
 * <AUTHOR>
 */
@Service
@Slf4j
public class MetalMaterialWaterConsumptionConfigServiceImpl 
        extends ServiceImpl<MetalMaterialWaterConsumptionConfigMapper, MetalMaterialWaterConsumptionConfig>
        implements MetalMaterialWaterConsumptionConfigService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addConfig(MetalMaterialWaterConsumptionConfigDTO configDTO) {
        try {
            log.info("新增金属料吨水单耗配置，参数: {}", configDTO);
            
            // 参数验证
            if (configDTO.getMetalMaterialWaterConsumption() == null) {
                throw new IllegalArgumentException("金属料吨水单耗不能为空");
            }
            if (configDTO.getMethod() == null || configDTO.getMethod().trim().isEmpty()) {
                throw new IllegalArgumentException("冶炼方法不能为空");
            }
            if (configDTO.getDevice() == null || configDTO.getDevice().trim().isEmpty()) {
                throw new IllegalArgumentException("冶炼设备不能为空");
            }
            
            // DTO转PO
            MetalMaterialWaterConsumptionConfig config = BeanUtil.copyProperties(configDTO, MetalMaterialWaterConsumptionConfig.class);
            
            // 保存到数据库
            boolean result = this.save(config);
            
            if (result) {
                log.info("新增金属料吨水单耗配置成功，ID: {}", config.getId());
            } else {
                log.error("新增金属料吨水单耗配置失败");
            }
            
            return result;
        } catch (Exception e) {
            log.error("新增金属料吨水单耗配置异常", e);
            throw new RuntimeException("新增配置失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteConfig(Long id) {
        try {
            log.info("删除金属料吨水单耗配置，ID: {}", id);
            
            if (id == null) {
                throw new IllegalArgumentException("配置ID不能为空");
            }
            
            // 检查配置是否存在
            MetalMaterialWaterConsumptionConfig existConfig = this.getById(id);
            if (existConfig == null) {
                throw new IllegalArgumentException("配置不存在，ID: " + id);
            }
            
            // 删除配置
            boolean result = this.removeById(id);
            
            if (result) {
                log.info("删除金属料吨水单耗配置成功，ID: {}", id);
            } else {
                log.error("删除金属料吨水单耗配置失败，ID: {}", id);
            }
            
            return result;
        } catch (Exception e) {
            log.error("删除金属料吨水单耗配置异常，ID: {}", id, e);
            throw new RuntimeException("删除配置失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateConfig(MetalMaterialWaterConsumptionConfigDTO configDTO) {
        try {
            log.info("更新金属料吨水单耗配置，参数: {}", configDTO);
            
            // 参数验证
            if (configDTO.getId() == null) {
                throw new IllegalArgumentException("配置ID不能为空");
            }
            if (configDTO.getMetalMaterialWaterConsumption() == null) {
                throw new IllegalArgumentException("金属料吨水单耗不能为空");
            }
            if (configDTO.getMethod() == null || configDTO.getMethod().trim().isEmpty()) {
                throw new IllegalArgumentException("冶炼方法不能为空");
            }
            if (configDTO.getDevice() == null || configDTO.getDevice().trim().isEmpty()) {
                throw new IllegalArgumentException("冶炼设备不能为空");
            }
            
            // 检查配置是否存在
            MetalMaterialWaterConsumptionConfig existConfig = this.getById(configDTO.getId());
            if (existConfig == null) {
                throw new IllegalArgumentException("配置不存在，ID: " + configDTO.getId());
            }
            
            // DTO转PO
            MetalMaterialWaterConsumptionConfig config = BeanUtil.copyProperties(configDTO, MetalMaterialWaterConsumptionConfig.class);
            
            // 更新配置
            boolean result = this.updateById(config);
            
            if (result) {
                log.info("更新金属料吨水单耗配置成功，ID: {}", config.getId());
            } else {
                log.error("更新金属料吨水单耗配置失败，ID: {}", config.getId());
            }
            
            return result;
        } catch (Exception e) {
            log.error("更新金属料吨水单耗配置异常", e);
            throw new RuntimeException("更新配置失败: " + e.getMessage());
        }
    }

    @Override
    public IPage<MetalMaterialWaterConsumptionConfigVO> getConfigList(MetalMaterialWaterConsumptionConfigDTO configDTO) {
        try {
            log.info("分页查询金属料吨水单耗配置列表，参数: {}", configDTO);
            
            // 创建分页对象
            Page<MetalMaterialWaterConsumptionConfigVO> page = new Page<>(
                    configDTO.getPageNo() != null ? configDTO.getPageNo() : 1,
                    configDTO.getPageSize() != null ? configDTO.getPageSize() : 10
            );
            
            // 执行分页查询
            IPage<MetalMaterialWaterConsumptionConfigVO> result = baseMapper.selectConfigPage(
                    page, 
                    configDTO.getMethod(), 
                    configDTO.getDevice()
            );
            
            log.info("查询金属料吨水单耗配置列表成功，总数: {}", result.getTotal());
            return result;
            
        } catch (Exception e) {
            log.error("查询金属料吨水单耗配置列表异常", e);
            throw new RuntimeException("查询配置列表失败: " + e.getMessage());
        }
    }

    @Override
    public MetalMaterialWaterConsumptionConfigVO getConfigById(Long id) {
        try {
            log.info("根据ID查询金属料吨水单耗配置详情，ID: {}", id);
            
            if (id == null) {
                throw new IllegalArgumentException("配置ID不能为空");
            }
            
            // 查询配置
            MetalMaterialWaterConsumptionConfig config = this.getById(id);
            if (config == null) {
                log.warn("配置不存在，ID: {}", id);
                return null;
            }
            
            // PO转VO
            MetalMaterialWaterConsumptionConfigVO configVO = BeanUtil.copyProperties(config, MetalMaterialWaterConsumptionConfigVO.class);
            
            log.info("查询金属料吨水单耗配置详情成功，ID: {}", id);
            return configVO;
            
        } catch (Exception e) {
            log.error("查询金属料吨水单耗配置详情异常，ID: {}", id, e);
            throw new RuntimeException("查询配置详情失败: " + e.getMessage());
        }
    }
}
