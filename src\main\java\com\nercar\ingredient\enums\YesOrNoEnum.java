package com.nercar.ingredient.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

public enum YesOrNoEnum {
    YES(1, "是"),
    NO(0, "否");

    @EnumValue
    private final int code;
    @JsonValue
    private final String name;

    YesOrNoEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
