-- 历史配料查询功能验证脚本
-- 执行时间：2025-01-14

-- 1. 查看standard_ingredient_records表的数据分布
SELECT 
    '总配料记录数' as metric,
    COUNT(*) as count
FROM standard_ingredient_records
UNION ALL
SELECT 
    '历史配料记录数(status=1)',
    COUNT(*)
FROM standard_ingredient_records 
WHERE status = 1
UNION ALL
SELECT 
    '草稿配料记录数(status=0)',
    COUNT(*)
FROM standard_ingredient_records 
WHERE status = 0;

-- 2. 按工艺路径ID统计配料记录分布
SELECT 
    process_path_id,
    COUNT(*) as record_count,
    COUNT(DISTINCT steel_grade_id) as steel_grade_count,
    MIN(mixing_date) as earliest_date,
    MAX(mixing_date) as latest_date
FROM standard_ingredient_records 
WHERE status = 1
GROUP BY process_path_id
ORDER BY record_count DESC
LIMIT 10;

-- 3. 按钢种统计配料记录分布
SELECT 
    sg.steel_grade,
    air.steel_grade_id,
    COUNT(*) as record_count,
    COUNT(DISTINCT air.process_path_id) as process_path_count
FROM standard_ingredient_records air
LEFT JOIN steel_grades sg ON air.steel_grade_id = sg.id
WHERE air.status = 1
GROUP BY sg.steel_grade, air.steel_grade_id
ORDER BY record_count DESC
LIMIT 10;

-- 4. 验证钢种+工艺路径的组合查询
SELECT 
    sg.steel_grade,
    air.process_path_id,
    COUNT(*) as record_count,
    STRING_AGG(DISTINCT air.user_name, ', ') as users,
    MIN(air.mixing_date) as earliest_date,
    MAX(air.mixing_date) as latest_date
FROM standard_ingredient_records air
LEFT JOIN steel_grades sg ON air.steel_grade_id = sg.id
WHERE air.status = 1
GROUP BY sg.steel_grade, air.process_path_id
HAVING COUNT(*) > 1
ORDER BY record_count DESC;

-- 5. 模拟新接口的查询逻辑（钢种：18CrNiMo7-6+HH）
SELECT 
    air.id,
    air.status,
    air.user_name,
    air.department_id,
    air.steel_grade_id,
    air.process_path_id,
    air.raw_material_total,
    air.cost_price,
    air.mixing_date,
    air.release_date,
    air.category,
    air.special_notes,
    air.createuser,
    air.createtime,
    air.updatetime,
    sg.steel_grade as steelGrade,
    es.standard_name as standardName
FROM standard_ingredient_records air
LEFT JOIN steel_grades sg ON air.steel_grade_id = sg.id
LEFT JOIN execution_standard es ON air.execution_standard_id = es.id
WHERE air.status = 1
  AND sg.steel_grade LIKE '%18CrNiMo7-6+HH%'
ORDER BY air.mixing_date DESC;

-- 6. 模拟新接口的查询逻辑（钢种+工艺路径）
SELECT 
    air.id,
    air.status,
    air.user_name,
    air.steel_grade_id,
    air.process_path_id,
    air.mixing_date,
    air.special_notes,
    sg.steel_grade as steelGrade,
    es.standard_name as standardName
FROM standard_ingredient_records air
LEFT JOIN steel_grades sg ON air.steel_grade_id = sg.id
LEFT JOIN execution_standard es ON air.execution_standard_id = es.id
WHERE air.status = 1
  AND sg.steel_grade LIKE '%18CrNiMo7-6+HH%'
  AND air.process_path_id = 1917153489721815042
ORDER BY air.mixing_date DESC;

-- 7. 验证查询性能差异
-- 7.1 只按钢种查询的性能
EXPLAIN (ANALYZE, BUFFERS) 
SELECT COUNT(*)
FROM standard_ingredient_records air
LEFT JOIN steel_grades sg ON air.steel_grade_id = sg.id
WHERE air.status = 1
  AND sg.steel_grade LIKE '%18CrNiMo7-6+HH%';

-- 7.2 按钢种+工艺路径查询的性能
EXPLAIN (ANALYZE, BUFFERS) 
SELECT COUNT(*)
FROM standard_ingredient_records air
LEFT JOIN steel_grades sg ON air.steel_grade_id = sg.id
WHERE air.status = 1
  AND sg.steel_grade LIKE '%18CrNiMo7-6+HH%'
  AND air.process_path_id = 1917153489721815042;

-- 8. 检查数据完整性
SELECT 
    '有process_path_id的记录数' as check_item,
    COUNT(*) as count
FROM standard_ingredient_records 
WHERE process_path_id IS NOT NULL
UNION ALL
SELECT 
    'process_path_id为NULL的记录数',
    COUNT(*)
FROM standard_ingredient_records 
WHERE process_path_id IS NULL;

-- 9. 验证工艺路径ID的有效性
SELECT 
    air.process_path_id,
    COUNT(*) as usage_count,
    CASE 
        WHEN pp.id IS NOT NULL THEN '有效'
        ELSE '无效或已删除'
    END as validity_status
FROM standard_ingredient_records air
LEFT JOIN process_path pp ON air.process_path_id = pp.id
WHERE air.process_path_id IS NOT NULL
GROUP BY air.process_path_id, pp.id
ORDER BY usage_count DESC;

-- 验证完成提示
SELECT '历史配料查询功能验证脚本执行完成' as status;
