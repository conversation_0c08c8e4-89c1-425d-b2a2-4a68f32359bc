<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.MaterialYieldRatesMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.MaterialYieldRates">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="productionDept" column="production_dept" jdbcType="VARCHAR"/>
            <result property="lineName" column="line_name" jdbcType="VARCHAR"/>
            <result property="materialCategoryName" column="material_category_name" jdbcType="VARCHAR"/>
            <result property="productCategory" column="product_category" jdbcType="VARCHAR"/>
            <result property="steelClass" column="steel_class" jdbcType="VARCHAR"/>
            <result property="steelGrade" column="steel_grade" jdbcType="VARCHAR"/>
            <result property="specifications" column="specifications" jdbcType="VARCHAR"/>
            <result property="materialYield" column="material_yield" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,production_dept,line_name,
        material_category_name,product_category,steel_class,
        steel_grade,specifications,material_yield
    </sql>
    <select id="selectMaterialYieldRates" resultType="com.nercar.ingredient.domain.po.MaterialYieldRates">
        select * from material_yield_rates
        ${ew.customSqlSegment}
    </select>
</mapper>
