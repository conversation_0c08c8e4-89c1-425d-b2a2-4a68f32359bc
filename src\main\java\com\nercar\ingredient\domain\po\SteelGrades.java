package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 钢种表
 * @TableName steel_grades
 */
@TableName(value ="steel_grades")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SteelGrades extends BaseEntity {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 钢种
     */
    @TableField(value = "steel_grade")
    private String steelGrade;

    /**
     * 配料人
     */
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    private String createuser;

    /**
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;




}