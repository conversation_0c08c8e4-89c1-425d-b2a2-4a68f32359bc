<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.ProductionFactoryMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.ProductionFactory">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="imagesUrl" column="images_url" jdbcType="VARCHAR"/>
            <result property="createuser" column="createuser" jdbcType="VARCHAR"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,images_url,
        createuser,createtime,updatetime
    </sql>
</mapper>
