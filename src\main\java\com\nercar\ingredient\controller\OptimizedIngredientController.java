package com.nercar.ingredient.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.nercar.ingredient.domain.dto.IngredientResultIdsDTO;
import com.nercar.ingredient.domain.dto.OptimizedIngredientDTO;
import com.nercar.ingredient.domain.vo.AllDataVO;
import com.nercar.ingredient.domain.vo.PurposeCompositionsVO;
import com.nercar.ingredient.domain.vo.StandardIngredientRecordsResultVO;
import com.nercar.ingredient.domain.vo.StandardRawMaterialsVO;
import com.nercar.ingredient.excel.PurposeCompositionsExcel;
import com.nercar.ingredient.excel.StandardRawMaterialsExcel;
import com.nercar.ingredient.response.Result;
import com.nercar.ingredient.service.StandardIngredientRecordsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Tag(name = "优化配料接口")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/optimizedIngredient")
public class OptimizedIngredientController {

    @Autowired
    private StandardIngredientRecordsService standardIngredientRecordsService;

    @Operation(summary = "导出配料信息")
    @PostMapping("/excel")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<byte[]> export(@RequestBody OptimizedIngredientDTO optimizedIngredientDTO) {
        Long id = optimizedIngredientDTO.getId();
        if (id == null) {
            throw new RuntimeException("不能传空");
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
        String timeStamp = dateFormat.format(new Date());
        String filename = "excel_" + timeStamp;

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            StandardIngredientRecordsResultVO standardIngredientRecordsResultVO = standardIngredientRecordsService.previewIngredient(id);

            // 处理 StandardRawMaterialsVO 数据
            List<StandardRawMaterialsVO> standardRawMaterialsVOList = standardIngredientRecordsResultVO.getStandardRawMaterialsVOList();
            List<StandardRawMaterialsExcel> newRawMaterialsList = new ArrayList<>();
            for (StandardRawMaterialsVO item : standardRawMaterialsVOList) {
                StandardRawMaterialsExcel excelItem = new StandardRawMaterialsExcel();
                BeanUtils.copyProperties(item, excelItem);
//                excelItem.setSingleConsume(item.getWieght());
                if (item.getId() != null) {
                    excelItem.setId(String.valueOf(item.getId()));
                }
                excelItem.setWieght(item.getWieght());
                excelItem.setProductionCost(item.getCost());
                excelItem.setAverageValue(item.getAverageValue());
                excelItem.setSingleConsume(item.getSingleConsume());
                newRawMaterialsList.add(excelItem);
            }

            // 处理 PurposeCompositionsVO 数据
            List<PurposeCompositionsVO> purposeCompositionsVOList = standardIngredientRecordsResultVO.getPurposeCompositionsVOList();
            List<PurposeCompositionsExcel> newPurposeCompositionsList = new ArrayList<>();
            for (PurposeCompositionsVO item : purposeCompositionsVOList) {
                PurposeCompositionsExcel excelItem = new PurposeCompositionsExcel();
                BeanUtils.copyProperties(item, excelItem);
                if (item.getId() != null) {
                    excelItem.setId(String.valueOf(item.getId()));
                }
                newPurposeCompositionsList.add(excelItem);
            }

            // 使用 ExcelWriter 实现多表写入
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
            WriteSheet rawMaterialsSheet = EasyExcel.writerSheet("配料信息").head(StandardRawMaterialsExcel.class).build();
            excelWriter.write(newRawMaterialsList, rawMaterialsSheet);

            WriteSheet purposeCompositionsSheet = EasyExcel.writerSheet("目标成分").head(PurposeCompositionsExcel.class).build();
            excelWriter.write(newPurposeCompositionsList, purposeCompositionsSheet);

            excelWriter.finish();

            byte[] excelByteArray = outputStream.toByteArray();

            HttpHeaders headers = new HttpHeaders();
            headers.set(HttpHeaders.CONTENT_ENCODING, "UTF-8");
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", filename + ".xlsx");

            return new ResponseEntity<>(excelByteArray, headers, HttpStatus.OK);
        } catch (Exception e) {
            // 处理异常，记录日志等
            log.error("导出配料信息失败", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


//    @Operation(summary = "优化配料计算结果应用" )
//    @PostMapping("/updateIngredientByResultId")
//    public Result<String> updateIngredientByResultId(@RequestBody IngredientResultDTO ingredientResultDTO) {
//        log.info("请求的参数:{}", ingredientResultDTO);
//        standardIngredientRecordsService.updateIngredientByResultId(ingredientResultDTO);
//        return Result.ok();
//    }

    @Operation(summary="查询当前配料记录所对应的所有数据")
    @GetMapping("/getRecords/{id}")
    public Result<AllDataVO> getRecords(@PathVariable Long id) {
        AllDataVO allDataVO = standardIngredientRecordsService.getRecords(id);
        return Result.ok(allDataVO);
    }

    @Operation(summary = "应用优化计算结果")
    @PostMapping("/updateIngredientByResultIds")
    public Result<String> updateIngredientByResultIds(@RequestBody IngredientResultIdsDTO ingredientResultIdsDTO) {
        log.info("请求的参数:{}", ingredientResultIdsDTO);
        standardIngredientRecordsService.updateIngredientByResultIds(ingredientResultIdsDTO);
        return Result.ok();
    }




}
