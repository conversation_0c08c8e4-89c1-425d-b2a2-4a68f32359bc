package com.nercar.ingredient.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nercar.ingredient.domain.po.ProductionEquipments;
import com.nercar.ingredient.domain.po.StandardRawMaterials;
import com.nercar.ingredient.domain.po.StandardRawMaterialsPlus;
import com.nercar.ingredient.mapper.ProductionEquipmentsMapper;
import com.nercar.ingredient.mapper.StandardRawMaterialsMapper;
import com.nercar.ingredient.mapper.StandardRawMaterialsPlusMapper;
import com.nercar.ingredient.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/dataFix")
@Slf4j
public class DataFixController {
    
    @Autowired
    private StandardRawMaterialsPlusMapper standardRawMaterialsPlusMapper;
    
    @Autowired
    private StandardRawMaterialsMapper standardRawMaterialsMapper;
    
    @Autowired
    private ProductionEquipmentsMapper productionEquipmentsMapper;
    
    /**
     * 数据修复：同步Plus表目标值到旧表（优化版本）
     */
    @Operation(summary = "数据修复：同步Plus表目标值到旧表-优化版")
    @PostMapping("/syncTargetValuesToOldTableOptimized")
    @Transactional(rollbackFor = Exception.class)
    public Result<String> syncTargetValuesToOldTableOptimized() {
        long startTime = System.currentTimeMillis();
        log.info("=== 开始执行优化版数据同步 ===");
        
        try {
            // 1. 查询新表中所有"目标值"类型的记录
            log.info("步骤1：查询新表目标值记录...");
            LambdaQueryWrapper<StandardRawMaterialsPlus> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StandardRawMaterialsPlus::getType, "目标值");
            List<StandardRawMaterialsPlus> targetValueRecords = standardRawMaterialsPlusMapper.selectList(wrapper);
            
            log.info("步骤1完成：找到{}条目标值记录需要同步", targetValueRecords.size());
            
            if (targetValueRecords.isEmpty()) {
                return Result.success("没有找到需要同步的目标值记录");
            }
            
            // 2. 批量查询旧表中已存在的记录，避免逐条查询
            log.info("步骤2：批量查询已存在记录...");
            Set<String> existingKeys = getExistingRecordKeys(targetValueRecords);
            log.info("步骤2完成：旧表中已存在 {} 条相关记录", existingKeys.size());
            
            // 3. 分类处理：需要新增的和需要更新的
            log.info("步骤3：分类处理记录...");
            List<StandardRawMaterialsPlus> recordsToInsert = new ArrayList<>();
            List<StandardRawMaterialsPlus> recordsToUpdate = new ArrayList<>();
            
            for (StandardRawMaterialsPlus plusRecord : targetValueRecords) {
                String key = plusRecord.getName() + "_" + plusRecord.getProductionEquipmentId();
                if (existingKeys.contains(key)) {
                    recordsToUpdate.add(plusRecord);
                } else {
                    recordsToInsert.add(plusRecord);
                }
            }
            
            log.info("步骤3完成：需要新增 {} 条，需要更新 {} 条", recordsToInsert.size(), recordsToUpdate.size());
            
            int successCount = 0;
            List<String> errorMessages = new ArrayList<>();
            
            // 4. 批量处理新增记录
            if (!recordsToInsert.isEmpty()) {
                log.info("步骤4：开始批量新增记录...");
                successCount += batchInsertRecords(recordsToInsert, errorMessages);
                log.info("步骤4完成：批量新增处理完毕");
            }
            
            // 5. 批量处理更新记录
            if (!recordsToUpdate.isEmpty()) {
                log.info("步骤5：开始批量更新记录...");
                successCount += batchUpdateRecords(recordsToUpdate, errorMessages);
                log.info("步骤5完成：批量更新处理完毕");
            }
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            // 6. 返回处理结果
            String resultMessage = String.format(
                "=== 优化版数据同步完成！总耗时：%d秒 ===\n" +
                "总记录数：%d条\n" +
                "成功处理：%d条\n" +
                "失败：%d条", 
                duration / 1000,
                targetValueRecords.size(),
                successCount, 
                errorMessages.size()
            );
            
            if (!errorMessages.isEmpty()) {
                resultMessage += "\n失败详情：\n" + String.join("\n", errorMessages.subList(0, Math.min(5, errorMessages.size())));
                if (errorMessages.size() > 5) {
                    resultMessage += "\n... 还有" + (errorMessages.size() - 5) + "条错误";
                }
            }
            
            log.info(resultMessage);
            return Result.success(resultMessage);
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            log.error("=== 优化版数据同步失败！耗时：{}秒 ===", duration / 1000, e);
            return Result.failed("数据同步失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量查询已存在记录的键
     */
    private Set<String> getExistingRecordKeys(List<StandardRawMaterialsPlus> targetValueRecords) {
        // 提取所有可能的名称和设备ID
        List<String> nameList = targetValueRecords.stream()
            .map(StandardRawMaterialsPlus::getName)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
        
        List<Long> equipmentIdList = targetValueRecords.stream()
            .map(StandardRawMaterialsPlus::getProductionEquipmentId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
        
        if (nameList.isEmpty() || equipmentIdList.isEmpty()) {
            return new HashSet<>();
        }
        
        // 批量查询已存在的记录
        LambdaQueryWrapper<StandardRawMaterials> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StandardRawMaterials::getName, nameList)
                   .in(StandardRawMaterials::getProductionEquipmentId, equipmentIdList)
                   .select(StandardRawMaterials::getName, StandardRawMaterials::getProductionEquipmentId);
        
        List<StandardRawMaterials> existingRecords = standardRawMaterialsMapper.selectList(queryWrapper);
        
        // 构建键集合
        return existingRecords.stream()
            .map(record -> record.getName() + "_" + record.getProductionEquipmentId())
            .collect(Collectors.toSet());
    }
    
    /**
     * 批量新增记录
     */
    private int batchInsertRecords(List<StandardRawMaterialsPlus> recordsToInsert, List<String> errorMessages) {
        int successCount = 0;
        int batchSize = 20; // 每批处理20条
        int totalBatches = (int) Math.ceil((double) recordsToInsert.size() / batchSize);
        
        for (int i = 0; i < totalBatches; i++) {
            int startIndex = i * batchSize;
            int endIndex = Math.min(startIndex + batchSize, recordsToInsert.size());
            List<StandardRawMaterialsPlus> batch = recordsToInsert.subList(startIndex, endIndex);
            
            try {
                List<StandardRawMaterials> newRecords = new ArrayList<>();
                
                for (StandardRawMaterialsPlus plusRecord : batch) {
                    try {
                        StandardRawMaterials newRecord = createNewRecord(plusRecord);
                        newRecords.add(newRecord);
                    } catch (Exception e) {
                        String errorMsg = String.format("转换记录失败：%s - %s, 错误：%s", 
                            plusRecord.getName(), 
                            getEquipmentName(plusRecord.getProductionEquipmentId()), 
                            e.getMessage());
                        errorMessages.add(errorMsg);
                    }
                }
                
                // 批量插入
                if (!newRecords.isEmpty()) {
                    for (StandardRawMaterials record : newRecords) {
                        standardRawMaterialsMapper.insert(record);
                        successCount++;
                    }
                    log.info("新增批次 {}/{} 完成：{} 条记录", i + 1, totalBatches, newRecords.size());
                }
                
            } catch (Exception e) {
                String errorMsg = String.format("新增批次 %d/%d 失败，错误：%s", i + 1, totalBatches, e.getMessage());
                errorMessages.add(errorMsg);
                log.error(errorMsg, e);
            }
        }
        
        return successCount;
    }
    
    /**
     * 批量更新记录
     */
    private int batchUpdateRecords(List<StandardRawMaterialsPlus> recordsToUpdate, List<String> errorMessages) {
        int successCount = 0;
        int batchSize = 20; // 每批处理20条
        int totalBatches = (int) Math.ceil((double) recordsToUpdate.size() / batchSize);
        
        for (int i = 0; i < totalBatches; i++) {
            int startIndex = i * batchSize;
            int endIndex = Math.min(startIndex + batchSize, recordsToUpdate.size());
            List<StandardRawMaterialsPlus> batch = recordsToUpdate.subList(startIndex, endIndex);
            
            try {
                for (StandardRawMaterialsPlus plusRecord : batch) {
                    try {
                        // 查询现有记录
                        LambdaQueryWrapper<StandardRawMaterials> checkWrapper = new LambdaQueryWrapper<>();
                        checkWrapper.eq(StandardRawMaterials::getName, plusRecord.getName())
                                   .eq(StandardRawMaterials::getProductionEquipmentId, plusRecord.getProductionEquipmentId());
                        
                        StandardRawMaterials existingRecord = standardRawMaterialsMapper.selectOne(checkWrapper);
                        
                        if (existingRecord != null) {
                            updateExistingRecord(existingRecord, plusRecord);
                            standardRawMaterialsMapper.updateById(existingRecord);
                            successCount++;
                        }
                        
                    } catch (Exception e) {
                        String errorMsg = String.format("更新记录失败：%s - %s, 错误：%s", 
                            plusRecord.getName(), 
                            getEquipmentName(plusRecord.getProductionEquipmentId()), 
                            e.getMessage());
                        errorMessages.add(errorMsg);
                    }
                }
                
                log.info("更新批次 {}/{} 完成：{} 条记录", i + 1, totalBatches, batch.size());
                
            } catch (Exception e) {
                String errorMsg = String.format("更新批次 %d/%d 失败，错误：%s", i + 1, totalBatches, e.getMessage());
                errorMessages.add(errorMsg);
                log.error(errorMsg, e);
            }
        }
        
        return successCount;
    }
    
    // 保留你原有的方法
    private void updateExistingRecord(StandardRawMaterials existingRecord, StandardRawMaterialsPlus plusRecord) {
        // 复制基本信息
        existingRecord.setName(plusRecord.getName());
        existingRecord.setDepartmentId(plusRecord.getDepartmentId());
        existingRecord.setProductionEquipmentId(plusRecord.getProductionEquipmentId());
        existingRecord.setYieldRate(plusRecord.getYieldRate());
        existingRecord.setPrice(plusRecord.getPrice());
        existingRecord.setIsCustom(plusRecord.getIsCustom());
        existingRecord.setCategory(plusRecord.getCategory());
        existingRecord.setPriority(plusRecord.getPriority());
        existingRecord.setCContent(plusRecord.getCContent());
        existingRecord.setSingleConsume(plusRecord.getSingleConsume());
        existingRecord.setDepartmentName(plusRecord.getDepartmentName());
        
        // 处理元素信息
        processElementInfo(existingRecord, plusRecord);
    }
    
    private StandardRawMaterials createNewRecord(StandardRawMaterialsPlus plusRecord) {
        StandardRawMaterials newRecord = new StandardRawMaterials();
        
        // 复制基本信息
        BeanUtils.copyProperties(plusRecord, newRecord);
        newRecord.setId(null); // 确保ID为空，让数据库自动生成
        
        // 处理元素信息
        processElementInfo(newRecord, plusRecord);
        
        return newRecord;
    }
    
    private void processElementInfo(StandardRawMaterials targetRecord, StandardRawMaterialsPlus sourceRecord) {
        // 构建元素映射
        Map<String, BigDecimal> elementMap = new HashMap<>();
        elementMap.put("C", sourceRecord.getC());
        elementMap.put("Mn", sourceRecord.getMn());
        elementMap.put("Si", sourceRecord.getSi());
        elementMap.put("P", sourceRecord.getP());
        elementMap.put("Cr", sourceRecord.getCr());
        elementMap.put("V", sourceRecord.getV());
        elementMap.put("Mo", sourceRecord.getMo());
        elementMap.put("Ni", sourceRecord.getNi());
        elementMap.put("W", sourceRecord.getW());
        elementMap.put("Cu", sourceRecord.getCu());
        elementMap.put("Ti", sourceRecord.getTi());
        elementMap.put("Nb", sourceRecord.getNb());
        elementMap.put("Co", sourceRecord.getCo());
        elementMap.put("S", sourceRecord.getS());
        elementMap.put("Sn", sourceRecord.getSn());
        elementMap.put("Al", sourceRecord.getAl());
        elementMap.put("Fe", sourceRecord.getFe());
        elementMap.put("B", sourceRecord.getB());
        elementMap.put("Zr", sourceRecord.getZr());
        elementMap.put("La", sourceRecord.getLa());
        elementMap.put("Ce", sourceRecord.getCe());
        elementMap.put("Ca", sourceRecord.getCa());
        elementMap.put("Pb", sourceRecord.getPb());
        elementMap.put("Bi", sourceRecord.getBi());
        elementMap.put("Sb", sourceRecord.getSb());
        elementMap.put("As", sourceRecord.getAs());
        elementMap.put("Als", sourceRecord.getAls());
        elementMap.put("Ta", sourceRecord.getTa());
        elementMap.put("Mg", sourceRecord.getMg());
        elementMap.put("Ag", sourceRecord.getAg());
        elementMap.put("Hg", sourceRecord.getHg());
        elementMap.put("Cd", sourceRecord.getCd());
        elementMap.put("Zn", sourceRecord.getZn());
        elementMap.put("Te", sourceRecord.getTe());
        elementMap.put("Se", sourceRecord.getSe());
        elementMap.put("Pr", sourceRecord.getPr());
        elementMap.put("Nd", sourceRecord.getNd());
        elementMap.put("Sc", sourceRecord.getSc());
        elementMap.put("Y", sourceRecord.getY());
        elementMap.put("Hf", sourceRecord.getHf());
        elementMap.put("Pcm", sourceRecord.getPcm());
        elementMap.put("H", sourceRecord.getH());
        elementMap.put("O", sourceRecord.getO());
        elementMap.put("N", sourceRecord.getN());
        
        // 过滤掉null值并按值大小排序
        List<Map.Entry<String, BigDecimal>> sortedElements = elementMap.entrySet()
                .stream()
                .filter(entry -> entry.getValue() != null)
                .sorted(Map.Entry.<String, BigDecimal>comparingByValue().reversed())
                .collect(Collectors.toList());
        
        if (!sortedElements.isEmpty()) {
            // 设置主元素和基准品位
            Map.Entry<String, BigDecimal> primaryElement = sortedElements.get(0);
            targetRecord.setElement(primaryElement.getKey());
            targetRecord.setComposition(primaryElement.getValue());
            
            // 设置次元素（如果存在）
            if (sortedElements.size() > 1) {
                Map.Entry<String, BigDecimal> secondaryElement = sortedElements.get(1);
                targetRecord.setSecondaryElement(secondaryElement.getKey());
            }
        }
        
        // 设置C元素含量
        if (sourceRecord.getC() != null) {
            targetRecord.setCarbonElement(sourceRecord.getC().toString());
            targetRecord.setCContent(sourceRecord.getC().intValue());
        }
    }
    
    private String getEquipmentName(Long equipmentId) {
        if (equipmentId == null) return "未知设备";
        
        LambdaQueryWrapper<ProductionEquipments> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductionEquipments::getId, equipmentId);
        ProductionEquipments equipment = productionEquipmentsMapper.selectOne(wrapper);
        
        return equipment != null ? equipment.getEquipmentName() : "未知设备";
    }
    
    // 保留你原有的检测方法
    @Operation(summary = "数据检查：对比新旧表数据")
    @GetMapping("/checkDataConsistency")
    public Result<Map<String, Object>> checkDataConsistency() {
        // 统计新表中目标值记录数量
        LambdaQueryWrapper<StandardRawMaterialsPlus> plusWrapper = new LambdaQueryWrapper<>();
        plusWrapper.eq(StandardRawMaterialsPlus::getType, "目标值");
        long plusTargetCount = standardRawMaterialsPlusMapper.selectCount(plusWrapper);
        
        // 统计旧表记录数量
        long oldTableCount = standardRawMaterialsMapper.selectCount(null);
        
        // 查找新表中有但旧表中没有的记录
        List<StandardRawMaterialsPlus> plusRecords = standardRawMaterialsPlusMapper.selectList(plusWrapper);
        List<String> missingInOldTable = new ArrayList<>();
        
        for (StandardRawMaterialsPlus plusRecord : plusRecords) {
            LambdaQueryWrapper<StandardRawMaterials> checkWrapper = new LambdaQueryWrapper<>();
            checkWrapper.eq(StandardRawMaterials::getName, plusRecord.getName())
                       .eq(StandardRawMaterials::getProductionEquipmentId, plusRecord.getProductionEquipmentId());
            
            long count = standardRawMaterialsMapper.selectCount(checkWrapper);
            if (count == 0) {
                missingInOldTable.add(String.format("%s - %s", 
                    plusRecord.getName(), 
                    getEquipmentName(plusRecord.getProductionEquipmentId())));
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("新表目标值记录数", plusTargetCount);
        result.put("旧表记录数", oldTableCount);
        result.put("缺失记录数", missingInOldTable.size());
        result.put("缺失记录详情", missingInOldTable);
        
        return Result.success(result);
    }
}