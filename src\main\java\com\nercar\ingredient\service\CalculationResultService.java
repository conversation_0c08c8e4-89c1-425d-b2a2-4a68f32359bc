package com.nercar.ingredient.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.ingredient.domain.dto.CalculationQueryDataDTO;
import com.nercar.ingredient.domain.po.CalculationResult;
import com.nercar.ingredient.domain.po.CostEstimation;
import com.nercar.ingredient.domain.vo.CalculationResultDataVO;

/**
* <AUTHOR>
* @description 针对表【calculation_result(计算结果表)】的数据库操作Service
* @createDate 2025-04-01 13:55:28
*/
public interface CalculationResultService extends IService<CalculationResult> {

    CalculationResultDataVO calculation(CalculationQueryDataDTO calculationQueryDataDTO);

    CostEstimation getCostEstimationById(Long id);
}
