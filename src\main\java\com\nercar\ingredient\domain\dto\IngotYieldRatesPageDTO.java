package com.nercar.ingredient.domain.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class IngotYieldRatesPageDTO  implements Serializable {

    private Integer pageNo;
    private Integer pageSize;

    private String departmentName;

    private String processPath;

    private String createtime;
}
