server:
  port: 7104


# 图片上传配置
image:
  upload:
    path: images  # 相对于项目根目录的路径
    max-size: 5MB  # 最大文件大小
  server:
#    base-url: http://*************:7104
#    base-url: http://*************:9021
    base-url: http://************:9021
spring:
  application:
    name: fushun-ingredient
  #  datasource:
  #    url: *******************************************
  #    username: postgres
  #    password: 123456
  #    driver-class-name: org.postgresql.Driver
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  jackson:
    date-format: java.text.SimpleDateFormat
    time-zone: GMT+8
  profiles:
    active: dev
#    active: prod
  web:
    resources:
      static-locations: classpath:/static/,classpath:/images/


mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true  # 将下划线命名转换为驼峰命名
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
  global-config:
    db-config:
      id-type: ASSIGN_ID
      # 开启自动填充功能
      field-strategy: not_null
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.nercar.ingredient.domain.po



logging:
  level:
    com.baomidou.mybatis plus: DEBUG
    org.apache.ibatis: DEBUG



sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 43200
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: simple-uuid
  # 是否输出操作日志
  is-log: true

smart-desktop:
  token-prefix: SmartDesktop-

# 远程服务配置
remote-service:
  approve-batching:
    endpoints:
      batching: /approve/batching
      batching-again: /approve/batching/onceagain
  user-auth:
    endpoints:
      token: /user/token
  calculation:
    endpoints:
      calculate: /caculate

# 合同评审系统配置
contract-review:
  login:
    username: "1000"
    password: "123456"
  token:
    header-name: Authorization
    refresh-interval: 30  # Token刷新间隔（分钟）
    cache-duration: 60    # Token缓存时长（分钟）
  endpoints:
    login: /employee/login
    customer-list: /sale/getCustomerListByName
    historical-reviews: /sale/getHistoricalReviews
    steel-grade-list: /sale/getSteelGradeListByName
    steel-type-list: /sale/getSteelTypeListByName
    contract-detail: /techCenterStandardSection/getReviewedOrderInfo
    file-list: /file/select
    file-preview: /file/preview

# 配料算法接口配置
calculation:
  # 是否使用新算法接口
  use-new-algorithm: true
  # 新算法接口配置
  new-algorithm:
    base-url: http://49.4.23.163:1998
    endpoint: /cacluate
    timeout: 30000
