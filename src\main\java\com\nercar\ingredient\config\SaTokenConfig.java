package com.nercar.ingredient.config;

import cn.dev33.satoken.dao.SaTokenDao;
import cn.dev33.satoken.dao.SaTokenDaoRedis;
import cn.dev33.satoken.dao.SaTokenDaoDefaultImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;

@Configuration
public class SaTokenConfig {

    private final Environment environment;

    public SaTokenConfig(Environment environment) {
        this.environment = environment;
    }

    @Bean
    @Primary
    public SaTokenDao saTokenDao() {
        // 现在dev环境也有Redis配置了，统一使用Redis存储
        return new SaTokenDaoRedis();

        // 原有的环境判断逻辑（现在不需要了）
        /*
        // 获取当前激活的profile
        String[] activeProfiles = environment.getActiveProfiles();
        boolean isDevProfile = false;

        for (String profile : activeProfiles) {
            if ("dev".equals(profile)) {
                isDevProfile = true;
                break;
            }
        }

        // 根据环境选择存储实现
        if (isDevProfile) {
            return new SaTokenDaoDefaultImpl();
        } else {
            return new SaTokenDaoRedis();
        }
        */
    }
}
