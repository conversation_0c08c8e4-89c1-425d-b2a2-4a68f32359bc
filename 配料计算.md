# 全局公共参数

**全局Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局认证方式**

> 无需认证

# 状态码说明

| 状态码 | 中文描述 |
| --- | ---- |
| 暂无参数 |

# 配料计算

> 创建人: 托厄

> 更新人: 托厄

> 创建时间: 2025-07-25 14:13:18

> 更新时间: 2025-07-25 14:23:19

**标准配料调用优化配料算法接口**

**接口状态**

> 开发中

**接口URL**

> http://***********:1998/cacluate

| 环境  | URL |
| --- | --- |


**Mock URL**

> /cacluate?apipost_id=435650c7d5002

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "steelGrade": "18CrNiMo7-6+HH",
    "targetWeight": 1229.024,
    "materials": [
        {
            "name": "高铬",
            "compositions": [
                {
                    "element": "Cr",
                    "content": 50
                },
                {
                    "element": "Cr",
                    "content": 50
                }
            ],
            "price": 12222.5,
            "yieldRate": 90.5,
            "fixedWeight": 66
        }
    ],
    "targetComp": [
        {
            "element": "Mo",
            "minValue": 0.17,
            "maxValue": 0.2,
            "targetValue": 0.185
        }
    ]
}
```

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "OK",
    "data": {
        "rawMaterialWeight": 27,
        "resultWeight": 13,
        "totalCost": 17,
        "selectedMaterials": [
            {
                "name": "高铬",
                "weight": 10,
                "compositions": [
                    {
                        "element": "Cr",
                        "weight": 16,
                        "weightPercent": 31
                    }
                ]
            }
        ],
        "compositions": [
            {
                "element": "C",
                "composition": 97
            }
        ]
    }
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | OK | string | - |
| data | - | object | - |
| data.rawMaterialWeight | 1215 | number | - |
| data.resultWeight | 1122.49 | number | - |
| data.totalCost | 100000 | number | - |
| data.selectedMaterials | - | array | - |
| data.selectedMaterials.name | 高铬 | string | - |
| data.selectedMaterials.weight | 100 | number | - |
| data.selectedMaterials.compositions | - | object | - |
| data.selectedMaterials.compositions.Cr | - | object | - |
| data.selectedMaterials.compositions.Cr.weight | 50 | number | - |
| data.selectedMaterials.compositions.Cr.weightPercent | 50 | number | - |
| data.compositions | - | object | - |
| data.compositions.C | 0.4 | number | - |
| data.compositions.Si | 0.5 | number | - |

* 失败(404)

```javascript
暂无数据
```

**Query**
