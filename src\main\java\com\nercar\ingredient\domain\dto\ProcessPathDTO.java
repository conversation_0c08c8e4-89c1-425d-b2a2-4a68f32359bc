package com.nercar.ingredient.domain.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;

/**
 * 工艺路径表
 * @TableName process_path
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProcessPathDTO extends BaseDTO {

    private Integer pageNo;
    private Integer pageSize;
    /**
     * 主键
     */
    @TableId
    @Schema(description = "主键")
    private Long id;
    /**
     * 钢种id
     */
    @Schema(description = "钢种id")
    private Long steelGradesId;

    /**
     * 钢种
     */
    @Schema(description = "钢种")
    private String steelGrade;

    /**
     * 路径名称
     */
    @Schema(description = "路径名称")
    private String pathName;

    /**
     * 用途描述
     */
    @Schema(description = "用途描述")
    private String purpose;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 使用频次
     */
    @Schema(description = "使用频次")
    private Integer frequence;

    /**
     * 成材率id
     */
    @Schema(description = "成材率id")
    private Long materialYield;

    /**
     *
     */
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    private String createuser;

    /**
     *
     */
    @TableField(value = "createtime",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     *
     */
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;



    /**
     * 工艺步骤
     */
    @Schema(description = "工艺步骤")
    private ArrayList<PathSaveDTO> steps;


}