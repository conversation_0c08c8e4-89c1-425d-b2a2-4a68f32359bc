package com.nercar.ingredient.domain.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 钢种表
 * @TableName steel_grades
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SteelGradesDTO extends BaseDTO {


    private Integer pageNo;
    private Integer pageSize;
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 钢种
     */
    private String steelGrade;

    /**
     * 工艺路径ID
     */
    @Schema(description = "工艺路径ID，用于查询特定工艺路径下的历史配料")
    private Long processPathId;

    /**
     *
     */
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    private String createuser;

    /**
     *
     */
    @TableField(value = "createtime",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     *
     */
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;




}