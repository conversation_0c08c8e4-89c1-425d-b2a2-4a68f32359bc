<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.StandardTemplateMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.StandardTemplate">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="pathName" column="path_name" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="device" column="device" jdbcType="VARCHAR"/>
            <result property="departmentId" column="department_id" jdbcType="BIGINT"/>
            <result property="departmentName" column="department_name" jdbcType="VARCHAR"/>
            <result property="rating" column="rating" jdbcType="INTEGER"/>
            <result property="createuser" column="createuser" jdbcType="VARCHAR"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,path_name,remark,device,
        department_id,department_name,rating,createuser,createtime,
        updatetime
    </sql>
</mapper>
