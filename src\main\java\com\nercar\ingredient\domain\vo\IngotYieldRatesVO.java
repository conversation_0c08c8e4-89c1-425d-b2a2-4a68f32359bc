package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 成锭率
 * @TableName ingot_yield_rates
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IngotYieldRatesVO extends BaseVO {
    /**
     * 
     */
    @TableId
    @Schema(description = "主键")
    private Long id;

    /**
     * 工序
     */
    @Schema(description = "工艺路径")
    private String processPath;


    private String equipments;

    @Schema(description = "设备")
    private String device;



    private String updateuser;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String departmentName;

    /**
     * 成锭率
     */
    @Schema(description = "成锭率")
    private BigDecimal ingotYield;

    /**
     * 
     */
    private String createuser;

    /**
     * 
     */
    private Date createtime;

    /**
     *
     */
    private Date updatetime;

    /**
     * 是否自定义(0=系统默认,1=用户自定义)
     */
    @Schema(description = "是否自定义(0=系统默认,1=用户自定义)")
    private Integer isCustom;

}