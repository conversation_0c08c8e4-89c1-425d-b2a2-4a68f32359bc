package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 配料单成锭率
 * @TableName ingredient_idingot_result
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IngredientIdingotResultVO extends BaseVO {
    /**
     * 主键
     */
    @TableId
    @Schema(description = "主键")
    private Long id;

    /**
     * 配料单ID
     */
    @Schema(description = "配料单ID")
    private Long standardIngredientRecordId;

    /**
     * 成锭率表ID
     */
    @Schema(description = "成锭率表ID")
    private Long igingotId;

    /**
     * 工序
     */
    @Schema(description = "工序")
    private String processPath;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String departmentName;

    /**
     * 成锭率
     */
    @Schema(description = "成锭率")
    private BigDecimal ingotYield;




}