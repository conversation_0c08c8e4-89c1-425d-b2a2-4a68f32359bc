package com.nercar.ingredient.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.nercar.ingredient.domain.bo.CurrentUser;
import com.nercar.ingredient.security.UserContext;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    private static final Logger log = LoggerFactory.getLogger(MyMetaObjectHandler.class);

    @Override
    public void insertFill(MetaObject metaObject) {
        CurrentUser currentUser = UserContext.getCurrentUser();
        if (currentUser != null) {
            this.strictInsertFill(metaObject, "createuser", String.class, currentUser.getUsername());
            this.strictInsertFill(metaObject, "createtime", LocalDateTime.class, LocalDateTime.now());
            this.strictInsertFill(metaObject, "updateuser", String.class, currentUser.getUsername());
            this.strictInsertFill(metaObject, "updatetime", LocalDateTime.class, LocalDateTime.now());
        }
        this.strictInsertFill(metaObject, "createtime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "updatetime", LocalDateTime.class, LocalDateTime.now());
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        CurrentUser currentUser = UserContext.getCurrentUser();
        if (currentUser != null) {
            // 使用setFieldValByName替代strictUpdateFill，这样无论字段是否为null都会更新
            this.setFieldValByName("updateuser", currentUser.getUsername(), metaObject);
            this.setFieldValByName("updatetime", LocalDateTime.now(), metaObject);
        }
        this.setFieldValByName("updatetime", LocalDateTime.now(), metaObject);
    }
}
