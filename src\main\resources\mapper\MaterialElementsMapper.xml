<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.MaterialElementsMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.MaterialElements">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="materialId" column="material_id" jdbcType="BIGINT"/>
            <result property="element" column="element" jdbcType="VARCHAR"/>
            <result property="composition" column="composition" jdbcType="NUMERIC"/>
            <result property="sort" column="sort" jdbcType="SMALLINT"/>
            <result property="createuser" column="createuser" jdbcType="VARCHAR"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,material_id,element,
        composition,sort,createuser,
        createtime,updatetime
    </sql>
</mapper>
