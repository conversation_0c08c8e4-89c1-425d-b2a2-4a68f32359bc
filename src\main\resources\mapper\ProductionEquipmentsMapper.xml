<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.ProductionEquipmentsMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.ProductionEquipments">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="equipmentName" column="equipment_name" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="equipmentType" column="equipment_type" jdbcType="VARCHAR"/>
            <result property="imagesUrl" column="images_url" jdbcType="VARCHAR"/>
            <result property="departmentId" column="department_id" jdbcType="BIGINT"/>
            <result property="createuser" column="createuser" jdbcType="VARCHAR"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,equipment_name,type,equipment_type,
        images_url,department_id,createuser,
        createtime,updatetime
    </sql>
</mapper>
