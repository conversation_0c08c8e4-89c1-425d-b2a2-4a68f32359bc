package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 金属料吨水单耗配置表
 * @TableName metal_material_water_consumption_config
 */
@TableName(value = "metal_material_water_consumption_config")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "金属料吨水单耗配置")
public class MetalMaterialWaterConsumptionConfig {
    
    /**
     * 主键
     */
    @TableId
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 金属料吨水单耗
     */
    @Schema(description = "金属料吨水单耗")
    private Integer metalMaterialWaterConsumption;

    /**
     * 冶炼方法
     */
    @Schema(description = "冶炼方法")
    private String method;

    /**
     * 冶炼设备
     */
    @Schema(description = "冶炼设备")
    private String device;

    /**
     * 创建人
     */
    @TableField(value = "createuser", fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createuser;

    /**
     * 创建时间
     */
    @TableField(value = "createtime", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;
}
