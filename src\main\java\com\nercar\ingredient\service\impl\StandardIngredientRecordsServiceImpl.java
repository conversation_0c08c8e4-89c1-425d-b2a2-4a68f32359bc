package com.nercar.ingredient.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.dto.*;
import com.nercar.ingredient.domain.po.*;
import com.nercar.ingredient.domain.vo.*;
import com.nercar.ingredient.mapper.*;
import com.nercar.ingredient.security.UserContext;
import com.nercar.ingredient.service.ExecutionStandardService;
import com.nercar.ingredient.service.MetalMaterialWaterConsumptionConfigService;
import com.nercar.ingredient.service.ProcessPathStepsService;
import com.nercar.ingredient.service.StandardIngredientRecordsService;
import com.nercar.ingredient.service.SteelGradesService;
import io.netty.channel.ChannelOption;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.ConnectException;
import java.net.URLEncoder;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
* <AUTHOR>
* @description 针对表【standard_ingredient_records(标准配料记录表)】的数据库操作Service实现
* @createDate 2025-04-01 13:55:28
*/
@Service
@Slf4j
public class StandardIngredientRecordsServiceImpl extends ServiceImpl<StandardIngredientRecordsMapper, StandardIngredientRecords>
    implements StandardIngredientRecordsService{
    @Autowired
    private SteelGradesService steelGradesService;
    @Autowired
    private ExecutionStandardService executionStandardService;
    @Autowired
    private PurposeCompositionsMapper purposeCompositionsMapper;
    @Autowired
    private SteelGradesMapper steelGradesMapper;
    @Autowired
    private ProcessPathMapper processPathMapper;
    @Autowired
    private ProcessPathStepsService processPathStepsService;
    @Autowired
    private PathMaterialsMapper pathMaterialsMapper;
    @Autowired
    private StandardRawMaterialsMapper standardRawMaterialsMapper;
    @Autowired
    private CalculationResultMapper calculationResultMapper;
    @Autowired
    private CostEstimationMapper costEstimationMapper;
    @Autowired
    private IngotYieldRatesMapper ingotYieldRatesMapper;
    @Autowired
    private IngredientIdingotResultMapper ingredientIdingotResultMapper;
    @Autowired
    private IngredientYieldResultMapper ingredientYieldResultMapper;
    @Autowired
    private MaterialYieldRatesMapper materialYieldRatesMapper;
    @Autowired
    private UsersMapper usersMapper;
    @Autowired
    private DepartmentsMapper departmentsMapper;
    @Autowired
    private MetalMaterialWaterConsumptionConfigService metalMaterialWaterConsumptionConfigService;
    @Autowired
    private StandardCompositionsMapper standardCompositionsMapper;
    @Autowired
    private WebClient.Builder webClientBuilder;

    @Value("${remote-service.approve-batching.base-url}")
    private String approveBatchingBaseUrl;

    @Value("${remote-service.approve-batching.endpoints.batching}")
    private String batchingEndpoint;

    @Value("${remote-service.approve-batching.endpoints.batching-again}")
    private String batchingAgainEndpoint;

    /**
     * 根据用户名获取用户部门信息
     * @param userName 用户名
     * @return 用户信息
     */
    private Users getUserByUserName(String userName) {
        LambdaQueryWrapper<Users> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(Users::getUserName, userName);
        List<Users> usersList = usersMapper.selectList(userWrapper);

        if (usersList.isEmpty()) {
            log.error("用户不存在：{}", userName);
            throw new RuntimeException("用户不存在：" + userName);
        }

        Users user = usersList.get(0); // 取第一条记录，解决重复用户问题

        if (user.getDepartmentId() == null) {
            log.warn("用户 {} 没有部门信息，将按技术中心用户处理（可查看所有数据）", userName);
        }

        return user;
    }

    /**
     * 检查用户是否为技术中心用户或无部门用户（按技术中心处理）
     * @param departmentId 部门ID
     * @return 是否为技术中心用户或无部门用户
     */
    private boolean isTechnicalCenterUser(Long departmentId) {
        // 如果部门ID为空，按技术中心用户处理（可查看所有数据）
        if (departmentId == null) {
            log.info("用户部门权限检查 - 部门ID为空，按技术中心用户处理（可查看所有数据）");
            return true;
        }

        Departments department = departmentsMapper.selectById(departmentId);
        if (department == null) {
            log.warn("部门信息不存在，部门ID: {}，按技术中心用户处理", departmentId);
            return true; // 部门信息异常时，按技术中心用户处理，避免系统报错
        }

        String departmentName = department.getDepartmentName();
        boolean isTechnicalCenter = departmentName.contains("技术中心");

        log.info("用户部门权限检查 - 部门ID: {}, 部门名称: {}, 是否技术中心: {}",
                departmentId, departmentName, isTechnicalCenter);

        return isTechnicalCenter;
    }

    @Override
    public IPage<StandardIngredientRecordsVO> selectPage(SteelGradesDTO steelGradesDTO, LambdaQueryWrapper<StandardIngredientRecords> wrapper) {
        Page<StandardIngredientRecordsVO> page = new Page<>(steelGradesDTO.getPageNo(), steelGradesDTO.getPageSize());
        return baseMapper.selectRecords(page, wrapper);
    }

    @Override
    public IPage<StandardIngredientRecordsVO> selectRecords(StandardIngredientRecordsQueryDTO standardIngredientRecordsQueryDTO) {
        // 权限控制：获取当前用户信息
        String userName = UserContext.getCurrentUser().getUsername();
        log.info("历史配料查询权限检查开始 - 用户: {}", userName);

        // 查询用户信息并验证部门权限
        Users currentUser = getUserByUserName(userName);
        boolean isTechnicalCenter = isTechnicalCenterUser(currentUser.getDepartmentId());

        log.info("用户权限信息 - 用户: {}, 部门ID: {}, 是否技术中心: {}",
                userName, currentUser.getDepartmentId(), isTechnicalCenter);

        Long steelGradeId= null;
        if ( standardIngredientRecordsQueryDTO.getSteelGrade().length() > 0) {
            //1、根据steelGrade查询steelGradeId
            LambdaQueryWrapper<SteelGrades> steelwrapper = new LambdaQueryWrapper<>();
            steelwrapper.like(StringUtils.hasLength(standardIngredientRecordsQueryDTO.getSteelGrade()), SteelGrades::getSteelGrade, standardIngredientRecordsQueryDTO.getSteelGrade());
            SteelGrades steelGrades = steelGradesService.getOne(steelwrapper);
            // 空值校验
            if ( steelGrades != null){
                steelGradeId = steelGrades.getId();
            }
        }
        Long standardId= null;
        if ( standardIngredientRecordsQueryDTO.getStandardName().length() > 0) {
        //2、根据standardName查询standardId
        LambdaQueryWrapper<ExecutionStandard> standardwrapper = new LambdaQueryWrapper<>();
        standardwrapper.like(StringUtils.hasLength(standardIngredientRecordsQueryDTO.getStandardName()), ExecutionStandard::getStandardName, standardIngredientRecordsQueryDTO.getStandardName());
        ExecutionStandard executionStandard = executionStandardService.getOne(standardwrapper);
        // 空值校验
            if ( executionStandard != null){
                standardId = executionStandard.getId();
            }
        }
        //3、根据steelGradeId和standardId以及mixingDate查询standardIngredientRecords
        LambdaQueryWrapper<StandardIngredientRecords> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(steelGradeId), StandardIngredientRecords::getSteelGradeId, steelGradeId);
        wrapper.eq(Objects.nonNull(standardId), StandardIngredientRecords::getExecutionStandardId, standardId);

        // 权限控制：非技术中心用户只能查看本部门的配料记录
        if (!isTechnicalCenter) {
            wrapper.eq(StandardIngredientRecords::getDepartmentId, currentUser.getDepartmentId());
            log.info("应用部门权限过滤 - 用户: {}, 只能查看部门ID: {} 的配料记录",
                    userName, currentUser.getDepartmentId());
        } else {
            log.info("技术中心用户权限 - 用户: {}, 可查看所有部门的配料记录", userName);
        }
        
        // 时间范围查询
        if (Objects.nonNull(standardIngredientRecordsQueryDTO.getStartmixingDate()) && 
            Objects.nonNull(standardIngredientRecordsQueryDTO.getEndmixingDate())) {
            LocalDateTime startOfDay = standardIngredientRecordsQueryDTO.getStartmixingDate().withHour(0).withMinute(0).withSecond(0);
            LocalDateTime endOfDay = standardIngredientRecordsQueryDTO.getEndmixingDate().withHour(23).withMinute(59).withSecond(59);
            wrapper.ge(StandardIngredientRecords::getMixingDate, startOfDay)
                   .le(StandardIngredientRecords::getMixingDate, endOfDay);
        }
        
        Page<StandardIngredientRecordsVO> page = new Page<>(standardIngredientRecordsQueryDTO.getPageNo(), standardIngredientRecordsQueryDTO.getPageSize());
        return baseMapper.selectRecords(page, wrapper);
    }


    @Override
    public StandardIngredientRecordsResultVO previewIngredient(Long id) {
        if (id == null) {
            throw  new IllegalArgumentException("id不能为空");
        }
        //1、拿到的参数是 standardIngredientRecordsId
        //2、我们需要的数据分别来自，目标成分表，计算结果表以及标准原料表
        StandardIngredientRecords standardIngredientRecords = baseMapper.selectById(id);
        if (standardIngredientRecords == null) {
            throw new IllegalArgumentException("标准配料记录不存在，id=" + id);
        }
        //4、根据id找到目标成分表拿到相关数据
        LambdaQueryWrapper<PurposeCompositions> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PurposeCompositions::getStandardIngredientRecordId, id)
               .orderByAsc(PurposeCompositions::getSortOrder);
        List<PurposeCompositions> purposeCompositionsList = purposeCompositionsMapper.selectList(wrapper);
//        if ( purposeCompositionsList.isEmpty()) {
//            throw new RuntimeException("未找到目标成分记录，standardIngredientRecordId=" + id);
//        }
        List<PurposeCompositionsVO> purposeCompositionsVOList = BeanUtil.copyToList(purposeCompositionsList, PurposeCompositionsVO.class);
        //5、根据calculationResultId找到计算结果表联合标准原料表拿到相关数据
//        Long calculationResultId = standardIngredientRecords.getCalculationResultId();
        List<CalculationResult> calculationResultList = calculationResultMapper.selectList(new LambdaQueryWrapper<CalculationResult>().eq(CalculationResult::getStandardIngredientRecordId, id));

        if (calculationResultList == null) {
            throw new IllegalArgumentException("calculationResultId不能为空，标准配料记录id=" + id);
        }
//        LambdaQueryWrapper<CalculationResult> wrapper2 = new LambdaQueryWrapper<>();
//        wrapper2.eq(CalculationResult::getId, calculationResultId);
        List<Long> materialsIdList = calculationResultList.stream().map(calculationResult -> {
            return calculationResult.getRawMaterialId();
        }).collect(Collectors.toList());
        List<StandardRawMaterialsVO> standardRawMaterialsVOList=calculationResultMapper.selectMaterialsByIds(materialsIdList,id);

        for (StandardRawMaterialsVO standardRawMaterialsVO : standardRawMaterialsVOList) {
            BigDecimal wieght = standardRawMaterialsVO.getWieght();
            BigDecimal price = standardRawMaterialsVO.getPrice();
            if(price!=null){
                standardRawMaterialsVO.setCost(price.multiply(wieght));
            }
        }
        //返回StandardIngredientRecordsResultVO
        StandardIngredientRecordsResultVO build = StandardIngredientRecordsResultVO.builder()
                .purposeCompositionsVOList(purposeCompositionsVOList)
                .standardRawMaterialsVOList(standardRawMaterialsVOList)
                .build();
        return build;
    }

    @Override
    public IPage<StandardIngredientRecordsVO> selectDraft(StandardIngredientRecordsQueryDTO standardIngredientRecordsQueryDTO) {
        Long steelGradeId= null;
        if ( standardIngredientRecordsQueryDTO.getSteelGrade().length() > 0) {
            //1、根据steelGrade查询steelGradeId
            LambdaQueryWrapper<SteelGrades> steelwrapper = new LambdaQueryWrapper<>();
            steelwrapper.like(StringUtils.hasLength(standardIngredientRecordsQueryDTO.getSteelGrade()), SteelGrades::getSteelGrade, standardIngredientRecordsQueryDTO.getSteelGrade());
            SteelGrades steelGrades = steelGradesService.getOne(steelwrapper);
            // 空值校验
            if ( steelGrades != null){
                steelGradeId = steelGrades.getId();
            }
        }
        Long standardId= null;
        if ( standardIngredientRecordsQueryDTO.getStandardName().length() > 0) {
            //2、根据standardName查询standardId
            LambdaQueryWrapper<ExecutionStandard> standardwrapper = new LambdaQueryWrapper<>();
            standardwrapper.like(StringUtils.hasLength(standardIngredientRecordsQueryDTO.getStandardName()), ExecutionStandard::getStandardName, standardIngredientRecordsQueryDTO.getStandardName());
            ExecutionStandard executionStandard = executionStandardService.getOne(standardwrapper);
            // 空值校验
            if ( executionStandard != null){
                standardId = executionStandard.getId();
            }
        }
        //3、根据steelGradeId和standardId以及时间范围查询standardIngredientRecords
        LambdaQueryWrapper<StandardIngredientRecords> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardIngredientRecords::getStatus, 0);
        wrapper.eq(Objects.nonNull(steelGradeId), StandardIngredientRecords::getSteelGradeId, steelGradeId);
        wrapper.eq(Objects.nonNull(standardId), StandardIngredientRecords::getExecutionStandardId, standardId);
        
        // 时间范围查询
        if (Objects.nonNull(standardIngredientRecordsQueryDTO.getStartmixingDate()) && 
            Objects.nonNull(standardIngredientRecordsQueryDTO.getEndmixingDate())) {
            LocalDateTime startOfDay = standardIngredientRecordsQueryDTO.getStartmixingDate().withHour(0).withMinute(0).withSecond(0);
            LocalDateTime endOfDay = standardIngredientRecordsQueryDTO.getEndmixingDate().withHour(23).withMinute(59).withSecond(59);
            wrapper.ge(StandardIngredientRecords::getMixingDate, startOfDay)
                   .le(StandardIngredientRecords::getMixingDate, endOfDay);
        }
        
        Page<StandardIngredientRecordsVO> page = new Page<>(standardIngredientRecordsQueryDTO.getPageNo(), standardIngredientRecordsQueryDTO.getPageSize());
        return baseMapper.selectRecords(page, wrapper);

    }

    @Override
    public void updateIngredientByResultId(IngredientResultDTO ingredientResultDTO) {

        if (ingredientResultDTO.getStandardIngredientRecordId() == null|| ingredientResultDTO.getCalculationResultId() == null) {
            throw new IllegalArgumentException("id不能为空");
        }
        StandardIngredientRecords standardIngredientRecords = baseMapper.selectById(ingredientResultDTO.getStandardIngredientRecordId());
        if (standardIngredientRecords == null) {
            throw new IllegalArgumentException("标准配料记录不存在，id=" + ingredientResultDTO.getStandardIngredientRecordId());
        }
        standardIngredientRecords.setCalculationResultId(ingredientResultDTO.getCalculationResultId());
        baseMapper.updateById(standardIngredientRecords);
    }

    @Override
    public AllDataVO getRecords(Long id) {
        AllDataVO allDataVO=new AllDataVO();
        // 获得的参数是配料记录表的ID
        //需要的数据有钢种，执行标准名称，特殊说明，工艺路径，目标成分，原料清单以及计算结果
        if (id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
        StandardIngredientRecords standardIngredientRecords = baseMapper.selectById(id);
        if (standardIngredientRecords == null) {
            throw new IllegalArgumentException("标准配料记录不存在，id=" + id);
        }
        //钢种通过配料记录表的steelGradeId找到钢种表拿到数据
        Long steelGradeId = standardIngredientRecords.getSteelGradeId();
        SteelGrades steelGrades = steelGradesService.getById(steelGradeId);
        if (steelGrades == null) {
            throw new IllegalArgumentException("钢种不存在，steelGradeId=" + steelGradeId);
        }
        String steelGrade = steelGrades.getSteelGrade();
        allDataVO.setSteelGrade(steelGrade);
        //标准名称通过配料记录表的executionStandardId找到标准表拿到数据
        Long executionStandardId = standardIngredientRecords.getExecutionStandardId();
        ExecutionStandard executionStandard = executionStandardService.getById(executionStandardId);
        if (executionStandard == null) {
            throw new IllegalArgumentException("标准不存在，executionStandardId=" + executionStandardId);
        }
        String standardName = executionStandard.getStandardName();
        allDataVO.setStandardName(standardName);
        //特殊说明通过配料记录表的id找到数据
        String specialNotes = standardIngredientRecords.getSpecialNotes();
        allDataVO.setSpecialNotes(specialNotes);
        //工艺路径通过配料记录表的processPathId找到工艺路径表拿到数据
        Long processPathId = standardIngredientRecords.getProcessPathId();
        ProcessPath processPath = new ProcessPath();
        processPath= processPathMapper.selectById(processPathId);
        ProcessPathVO build=new ProcessPathVO();
        if(processPath!=null){
            build = ProcessPathVO.builder()
                    .id(processPath.getId())
                    .steelGradesId(processPath.getSteelGradesId())
                    .pathName(processPath.getPathName())
                    .purpose(processPath.getPurpose())
                    .note(processPath.getNote())
                    .frequence(processPath.getFrequence())
                    .materialYield(processPath.getMaterialYield())
                    .createuser(processPath.getCreateuser())
                    .createtime(processPath.getCreatetime())
                    .updatetime(processPath.getUpdatetime())
                    .build();
            //查找当前路径对应的工序以及设备信息
            List<PathSaveDTO> processPathSteps=processPathStepsService.selectProcessPathSteps(build);
            build.setSteps(processPathSteps);
            allDataVO.setProcessPath(build);
        }
        //目标成分通过配料记录表的id找到目标成分表拿到数据
        LambdaQueryWrapper<PurposeCompositions> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PurposeCompositions::getStandardIngredientRecordId, id)
               .orderByAsc(PurposeCompositions::getSortOrder);
        List<PurposeCompositions> list = purposeCompositionsMapper.selectList(wrapper);
        List<PurposeCompositionsVO> purposeCompositions = BeanUtil.copyToList(list, PurposeCompositionsVO.class);
        allDataVO.setPurposeCompositions(purposeCompositions);

        //查询执行标准的标准成分
        LambdaQueryWrapper<StandardCompositions> standardWrapper = new LambdaQueryWrapper<>();
        standardWrapper.eq(StandardCompositions::getStandardId, executionStandardId)
                      .orderByAsc(StandardCompositions::getElementName);
        List<StandardCompositions> standardCompositionsList = standardCompositionsMapper.selectList(standardWrapper);
        List<StandardCompositionsVO> standardCompositions = BeanUtil.copyToList(standardCompositionsList, StandardCompositionsVO.class);
        allDataVO.setStandardCompositions(standardCompositions);
        log.info("查询执行标准的标准成分，标准ID: {}, 成分数量: {}", executionStandardId, standardCompositions.size());
        //原料清单表通过工艺路径表的id找到路径原料中间表拿到原料ID
        List<PathMaterials> pathMaterials = pathMaterialsMapper.selectList(new LambdaQueryWrapper<PathMaterials>().eq(PathMaterials::getPathId, processPathId));
        //再去原料表找到数据
        List<Long> rawMaterialIds = pathMaterials.stream().map(PathMaterials::getRawMaterialId).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(rawMaterialIds)){
//            List<StandardRawMaterials> standardRawMaterials = standardRawMaterialsMapper.selectBatchIds(rawMaterialIds);
//            List<StandardRawMaterialsVO> standardRawMaterialsVO = BeanUtil.copyToList(standardRawMaterials, StandardRawMaterialsVO.class);
            List<StandardRawMaterialsVO> standardRawMaterialsVO=calculationResultMapper.selectMaterialsByIds(rawMaterialIds,id);
            allDataVO.setStandardRawMaterials(standardRawMaterialsVO);
        }
        //计算结果通过配料记录表的id找到计算结果表拿到数据
        // 根据配料记录状态决定是否查询计算结果
        Integer status = standardIngredientRecords.getStatus();
        if (status != null && status == 0) {
            // 草稿状态：不返回计算结果
            allDataVO.setCalculationResult(new ArrayList<>());
        } else {
            // 正常状态：查询所有批次的计算结果并按批次分组
            List<CalculationBatchVO> calculationBatches = getAllCalculationResultsByBatch(id);
            // 设置按批次分组的计算结果
            allDataVO.setCalculationResult(calculationBatches);
        }

        //查询金属料吨水单耗配置信息
        Long configId = standardIngredientRecords.getMetalMaterialWaterConsumptionConfigId();
        Integer consumption = standardIngredientRecords.getMetalMaterialWaterConsumption();

        // 设置金属料吨水单耗基本信息（无论配置记录是否存在）
        allDataVO.setMetalMaterialWaterConsumptionConfigId(configId);
        allDataVO.setMetalMaterialWaterConsumption(consumption);

        if (configId != null) {
            log.info("查询金属料吨水单耗配置，配置ID: {}", configId);
            // 根据配置ID查询金属料吨水单耗配置表获取冶炼方法和设备信息
            MetalMaterialWaterConsumptionConfig config = metalMaterialWaterConsumptionConfigService.getById(configId);
            if (config != null) {
                allDataVO.setMethod(config.getMethod());
                allDataVO.setDevice(config.getDevice());
                log.info("金属料吨水单耗配置查询成功 - 方法: {}, 设备: {}, 单耗: {}",
                         config.getMethod(), config.getDevice(), consumption);
            } else {
                log.warn("未找到金属料吨水单耗配置信息，配置ID: {}", configId);
            }
        } else {
            log.info("该配料记录未配置金属料吨水单耗");
        }

        // 查询成锭率集合
        LambdaQueryWrapper<IngredientIdingotResult> idingotWrapper = new LambdaQueryWrapper<>();
        idingotWrapper.eq(IngredientIdingotResult::getStandardIngredientRecordId, id);
        List<IngredientIdingotResult> ingredientIdingotResults = ingredientIdingotResultMapper.selectList(idingotWrapper);
        List<IngredientIdingotResultVO> ingredientIdingotResultVOs = BeanUtil.copyToList(ingredientIdingotResults, IngredientIdingotResultVO.class);
        allDataVO.setIngredientIdingotResults(ingredientIdingotResultVOs);
        log.info("查询成锭率记录数量: {}", ingredientIdingotResultVOs.size());

        // 查询成材率集合
        LambdaQueryWrapper<IngredientYieldResult> yieldWrapper = new LambdaQueryWrapper<>();
        yieldWrapper.eq(IngredientYieldResult::getStandardIngredientRecordId, id);
        List<IngredientYieldResult> ingredientYieldResults = ingredientYieldResultMapper.selectList(yieldWrapper);
        List<IngredientYieldResultVO> ingredientYieldResultVOs = BeanUtil.copyToList(ingredientYieldResults, IngredientYieldResultVO.class);
        allDataVO.setIngredientYieldResults(ingredientYieldResultVOs);
        log.info("查询成材率记录数量: {}", ingredientYieldResultVOs.size());

        //返回AllDataVO
        return allDataVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdateMainTable(StandardIngredientRecordsDTO standardIngredientRecordsDTO) {
        // 首先拿到的数据是一系列ID，分别为工艺路径表ID，目标成分表ID，钢种ID，标准ID
        String costEstimattionId = UserContext.getCurrentUser().getCostEstimattionId();
        String userName = UserContext.getCurrentUser().getUsername();
        // 先判断主表ID存不存在
            // 存在则更新，不存在则插入
            //先保存钢种以及标准
            Long steelGradeId = standardIngredientRecordsDTO.getSteelGradeId();
            Long executionStandardId = standardIngredientRecordsDTO.getExecutionStandardId();
            if (steelGradeId == null || executionStandardId == null) {
                throw new RuntimeException("钢种ID或标准ID为空");
            }
            StandardIngredientRecords standardIngredientRecords = new StandardIngredientRecords();
            standardIngredientRecords.setSteelGradeId(steelGradeId);
            standardIngredientRecords.setExecutionStandardId(executionStandardId);
            //如果不存在，先把一些ID存入主表
            standardIngredientRecords.setCostEstimattionId(Long.valueOf(costEstimattionId));
            standardIngredientRecords.setUserName(userName);
            //默认情况下，类别为标准配料，配料日期以及发布日期为当前时间
            standardIngredientRecords.setCategory("标准配料");
            standardIngredientRecords.setMixingDate(LocalDateTime.now());
            standardIngredientRecords.setReleaseDate(LocalDateTime.now());
            standardIngredientRecords.setProcessPathId( standardIngredientRecordsDTO.getProcessPathId());
            standardIngredientRecords.setSpecialNotes( standardIngredientRecordsDTO.getSpecialNotes());
            if(standardIngredientRecordsDTO.getId()!=null){
                baseMapper.updateById(standardIngredientRecords);
            }else {
                baseMapper.insert(standardIngredientRecords);
            }
            //根据工艺路径ID去查找原料，得到，原材料总量，总成本，保存完后得到主表ID
            //原料总量，总成本目前无法查询，需要在计算结果得出后再去更新
            //然后去目标成分表中设置主表ID
            standardIngredientRecordsDTO.getPurposeCompositionIds().forEach(purposeCompositionId -> {
                LambdaQueryWrapper<PurposeCompositions> purposeCompositionWrapper = new LambdaQueryWrapper<>();
                purposeCompositionWrapper.eq(PurposeCompositions::getId, purposeCompositionId);
                PurposeCompositions purposeCompositions = purposeCompositionsMapper.selectOne(purposeCompositionWrapper);
                purposeCompositions.setStandardIngredientRecordId(standardIngredientRecords.getId());
                purposeCompositionsMapper.updateById(purposeCompositions);
            });
            //然后去配料单成锭率表中设置主表ID
            standardIngredientRecordsDTO.getIdIngotRateIds().forEach(idIngotRateId -> {
                LambdaQueryWrapper<IngredientIdingotResult> idIngotRateWrapper = new LambdaQueryWrapper<>();
                idIngotRateWrapper.eq(IngredientIdingotResult::getId, idIngotRateId);
                IngredientIdingotResult ingredientIdingotResult = ingredientIdingotResultMapper.selectOne(idIngotRateWrapper);
                ingredientIdingotResult.setStandardIngredientRecordId(standardIngredientRecords.getId());
                ingredientIdingotResultMapper.updateById(ingredientIdingotResult);
            });
            //然后去配料单成材率表中设置主表ID
            standardIngredientRecordsDTO.getYieldRateIds().forEach(yieldRateId -> {
                LambdaQueryWrapper<IngredientYieldResult> yieldRateWrapper = new LambdaQueryWrapper<>();
                yieldRateWrapper.eq(IngredientYieldResult::getId, yieldRateId);
                IngredientYieldResult ingredientYieldResult = ingredientYieldResultMapper.selectOne(yieldRateWrapper);
                ingredientYieldResult.setStandardIngredientRecordId(standardIngredientRecords.getId());
                ingredientYieldResultMapper.updateById(ingredientYieldResult);
            });

        return standardIngredientRecords.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HttpResVo sendData(Long id) {
        //根据配料主表id
        LambdaQueryWrapper<StandardIngredientRecords> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardIngredientRecords::getId, id);
        StandardIngredientRecords standardIngredientRecords = baseMapper.selectOne(wrapper);
        //通过配料主表拿到一系列数据封装到RequestData
        //去成本测算表拿到成本测算ID，成本测算表只保存一条数据
        Long costEstimattionId = standardIngredientRecords.getCostEstimattionId();
        CostEstimation costEstimation = costEstimationMapper.selectOne(new LambdaQueryWrapper<CostEstimation>().eq(CostEstimation::getId, costEstimattionId));

        //分别需要成本测算ID，目标成分集合，计算结果集合，成锭率集合，成材率集合
        //成本测算ID
        RequestData requestData = new RequestData();
        requestData.setId(costEstimation.getTaskId());
        requestData.setApproveUserId(costEstimation.getUserIds().toString());
        requestData.setCostEstimationId(costEstimation.getId().toString());
        //目标成分
        LambdaQueryWrapper<PurposeCompositions> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(PurposeCompositions::getStandardIngredientRecordId, standardIngredientRecords.getId());
        List<PurposeCompositions> purposeCompositions = purposeCompositionsMapper.selectList(wrapper1);
        //遍历一下，把id设置为null
        for (PurposeCompositions purposeComposition : purposeCompositions) {
            purposeComposition.setId(null);
        }
        requestData.setPurposeCompositionsList(purposeCompositions);
        //计算结果
        LambdaQueryWrapper<CalculationResult> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(CalculationResult::getStandardIngredientRecordId, standardIngredientRecords.getId());
        List<CalculationResult> calculationResults = calculationResultMapper.selectList(wrapper2);
        //转为VO
        List<CalculationResultVO> calculationResultVOS = BeanUtil.copyToList(calculationResults, CalculationResultVO.class);
        //遍历计算结果集合，拿到原料ID，去原料表拿到价格，再设置价格，拿到目标成分ID，去目标成分表拿到minValue和maxValue，再设置minValue和maxValue
        for (CalculationResultVO calculationResult : calculationResultVOS) {
            //拿到原料id去原料表拿到价格
            Long rawMaterialId = calculationResult.getRawMaterialId();
            StandardRawMaterials standardRawMaterials = standardRawMaterialsMapper.selectById(rawMaterialId);
            if (standardRawMaterials == null) {
                throw new RuntimeException("未找到对应的原料信息");
            }
            calculationResult.setPrice(standardRawMaterials.getPrice());
            //拿到目标成分id去目标成分表拿到minValue和maxValue
            Long purposeCompositionId = calculationResult.getPurposeCompositionId();
            PurposeCompositions purposeCompositions1 = purposeCompositionsMapper.selectById(purposeCompositionId);
            if (purposeCompositions1 == null) {
                throw new RuntimeException("未找到对应的目标成分信息");
            }
            calculationResult.setMinValue(purposeCompositions1.getMinValue());
            calculationResult.setMaxValue(purposeCompositions1.getMaxValue());
        }
        //遍历一下，把id设置为null
        for (CalculationResultVO calculationResultVO : calculationResultVOS) {
            calculationResultVO.setId(null);
        }
        requestData.setCalculationResultList(calculationResultVOS);
        //成锭率
        //根据配料单ID去IngredientIdingotResultMapper拿到数据
        LambdaQueryWrapper<IngredientIdingotResult> wrapper3 = new LambdaQueryWrapper<>();
        wrapper3.eq(IngredientIdingotResult::getStandardIngredientRecordId, standardIngredientRecords.getId());
        List<IngredientIdingotResult> ingredientIdingotResults = ingredientIdingotResultMapper.selectList(wrapper3);
        //遍历根据成锭率ID去成锭率表里面拿到device，顺便把主键设置为Null
        for (IngredientIdingotResult ingredientIdingotResult : ingredientIdingotResults) {
            ingredientIdingotResult.setId(null);
            //拿到成锭率ID去成锭率表里面拿到device
            Long idingotId = ingredientIdingotResult.getIgingotId();
            IngotYieldRates ingotYieldRates = ingotYieldRatesMapper.selectById(idingotId);
            if (ingotYieldRates == null) {
                throw new RuntimeException("未找到对应的成锭率信息");
            }
            ingredientIdingotResult.setProductionDept(ingredientIdingotResult.getDepartmentName());
            ingredientIdingotResult.setDevice(ingotYieldRates.getDepartmentName());
        }
        requestData.setIngredientIdingotResultList(ingredientIdingotResults);
        //成材率
        //根据配料单ID去IngredientYieldResultMapper拿到数据
        LambdaQueryWrapper<IngredientYieldResult> wrapper4 = new LambdaQueryWrapper<>();
        wrapper4.eq(IngredientYieldResult::getStandardIngredientRecordId, standardIngredientRecords.getId());
        List<IngredientYieldResult> ingredientYieldResults = ingredientYieldResultMapper.selectList(wrapper4);
        //遍历根据成材率ID去成材率表里面拿到device,顺便把主键设置为Null
        for (IngredientYieldResult ingredientYieldResult : ingredientYieldResults) {
            ingredientYieldResult.setId(null);
            //拿到成材率ID去成材率表里面拿到device
            Long yieldId = ingredientYieldResult.getYieldId();
            MaterialYieldRates materialYieldRates = materialYieldRatesMapper.selectById(yieldId);
            if (materialYieldRates == null) {
                throw new RuntimeException("未找到对应的成材率信息");
            }
            ingredientYieldResult.setDevice(materialYieldRates.getDevice());
        }
        requestData.setIngredientYieldResultList(ingredientYieldResults);
        //然后请求远程接口
        // 创建 HttpClient 并配置超时
        HttpClient httpClient = HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 10000) // 设置连接超时
                .responseTimeout(Duration.ofMillis(10000)); // 设置响应超时
        if(costEstimation.getNum()!=1){
            // 使用 WebClient 发起请求，并通过自定义 HttpClient 配置超时
            Mono<HttpResVo> responseMono = webClientBuilder
                    .clientConnector(new ReactorClientHttpConnector(httpClient)) // 使用自定义 HttpClient
                    .build()
                    .post() // 使用 POST 方法
                .uri(approveBatchingBaseUrl + batchingAgainEndpoint) // 使用配置的URL
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(requestData) // 请求体数据
                .retrieve() // 获取响应
                .bodyToMono(HttpResVo.class)
                .onErrorResume(WebClientRequestException.class, e -> {
                    if (e.getCause() instanceof ConnectException) {
                        return Mono.just(new HttpResVo()); // 连接失败时返回一个空的响应
                    }
                    return Mono.just(new HttpResVo());
                })
                .onErrorResume(Exception.class, e -> {
                    return Mono.just(new HttpResVo());
                });

//        //再通过成本测算ID删除这条数据，防止以后找不到唯一ID
//        LambdaQueryWrapper<CostEstimation> wrapper5 = new LambdaQueryWrapper<>();
//        wrapper5.eq(CostEstimation::getId, costEstimation.getId());
//        costEstimationMapper.delete(wrapper5);
            // 阻塞获取响应结果
            HttpResVo response = responseMono.block(); // 阻塞等待响应
            System.out.println(response);
            return response;
        }

        // 使用 WebClient 发起请求，并通过自定义 HttpClient 配置超时
        Mono<HttpResVo> responseMono = webClientBuilder
                .clientConnector(new ReactorClientHttpConnector(httpClient)) // 使用自定义 HttpClient
                .build()
                .post() // 使用 POST 方法
                .uri(approveBatchingBaseUrl + batchingEndpoint) // 使用配置的URL
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(requestData) // 请求体数据
                .retrieve() // 获取响应
                .bodyToMono(HttpResVo.class)
                .onErrorResume(WebClientRequestException.class, e -> {
                    if (e.getCause() instanceof ConnectException) {
                        System.out.println(e);
                        return Mono.just(new HttpResVo()); // 连接失败时返回一个空的响应
                    }
                    return Mono.just(new HttpResVo());
                })
                .onErrorResume(Exception.class, e -> {
                    System.out.println(e);
                    return Mono.just(new HttpResVo());
                });

//        //再通过成本测算ID删除这条数据，防止以后找不到唯一ID
//        LambdaQueryWrapper<CostEstimation> wrapper5 = new LambdaQueryWrapper<>();
//        wrapper5.eq(CostEstimation::getId, costEstimation.getId());
//        costEstimationMapper.delete(wrapper5);
        // 阻塞获取响应结果
        HttpResVo response = responseMono.block(); // 阻塞等待响应
        System.out.println(response);
        return response;
    }

//    @Override
//    public void download(Long id, HttpServletResponse response)  {
////        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("F:/桌面/产品成本测算流程表模板文件.xlsx");
////            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("E:\\DiFa\\产品成本测算流程表模板文件.xlsx");
//        File file = new File("images/产品成本测算流程表模板文件.xlsx");  // 相对于项目根目录
//        try {
//            InputStream inputStream = new FileInputStream(file);
//            //拿到的id是标准配料表id
//            //可以根据标准配料表ID去找到所有的数据
//            //根据成本测算ID找到配料主表
//            LambdaQueryWrapper<StandardIngredientRecords> wrapper = new LambdaQueryWrapper<>();
//            wrapper.eq(StandardIngredientRecords::getId, id);
//            StandardIngredientRecords standardIngredientRecords = baseMapper.selectOne(wrapper);
//            //去成本测算表拿到成本测算ID，成本测算表只保存一条数据
//            CostEstimation costEstimation = costEstimationMapper.selectOne(new LambdaQueryWrapper<CostEstimation>().last("limit 1"));
//            //通过配料主表拿到一系列数据封装到RequestData
//            //分别需要成本测算ID，目标成分集合，计算结果集合，成锭率集合，成材率集合
//            //成本测算ID
//            RequestData requestData = new RequestData();
//            requestData.setCostEstimationId(costEstimation.getId().toString());
//            //目标成分
//            LambdaQueryWrapper<PurposeCompositions> wrapper1 = new LambdaQueryWrapper<>();
//            wrapper1.eq(PurposeCompositions::getStandardIngredientRecordId, standardIngredientRecords.getId());
//            List<PurposeCompositions> purposeCompositions = purposeCompositionsMapper.selectList(wrapper1);
//            requestData.setPurposeCompositionsList(purposeCompositions);
//            //计算结果
//            LambdaQueryWrapper<CalculationResult> wrapper2 = new LambdaQueryWrapper<>();
//            wrapper2.eq(CalculationResult::getStandardIngredientRecordId, standardIngredientRecords.getId());
//            List<CalculationResult> calculationResults = calculationResultMapper.selectList(wrapper2);
//            //转为VO
//            List<CalculationResultVO> calculationResultVOS = BeanUtil.copyToList(calculationResults, CalculationResultVO.class);
//            //遍历计算结果集合，拿到原料ID，去原料表拿到价格，再设置价格，拿到目标成分ID，去目标成分表拿到minValue和maxValue，再设置minValue和maxValue
//            for (CalculationResultVO calculationResult : calculationResultVOS) {
//                //拿到原料id去原料表拿到价格
//                Long rawMaterialId = calculationResult.getRawMaterialId();
//                StandardRawMaterials standardRawMaterials = standardRawMaterialsMapper.selectById(rawMaterialId);
//                if (standardRawMaterials == null) {
//                    throw new RuntimeException("未找到对应的原料信息");
//                }
//                calculationResult.setPrice(standardRawMaterials.getPrice());
//                //拿到目标成分id去目标成分表拿到minValue和maxValue
//                Long purposeCompositionId = calculationResult.getPurposeCompositionId();
//                PurposeCompositions purposeCompositions1 = purposeCompositionsMapper.selectById(purposeCompositionId);
//                if (purposeCompositions1 == null) {
//                    throw new RuntimeException("未找到对应的目标成分信息");
//                }
//                calculationResult.setMinValue(purposeCompositions1.getMinValue());
//                calculationResult.setMaxValue(purposeCompositions1.getMaxValue());
//            }
//            requestData.setCalculationResultList(calculationResultVOS);
//            //成锭率
//            //根据配料单ID去IngredientIdingotResultMapper拿到数据
//            LambdaQueryWrapper<IngredientIdingotResult> wrapper3 = new LambdaQueryWrapper<>();
//            wrapper3.eq(IngredientIdingotResult::getStandardIngredientRecordId, standardIngredientRecords.getId());
//            List<IngredientIdingotResult> ingredientIdingotResults = ingredientIdingotResultMapper.selectList(wrapper3);
//            //遍历根据成锭率ID去成锭率表里面拿到device
//            for (IngredientIdingotResult ingredientIdingotResult : ingredientIdingotResults) {
//                //拿到成锭率ID去成锭率表里面拿到device
//                Long idingotId = ingredientIdingotResult.getIgingotId();
//                IngotYieldRates ingotYieldRates = ingotYieldRatesMapper.selectById(idingotId);
//                if (ingotYieldRates == null) {
//                    throw new RuntimeException("未找到对应的成锭率信息");
//                }
//                ingredientIdingotResult.setDevice(ingotYieldRates.getDevice());
//            }
//            requestData.setIngredientIdingotResultList(ingredientIdingotResults);
//            //成材率
//            //根据配料单ID去IngredientYieldResultMapper拿到数据
//            LambdaQueryWrapper<IngredientYieldResult> wrapper4 = new LambdaQueryWrapper<>();
//            wrapper4.eq(IngredientYieldResult::getStandardIngredientRecordId, standardIngredientRecords.getId());
//            List<IngredientYieldResult> ingredientYieldResults = ingredientYieldResultMapper.selectList(wrapper4);
//            //遍历根据成材率ID去成材率表里面拿到device
//            for (IngredientYieldResult ingredientYieldResult : ingredientYieldResults) {
//                //拿到成材率ID去成材率表里面拿到device
//                Long yieldId = ingredientYieldResult.getYieldId();
//                MaterialYieldRates materialYieldRates = materialYieldRatesMapper.selectById(yieldId);
//                if (materialYieldRates == null) {
//                    throw new RuntimeException("未找到对应的成材率信息");
//                }
//                ingredientYieldResult.setDevice(materialYieldRates.getDevice());
//            }
//            requestData.setIngredientYieldResultList(ingredientYieldResults);
//            //现在测算表的第一部分数据拿到了就在costEstimation里面
//            //第二部分也拿到了就是requestData
//            //然后第三部分没有数据不需要查
//            //完成续写
//            // 2. 填充Excel模板
////            System.out.println(this.getClass().getClassLoader().getResource("/template/运营数据报表模板.xlsx").getPath());
//            response.reset(); // 重置响应头
//            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
//            String fileName = URLEncoder.encode("产品成本测算流程表.xlsx", "UTF-8");
//            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
//
//
//            XSSFWorkbook excel = new XSSFWorkbook(inputStream);
//            //获得Excel文件中的一个Sheet页
//            XSSFSheet sheet = excel.getSheet("Sheet1");
//            //日期
//            sheet.getRow(0).getCell(5).setCellValue(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
//            //编号
//            sheet.getRow(0).getCell(17).setCellValue(costEstimation.getEstimationNo());
//            //用户名称
//            sheet.getRow(1).getCell(6).setCellValue(costEstimation.getUsername());
//            //公司名称
//            sheet.getRow(1).getCell(18).setCellValue(costEstimation.getCompanyName());
//            //钢号
//            sheet.getRow(2).getCell(6).setCellValue(costEstimation.getSteelNumber());
//            //成品规格
//            sheet.getRow(2).getCell(14).setCellValue(costEstimation.getFinishedProductSpecification());
//            //预计订货量
//            sheet.getRow(2).getCell(18).setCellValue(costEstimation.getEstimatedOrderQuantity());
//            //长度交货状态
//            sheet.getRow(3).getCell(6).setCellValue(costEstimation.getLengthDeliveryStatus());
//            //表面交货状态
//            sheet.getRow(3).getCell(10).setCellValue(costEstimation.getSurfaceDeliveryStatus());
//            //热处理交货状态
//            sheet.getRow(3).getCell(14).setCellValue(costEstimation.getHeatDeliveryStatus());
//            //是否更改标准成本
//            sheet.getRow(3).getCell(18).setCellValue(costEstimation.getChangeStandardCost());
//            //技术标准以及说明
//            sheet.getRow(4).getCell(6).setCellValue(costEstimation.getTechnicalStandard());
//            //生产工艺路线
//            sheet.getRow(5).getCell(6).setCellValue(costEstimation.getProcessRoute());
//            //化学成分
//            List<PurposeCompositions> purposeCompositionsList = requestData.getPurposeCompositionsList();
//            for (int i = 0; i < 15; i++) {
//                PurposeCompositions purposeCompositions2 = purposeCompositionsList.get(i);
//                sheet.getRow(7).getCell(5+i).setCellValue(purposeCompositions2.getElementName());
//                sheet.getRow(8).getCell(5+i).setCellValue(purposeCompositions2.getMinValue().doubleValue());
//                sheet.getRow(9).getCell(5+i).setCellValue(purposeCompositions2.getMaxValue().doubleValue());
//            }
//            //冶炼配料
//            List<CalculationResultVO> calculationResultList = requestData.getCalculationResultList();
//            Double totalConsume= 0.0;
//            for (int i = 0; i < calculationResultList.size(); i++) {
//                CalculationResultVO calculationResultVO = calculationResultList.get(i);
//                sheet.getRow(12+i).getCell(4).setCellValue(calculationResultVO.getRawMaterialName());
//                sheet.getRow(12+i).getCell(6).setCellValue(calculationResultVO.getPrice().doubleValue());
//                sheet.getRow(12+i).getCell(8).setCellValue(calculationResultVO.getWieght().doubleValue());
//                sheet.getRow(12+i).getCell(10).setCellValue(calculationResultVO.getComposition().doubleValue());
//                sheet.getRow(12+i).getCell(12).setCellValue(calculationResultVO.getRecoveryRate().doubleValue());
//                sheet.getRow(12+i).getCell(14).setCellValue(calculationResultVO.getSingleConsume());
//                if(calculationResultVO.getSingleConsume()!=null){
//                    totalConsume = Double.parseDouble(calculationResultVO.getSingleConsume())+totalConsume;
//                }
//                sheet.getRow(12+i).getCell(16).setCellValue(calculationResultVO.getMaxValue().doubleValue());
//                sheet.getRow(12+i).getCell(18).setCellValue(calculationResultVO.getMinValue().doubleValue());
//            }
//            sheet.getRow(26).getCell(14).setCellValue(totalConsume);
//            //冶炼
//            List<IngredientIdingotResult> ingredientIdingotResultList = requestData.getIngredientIdingotResultList();
//            if(ingredientIdingotResultList.size()>=1){
//                IngredientIdingotResult ingredientIdingotResult = ingredientIdingotResultList.get(0);
//                sheet.getRow(29).getCell(4).setCellValue(ingredientIdingotResult.getDepartmentName());
//                sheet.getRow(29).getCell(6).setCellValue(ingredientIdingotResult.getDevice());
//                sheet.getRow(29).getCell(9).setCellValue(ingredientIdingotResult.getIngotYield().doubleValue());
//            }
//            if(ingredientIdingotResultList.size()>=2){
//                IngredientIdingotResult ingredientIdingotResult1 = ingredientIdingotResultList.get(1);
//                sheet.getRow(29).getCell(12).setCellValue(ingredientIdingotResult1.getDepartmentName());
//                sheet.getRow(29).getCell(14).setCellValue(ingredientIdingotResult1.getDevice());
//                sheet.getRow(29).getCell(17).setCellValue(ingredientIdingotResult1.getIngotYield().doubleValue());
//            }
//            //加工
//            List<IngredientYieldResult> ingredientYieldResultList = requestData.getIngredientYieldResultList();
//            if(ingredientYieldResultList.size()>=1){
//                IngredientYieldResult ingredientYieldResult = ingredientYieldResultList.get(0);
//                sheet.getRow(32).getCell(4).setCellValue(ingredientYieldResult.getProductionDept());
//                sheet.getRow(32).getCell(6).setCellValue(ingredientYieldResult.getDevice());
//                sheet.getRow(32).getCell(9).setCellValue(ingredientYieldResult.getYieldId());
//            }
//            if(ingredientYieldResultList.size()>=2){
//                IngredientYieldResult ingredientYieldResult = ingredientYieldResultList.get(1);
//                sheet.getRow(32).getCell(12).setCellValue(ingredientYieldResult.getProductionDept());
//                sheet.getRow(32).getCell(14).setCellValue(ingredientYieldResult.getDevice());
//                sheet.getRow(32).getCell(17).setCellValue(ingredientYieldResult.getYieldId());
//
//            }
//            if(ingredientIdingotResultList.size()>=3){
//                IngredientYieldResult ingredientIdingotResult2 = ingredientIdingotResultList.get(3);
//                sheet.getRow(32).getCell(4).setCellValue(ingredientIdingotResult2.getProductionDept());
//                sheet.getRow(32).getCell(6).setCellValue(ingredientIdingotResult2.getDevice());
//                sheet.getRow(32).getCell(9).setCellValue(ingredientIdingotResult2.getYieldId());
//            }
//            if(ingredientYieldResultList.size()>=4){
//                IngredientYieldResult ingredientYieldResult = ingredientYieldResultList.get(4);
//                sheet.getRow(32).getCell(12).setCellValue(ingredientYieldResult.getProductionDept());
//                sheet.getRow(32).getCell(14).setCellValue(ingredientYieldResult.getDevice());
//                sheet.getRow(32).getCell(17).setCellValue(ingredientYieldResult.getYieldId());
//
//            }
//
//            // 3. 写入并关闭资源
//            ServletOutputStream out = response.getOutputStream();
//            excel.write(out);
//            out.flush();
//
//            // 4. 确保所有资源都正确关闭
//            inputStream.close();
//            excel.close();
//            out.close();
//            //通过输出流将文件下载到客户端浏览器中
////            ServletOutputStream out = response.getOutputStream();
////            excel.write(out);
//            //关闭资源
////            out.flush();
////            out.close();
////            excel.close();
//
//        } catch (Exception e) {
//            log.error("导出成本测算流程表失败", e);
//            throw new RuntimeException("导出成本测算流程表失败: " + e.getMessage());
//        }
//
//
//    }
//
//    @Override
//    public ResponseEntity<String> previewDownload(Long id) {
//
//
//    }

    private XSSFWorkbook generateExcel(Long id) throws Exception {
        // 调用新方法，使用默认批次（向后兼容）
        return generateExcel(id, null);
    }

    private XSSFWorkbook generateExcel(Long id, String calculationSequence) throws Exception {
        log.info("开始生成Excel，ID: {}, 批次: {}", id, calculationSequence);

        // 模板文件路径
        File file = new File("images/产品成本测算流程表模板文件.xlsx");
        log.info("模板文件路径: {}, 文件存在: {}", file.getAbsolutePath(), file.exists());

        if (!file.exists()) {
            throw new RuntimeException("模板文件不存在: " + file.getAbsolutePath());
        }

        InputStream inputStream = new FileInputStream(file);

        // 初始化请求数据
        log.info("开始获取数据，ID: {}, 批次: {}", id, calculationSequence);
        RequestData requestData = fetchData(id, calculationSequence);
        log.info("数据获取完成，ID: {}, 批次: {}", id, calculationSequence);

        // 创建Excel对象
        XSSFWorkbook excel = new XSSFWorkbook(inputStream);

        // 获得Excel文件中的一个Sheet页
        XSSFSheet sheet = excel.getSheet("Sheet1");
        if (sheet == null) {
            throw new RuntimeException("模板文件中未找到Sheet1工作表");
        }

        // 填充Excel表格数据
        log.info("开始填充Excel数据，ID: {}", id);
        fillSheetWithData(sheet, requestData);
        log.info("Excel数据填充完成，ID: {}", id);

        // 关闭输入流
        inputStream.close();
        return excel;
    }

    private void fillSheetWithData(XSSFSheet sheet, RequestData requestData) {
        CostEstimation costEstimation = costEstimationMapper.selectOne(new LambdaQueryWrapper<CostEstimation>()
                .eq(CostEstimation::getId, Long.parseLong(requestData.getCostEstimationId())));

        // 日期
        sheet.getRow(0).getCell(5).setCellValue(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        // 编号
        sheet.getRow(0).getCell(17).setCellValue(costEstimation.getEstimationNo());
        // 用户名称
        sheet.getRow(1).getCell(6).setCellValue(costEstimation.getUsername());
        // 公司名称
        sheet.getRow(1).getCell(18).setCellValue(costEstimation.getCompanyName());
        // 钢号
        sheet.getRow(2).getCell(6).setCellValue(costEstimation.getSteelNumber());
        // 成品规格
        sheet.getRow(2).getCell(14).setCellValue(costEstimation.getFinishedProductSpecification());
        // 预计订货量
        sheet.getRow(2).getCell(18).setCellValue(costEstimation.getEstimatedOrderQuantity());
        // 长度交货状态
        sheet.getRow(3).getCell(6).setCellValue(costEstimation.getLengthDeliveryStatus());
        // 表面交货状态
        sheet.getRow(3).getCell(10).setCellValue(costEstimation.getSurfaceDeliveryStatus());
        // 热处理交货状态
        sheet.getRow(3).getCell(14).setCellValue(costEstimation.getHeatDeliveryStatus());
        // 是否更改标准成本
        sheet.getRow(3).getCell(18).setCellValue(costEstimation.getChangeStandardCost());
        // 技术标准以及说明
        sheet.getRow(4).getCell(6).setCellValue(costEstimation.getTechnicalStandard());
        // 生产工艺路线
        sheet.getRow(5).getCell(6).setCellValue(costEstimation.getProcessRoute());

        // 化学成分,判断是否为空
        List<PurposeCompositions> purposeCompositionsList = requestData.getPurposeCompositionsList();
        if(purposeCompositionsList!=null){
            for (int i = 0; i < Math.min(15, purposeCompositionsList.size()); i++) {
                PurposeCompositions purposeCompositions2 = purposeCompositionsList.get(i);
                sheet.getRow(7).getCell(5 + i).setCellValue(purposeCompositions2.getElementName());
                sheet.getRow(8).getCell(5 + i).setCellValue(purposeCompositions2.getMinValue().doubleValue());
                sheet.getRow(9).getCell(5 + i).setCellValue(purposeCompositions2.getMaxValue().doubleValue());
            }
        }

        // 冶炼配料
        List<CalculationResultVO> calculationResultList = requestData.getCalculationResultList();
       if(calculationResultList!=null){
           Double totalConsume = 0.0;
           for (int i = 0; i < calculationResultList.size(); i++) {
               CalculationResultVO calculationResultVO = calculationResultList.get(i);
               sheet.getRow(12 + i).getCell(4).setCellValue(calculationResultVO.getRawMaterialName());
               // 安全处理价格字段，避免空指针异常
               BigDecimal price = calculationResultVO.getPrice();
               sheet.getRow(12 + i).getCell(6).setCellValue(price != null ? price.doubleValue() : 0.0);
               // 安全处理其他可能为空的字段
               BigDecimal weight = calculationResultVO.getWieght();
               sheet.getRow(12 + i).getCell(8).setCellValue(weight != null ? weight.doubleValue() : 0.0);
               BigDecimal composition = calculationResultVO.getComposition();
               sheet.getRow(12 + i).getCell(10).setCellValue(composition != null ? composition.doubleValue() : 0.0);
               BigDecimal recoveryRate = calculationResultVO.getRecoveryRate();
               sheet.getRow(12 + i).getCell(12).setCellValue(recoveryRate != null ? recoveryRate.doubleValue() : 0.0);
               sheet.getRow(12 + i).getCell(14).setCellValue(calculationResultVO.getSingleConsume());
               if (calculationResultVO.getSingleConsume() != null) {
                   totalConsume += Double.parseDouble(calculationResultVO.getSingleConsume());
               }
               // 安全处理最大值和最小值字段
               BigDecimal maxValue = calculationResultVO.getMaxValue();
               sheet.getRow(12 + i).getCell(16).setCellValue(maxValue != null ? maxValue.doubleValue() : 0.0);
               BigDecimal minValue = calculationResultVO.getMinValue();
               sheet.getRow(12 + i).getCell(18).setCellValue(minValue != null ? minValue.doubleValue() : 0.0);
           }
           sheet.getRow(26).getCell(14).setCellValue(totalConsume);
       }

        // 冶炼
        List<IngredientIdingotResult> ingredientIdingotResultList = requestData.getIngredientIdingotResultList();
       if(ingredientIdingotResultList!=null){
           if (ingredientIdingotResultList.size() >= 1) {
               IngredientIdingotResult ingredientIdingotResult = ingredientIdingotResultList.get(0);
               sheet.getRow(29).getCell(4).setCellValue(ingredientIdingotResult.getDepartmentName());
               sheet.getRow(29).getCell(6).setCellValue(ingredientIdingotResult.getDevice());
               sheet.getRow(29).getCell(9).setCellValue(ingredientIdingotResult.getIngotYield().doubleValue());
           }
           if (ingredientIdingotResultList.size() >= 2) {
               IngredientIdingotResult ingredientIdingotResult1 = ingredientIdingotResultList.get(1);
               sheet.getRow(29).getCell(12).setCellValue(ingredientIdingotResult1.getDepartmentName());
               sheet.getRow(29).getCell(14).setCellValue(ingredientIdingotResult1.getDevice());
               sheet.getRow(29).getCell(17).setCellValue(ingredientIdingotResult1.getIngotYield().doubleValue());
           }
       }

        // 加工
        List<IngredientYieldResult> ingredientYieldResultList = requestData.getIngredientYieldResultList();
       if(ingredientYieldResultList!=null){
           if (ingredientYieldResultList.size() >= 1) {
               IngredientYieldResult ingredientYieldResult = ingredientYieldResultList.get(0);
               sheet.getRow(32).getCell(4).setCellValue(ingredientYieldResult.getProductionDept());
               sheet.getRow(32).getCell(6).setCellValue(ingredientYieldResult.getDevice());
               sheet.getRow(32).getCell(9).setCellValue(ingredientYieldResult.getYieldId());
           }
           if (ingredientYieldResultList.size() >= 2) {
               IngredientYieldResult ingredientYieldResult = ingredientYieldResultList.get(1);
               sheet.getRow(32).getCell(12).setCellValue(ingredientYieldResult.getProductionDept());
               sheet.getRow(32).getCell(14).setCellValue(ingredientYieldResult.getDevice());
               sheet.getRow(32).getCell(17).setCellValue(ingredientYieldResult.getYieldId());
           }
       }

        // 如果需要，可以继续添加更多数据
    }

    private RequestData fetchData(Long id) {
        // 调用新方法，使用默认批次（向后兼容）
        return fetchData(id, null);
    }

    private RequestData fetchData(Long id, String calculationSequence) {
        RequestData requestData = new RequestData();

        // 根据ID查找数据
        LambdaQueryWrapper<StandardIngredientRecords> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardIngredientRecords::getId, id);
        StandardIngredientRecords standardIngredientRecords = baseMapper.selectOne(wrapper);

        requestData.setCostEstimationId(standardIngredientRecords.getCostEstimattionId().toString());

        // 填充数据
        // 1. 目标成分
        List<PurposeCompositions> purposeCompositions = purposeCompositionsMapper.selectList(
                new LambdaQueryWrapper<PurposeCompositions>().eq(PurposeCompositions::getStandardIngredientRecordId, standardIngredientRecords.getId())
        );
        requestData.setPurposeCompositionsList(purposeCompositions);

        // 2. 计算结果（关键修改：支持批次参数）
        List<CalculationResult> calculationResults;
        if (calculationSequence == null || calculationSequence.trim().isEmpty()) {
            // 使用最新批次（向后兼容）
            calculationResults = getLatestCalculationResults(id);
            log.info("使用最新批次，ID: {}", id);
        } else {
            // 使用指定批次
            calculationResults = calculationResultMapper.selectBySequence(id, Integer.parseInt(calculationSequence));
            log.info("使用指定批次，ID: {}, 批次: {}", id, calculationSequence);
        }
        List<CalculationResultVO> calculationResultVOS = BeanUtil.copyToList(calculationResults, CalculationResultVO.class);

        for (CalculationResultVO calculationResult : calculationResultVOS) {
            Long rawMaterialId = calculationResult.getRawMaterialId();
            StandardRawMaterials standardRawMaterials = standardRawMaterialsMapper.selectById(rawMaterialId);
            calculationResult.setPrice(standardRawMaterials.getPrice());

            Long purposeCompositionId = calculationResult.getPurposeCompositionId();
            PurposeCompositions compositions = purposeCompositionsMapper.selectById(purposeCompositionId);
            calculationResult.setMinValue(compositions.getMinValue());
            calculationResult.setMaxValue(compositions.getMaxValue());
        }
        requestData.setCalculationResultList(calculationResultVOS);

        // 成锭率
        List<IngredientIdingotResult> ingredientIdingotResults = ingredientIdingotResultMapper.selectList(
                new LambdaQueryWrapper<IngredientIdingotResult>().eq(IngredientIdingotResult::getStandardIngredientRecordId, standardIngredientRecords.getId())
        );
        requestData.setIngredientIdingotResultList(ingredientIdingotResults);

        // 成材率
        List<IngredientYieldResult> ingredientYieldResults = ingredientYieldResultMapper.selectList(
                new LambdaQueryWrapper<IngredientYieldResult>().eq(IngredientYieldResult::getStandardIngredientRecordId, standardIngredientRecords.getId())
        );
        requestData.setIngredientYieldResultList(ingredientYieldResults);

        return requestData;
    }

    @Override
    public void download(Long id, HttpServletResponse response) {
        // 调用新方法，使用默认批次（向后兼容）
        download(id, null, response);
    }

    @Override
    public void download(Long id, String calculationSequence, HttpServletResponse response) {
        try {
            log.info("开始生成单个测算表，ID: {}, 批次: {}", id, calculationSequence);

            if (id == null) {
                throw new IllegalArgumentException("ID不能为空");
            }

            // 检查记录是否存在
            StandardIngredientRecords record = baseMapper.selectById(id);
            if (record == null) {
                throw new IllegalArgumentException("标准配料记录不存在，ID: " + id);
            }

            response.reset();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            String fileName = URLEncoder.encode("产品成本测算流程表.xlsx", "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            log.info("开始生成Excel文件，ID: {}, 批次: {}", id, calculationSequence);
            XSSFWorkbook excel = generateExcel(id, calculationSequence);

            log.info("开始写入响应流，ID: {}", id);
            ServletOutputStream out = response.getOutputStream();
            excel.write(out);
            out.flush();

            log.info("Excel文件写入完成，准备关闭流，ID: {}", id);
            excel.close();
            out.close();

            log.info("单个测算表生成完成，响应流已关闭，ID: {}", id);
        } catch (Exception e) {
            log.error("导出成本测算流程表失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            throw new RuntimeException("导出成本测算流程表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void batchDownload(List<Long> ids, HttpServletResponse response) {
        try {
            // 参数验证
            if (ids == null || ids.isEmpty()) {
                throw new IllegalArgumentException("ID列表不能为空");
            }

            if (ids.size() > 50) {
                throw new IllegalArgumentException("批量下载数量不能超过50个");
            }

            log.info("开始批量生成测算表，数量: {}", ids.size());

            response.reset();
            response.setContentType("application/zip");
            String zipFileName = URLEncoder.encode("测算表批量下载_" +
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".zip", "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + zipFileName);

            ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream());

            int successCount = 0;
            int failCount = 0;

            for (int i = 0; i < ids.size(); i++) {
                Long id = ids.get(i);
                try {
                    log.debug("正在生成测算表，ID: {}, 进度: {}/{}", id, i + 1, ids.size());

                    XSSFWorkbook excel = generateExcel(id);

                    // 创建ZIP条目，使用序号和ID命名
                    String entryName = String.format("测算表_%02d_ID_%s.xlsx", (i + 1), id);
                    ZipEntry entry = new ZipEntry(entryName);
                    zipOut.putNextEntry(entry);

                    // 写入Excel数据
                    excel.write(zipOut);
                    zipOut.closeEntry();
                    excel.close();

                    successCount++;
                    log.debug("成功生成测算表: {}", entryName);

                } catch (Exception e) {
                    failCount++;
                    log.error("生成测算表失败, ID: {}, 错误: {}", id, e.getMessage());

                    // 添加错误信息文件到ZIP中
                    try {
                        String errorFileName = String.format("错误报告_%02d_ID_%s.txt", (i + 1), id);
                        ZipEntry errorEntry = new ZipEntry(errorFileName);
                        zipOut.putNextEntry(errorEntry);

                        String errorContent = String.format("文件生成失败\nID: %s\n时间: %s\n错误信息: %s",
                            id, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                            e.getMessage());
                        zipOut.write(errorContent.getBytes("UTF-8"));
                        zipOut.closeEntry();
                    } catch (Exception errorException) {
                        log.error("写入错误报告失败: {}", errorException.getMessage());
                    }
                }
            }

            // 添加批量处理摘要文件
            try {
                ZipEntry summaryEntry = new ZipEntry("批量处理摘要.txt");
                zipOut.putNextEntry(summaryEntry);

                String summaryContent = String.format(
                    "批量生成测算表摘要\n" +
                    "生成时间: %s\n" +
                    "总数量: %d\n" +
                    "成功数量: %d\n" +
                    "失败数量: %d\n" +
                    "成功率: %.2f%%\n\n" +
                    "处理的ID列表:\n%s",
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                    ids.size(),
                    successCount,
                    failCount,
                    (double) successCount / ids.size() * 100,
                    ids.toString()
                );

                zipOut.write(summaryContent.getBytes("UTF-8"));
                zipOut.closeEntry();
            } catch (Exception summaryException) {
                log.error("写入摘要文件失败: {}", summaryException.getMessage());
            }

            zipOut.close();

            log.info("批量生成测算表完成，总数: {}, 成功: {}, 失败: {}", ids.size(), successCount, failCount);

        } catch (Exception e) {
            log.error("批量导出成本测算流程表失败", e);
            throw new RuntimeException("批量导出成本测算流程表失败: " + e.getMessage());
        }
    }

    @Override
    public void batchDownloadWithSequences(List<Long> ids, List<String> calculationSequences, HttpServletResponse response) {
        try {
            log.info("开始批量下载测算表（指定批次），ID列表: {}, 批次列表: {}", ids, calculationSequences);

            if (ids == null || ids.isEmpty()) {
                throw new IllegalArgumentException("ID列表不能为空");
            }

            if (calculationSequences == null || calculationSequences.size() != ids.size()) {
                throw new IllegalArgumentException("批次列表长度必须与ID列表长度一致");
            }

            // 设置ZIP文件响应头
            response.reset();
            response.setContentType("application/zip");
            String zipFileName = URLEncoder.encode("测算表批量下载.zip", "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + zipFileName);

            // 创建ZIP输出流
            ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream());
            int successCount = 0;
            int failCount = 0;

            for (int i = 0; i < ids.size(); i++) {
                Long id = ids.get(i);
                String sequence = calculationSequences.get(i);

                try {
                    log.info("生成Excel文件，ID: {}, 批次: {}", id, sequence);

                    // 检查记录是否存在
                    StandardIngredientRecords record = baseMapper.selectById(id);
                    if (record == null) {
                        log.warn("跳过不存在的记录，ID: {}", id);
                        failCount++;
                        continue;
                    }

                    XSSFWorkbook excel = generateExcel(id, sequence);

                    // 创建ZIP条目，文件名包含批次信息
                    String entryName = String.format("测算表_ID_%d_第%s次计算.xlsx", id, sequence);
                    ZipEntry zipEntry = new ZipEntry(entryName);
                    zipOut.putNextEntry(zipEntry);

                    // 写入Excel数据
                    excel.write(zipOut);
                    zipOut.closeEntry();
                    excel.close();

                    successCount++;
                    log.info("Excel文件添加到ZIP完成，ID: {}, 批次: {}", id, sequence);
                } catch (Exception e) {
                    log.error("生成Excel文件失败，ID: {}, 批次: {}", id, sequence, e);
                    failCount++;
                    // 继续处理下一个文件，不中断整个批量下载
                }
            }

            zipOut.close();
            log.info("批量下载测算表完成（指定批次），总数: {}, 成功: {}, 失败: {}", ids.size(), successCount, failCount);

        } catch (Exception e) {
            log.error("批量下载测算表失败（指定批次）", e);
            throw new RuntimeException("批量下载测算表失败: " + e.getMessage());
        }
    }

//    @Override
//    public ResponseEntity<String> previewDownload(Long id) {
//        try {
//            // 调用生成Excel的方法
//            XSSFWorkbook excel = generateExcel(id);
//
//            // 将文件转换为字节流
//            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//            excel.write(outputStream);
//            excel.close();
//
//            // 将字节流编码为Base64字符串
//            byte[] excelBytes = outputStream.toByteArray();
//            String base64Encoded = Base64.getEncoder().encodeToString(excelBytes);
//
//            // 返回作为文本响应
//            return ResponseEntity.ok(base64Encoded);
//
//        } catch (Exception e) {
//            log.error("预览成本测算流程表失败", e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("生成失败");
//        }
//    }

    @Override
    public void previewDownload(Long id, HttpServletResponse response) {
        // 调用新方法，使用默认批次（向后兼容）
        previewDownload(id, null, response);
    }

    @Override
    public void previewDownload(Long id, String calculationSequence, HttpServletResponse response) {
        try {
            response.setContentType("text/html;charset=UTF-8");
            XSSFWorkbook workbook = generateExcel(id, calculationSequence);
            XSSFSheet sheet = workbook.getSheetAt(0);

            // 获取所有合并单元格
            List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();
            Set<String> skipCells = new HashSet<>(); // 要跳过的被合并区域里的单元格
            Map<String, String> spanMap = new HashMap<>(); // 起始单元格 -> colspan/rowspan

            for (CellRangeAddress region : mergedRegions) {
                int firstRow = region.getFirstRow();
                int lastRow = region.getLastRow();
                int firstCol = region.getFirstColumn();
                int lastCol = region.getLastColumn();

                int rowspan = lastRow - firstRow + 1;
                int colspan = lastCol - firstCol + 1;
                String key = firstRow + "_" + firstCol;
                spanMap.put(key, (rowspan > 1 ? " rowspan='" + rowspan + "'" : "") +
                        (colspan > 1 ? " colspan='" + colspan + "'" : ""));

                // 记录被合并覆盖的单元格
                for (int r = firstRow; r <= lastRow; r++) {
                    for (int c = firstCol; c <= lastCol; c++) {
                        if (r != firstRow || c != firstCol) {
                            skipCells.add(r + "_" + c);
                        }
                    }
                }
            }

            StringBuilder html = new StringBuilder();
            html.append("<html><head><meta charset='UTF-8'><style>")
                    .append("table {border-collapse: collapse; width: 100%;}")
                    .append("td, th {border: 1px solid #ddd; padding: 8px; text-align: center;}")
                    .append("</style></head><body>");
            html.append("<h2 style='text-align: center;'>产品成本测算流程表</h2>");
            html.append("<table>");

            for (int i = sheet.getFirstRowNum(); i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }
                html.append("<tr>");
                for (int j = row.getFirstCellNum(); j < row.getLastCellNum(); j++) {
                    String cellKey = i + "_" + j;
                    if (skipCells.contains(cellKey)) {
                        continue; // 跳过合并区域中非起始的单元格
                    }

                    Cell cell = row.getCell(j);
                    String span = spanMap.getOrDefault(cellKey, "");
                    html.append("<td").append(span).append(">")
                            .append(getCellValue(cell))
                            .append("</td>");
                }
                html.append("</tr>");
            }

            html.append("</table></body></html>");
            response.getWriter().write(html.toString());

            workbook.close();
        } catch (Exception e) {
            log.error("预览成本测算流程表失败", e);
            throw new RuntimeException("预览成本测算流程表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIngredientByResultIds(IngredientResultIdsDTO ingredientResultIdsDTO) {
        // 获取参数
        Long standardIngredientRecordId = ingredientResultIdsDTO.getStandardIngredientRecordId();
        List<String> calculationResultIds = ingredientResultIdsDTO.getCalculationResultIds();
        
        // 查询该配料单ID下的所有计算结果
        LambdaQueryWrapper<CalculationResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CalculationResult::getStandardIngredientRecordId, standardIngredientRecordId);
        List<CalculationResult> existingResults = calculationResultMapper.selectList(wrapper);
        List<String> collect = existingResults.stream().map(existingResult -> existingResult.getId().toString())
                .collect(Collectors.toList());
        // 找出需要删除的记录ID
        List<String> deletedNames = new ArrayList<>();
        for (String id : collect) {
            if (!calculationResultIds.contains(id)) {
                // 删除不在新ID列表中的记录
                calculationResultMapper.deleteById(Long.parseLong(id));
            }
        }
    }

    @Autowired
    private StandardIngredientRecordsMapper standardIngredientRecordsMapper;

    @Override
    public boolean updateByNewPrice(Long id) {
        // 拿到的参数是主表id
        // 通过主表id找到路径
        StandardIngredientRecords standardIngredientRecords = standardIngredientRecordsMapper.selectById(id);
        Long processPathId = standardIngredientRecords.getProcessPathId();
        // 通过路径找到所有原料，给所有原料设置一个新的价格
        List<PathMaterials> pathMaterials = pathMaterialsMapper.selectList(new LambdaQueryWrapper<PathMaterials>().eq(PathMaterials::getPathId, processPathId));
        for (PathMaterials pathMaterial : pathMaterials) {
            Long rawMaterialId = pathMaterial.getRawMaterialId();
            StandardRawMaterials standardRawMaterials = standardRawMaterialsMapper.selectById(rawMaterialId);
            standardRawMaterials.setPrice(BigDecimal.valueOf(100));//先设置默认价格
            standardRawMaterialsMapper.updateById(standardRawMaterials);
        }
        //TODO 后面要接着调用计算方法，但是目前先不需要
        return true;
    }

    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                }
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 获取所有批次的计算结果并按批次分组
     * @param standardIngredientRecordId 配料记录ID
     * @return 按批次分组的计算结果列表
     */
    private List<CalculationBatchVO> getAllCalculationResultsByBatch(Long standardIngredientRecordId) {
        // 1. 查询所有计算序号（按升序排列，第1次计算在前）
        List<Integer> calculationSequences = calculationResultMapper.selectRecentCalculationSequences(
                standardIngredientRecordId, 100); // 最多查询100个批次

        if (calculationSequences.isEmpty()) {
            return new ArrayList<>();
        }

        List<CalculationBatchVO> batchList = new ArrayList<>();

        // 2. 为每个序号查询对应的计算结果
        for (Integer sequence : calculationSequences) {
            List<CalculationResult> batchResults = calculationResultMapper.selectBySequence(
                    standardIngredientRecordId, sequence);

            if (!batchResults.isEmpty()) {
                // 3. 转换为VO并设置价格等信息
                List<CalculationBatchResultVO> batchResultVOs = new ArrayList<>();
                LocalDateTime batchTime = null;

                for (CalculationResult result : batchResults) {
                    CalculationBatchResultVO resultVO = BeanUtil.copyProperties(result, CalculationBatchResultVO.class);

                    // 设置原料价格
                    Long rawMaterialId = result.getRawMaterialId();
                    StandardRawMaterials standardRawMaterials = standardRawMaterialsMapper.selectById(rawMaterialId);
                    if (standardRawMaterials != null) {
                        BigDecimal price = standardRawMaterials.getPrice();
                        if (price == null) {
                            log.warn("原料ID: {} 的价格为空，将使用默认值0", rawMaterialId);
                        }
                        resultVO.setPrice(price != null ? price : BigDecimal.ZERO);
                    } else {
                        log.warn("未找到原料信息，原料ID: {}", rawMaterialId);
                        resultVO.setPrice(BigDecimal.ZERO);
                    }

                    // 设置目标成分的最小值和最大值
                    Long purposeCompositionId = result.getPurposeCompositionId();
                    if (purposeCompositionId != null) {
                        PurposeCompositions purposeCompositions = purposeCompositionsMapper.selectById(purposeCompositionId);
                        if (purposeCompositions != null) {
                            resultVO.setMinValue(purposeCompositions.getMinValue());
                            resultVO.setMaxValue(purposeCompositions.getMaxValue());
                        } else {
                            log.warn("未找到目标成分信息，目标成分ID: {}", purposeCompositionId);
                        }
                    }

                    // 计算成本（价格 * 重量）
                    if (resultVO.getPrice() != null && result.getWieght() != null) {
                        BigDecimal cost = resultVO.getPrice().multiply(result.getWieght());
                        resultVO.setCost(cost);
                    } else {
                        resultVO.setCost(BigDecimal.ZERO);
                    }

                    batchResultVOs.add(resultVO);

                    // 记录批次时间（使用第一个结果的创建时间）
                    if (batchTime == null) {
                        batchTime = result.getCreatetime();
                    }
                }

                // 4. 创建批次VO
                CalculationBatchVO batchVO = CalculationBatchVO.builder()
                        .calculationSequence(sequence.toString())
                        .calculationTime(batchTime)
                        .results(batchResultVOs)
                        .batchNotes("第" + sequence + "次计算")
                        .build();

                batchList.add(batchVO);
            }
        }

        return batchList;
    }

    /**
     * 获取最新的计算结果（最大序号）
     * @param standardIngredientRecordId 配料记录ID
     * @return 最新的计算结果列表
     */
    private List<CalculationResult> getLatestCalculationResults(Long standardIngredientRecordId) {
        // 先查询最大的计算序号
        LambdaQueryWrapper<CalculationResult> maxSequenceWrapper = new LambdaQueryWrapper<>();
        maxSequenceWrapper.eq(CalculationResult::getStandardIngredientRecordId, standardIngredientRecordId)
                          .isNotNull(CalculationResult::getCalculationSequence)
                          .last("ORDER BY CAST(calculation_sequence AS INTEGER) DESC LIMIT 1");

        CalculationResult latestResult = calculationResultMapper.selectOne(maxSequenceWrapper);

        if (latestResult == null) {
            // 如果没有计算结果，返回空列表
            return new ArrayList<>();
        }

        String latestSequence = latestResult.getCalculationSequence();

        if (latestSequence == null || latestSequence.trim().isEmpty()) {
            // 如果序号为空，查询所有结果（兼容历史数据）
            LambdaQueryWrapper<CalculationResult> allWrapper = new LambdaQueryWrapper<>();
            allWrapper.eq(CalculationResult::getStandardIngredientRecordId, standardIngredientRecordId);
            return calculationResultMapper.selectList(allWrapper);
        } else {
            // 查询指定序号的所有计算结果
            LambdaQueryWrapper<CalculationResult> sequenceWrapper = new LambdaQueryWrapper<>();
            sequenceWrapper.eq(CalculationResult::getStandardIngredientRecordId, standardIngredientRecordId)
                           .eq(CalculationResult::getCalculationSequence, latestSequence);
            return calculationResultMapper.selectList(sequenceWrapper);
        }
    }


}
