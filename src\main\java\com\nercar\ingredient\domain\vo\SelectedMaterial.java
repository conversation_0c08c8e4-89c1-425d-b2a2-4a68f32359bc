package com.nercar.ingredient.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 选中原料类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class SelectedMaterial {
    /**
     * 原料名称
     */
    private String name;

    /**
     * 重量
     */
    private Double weight;

    /**
     * 成本
     */
    private Double cost;

    /**
     * 成分信息
     */
    private List<CompositionInfo> compositions;
}
