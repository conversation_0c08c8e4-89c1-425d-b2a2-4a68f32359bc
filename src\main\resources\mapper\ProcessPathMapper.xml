<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.ProcessPathMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.ProcessPath">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="pathName" column="path_name" jdbcType="VARCHAR"/>
            <result property="purpose" column="purpose" jdbcType="VARCHAR"/>
            <result property="note" column="note" jdbcType="VARCHAR"/>
            <result property="frequence" column="frequence" jdbcType="INTEGER"/>
            <result property="materialYield" column="material_yield" jdbcType="BIGINT"/>
            <result property="createuser" column="createuser" jdbcType="VARCHAR"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,path_name,purpose,
        note,frequence,material_yield,
        createuser,createtime,updatetime
    </sql>
    <select id="searchProcessPath" resultType="com.nercar.ingredient.domain.vo.ProcessPathVO">
        select pp.id,sg.steel_grade,pp.path_name,pp.frequence from process_path as pp
                                                                       left join steel_grades as sg on sg.id = pp.steel_grades_id
            ${ew.customSqlSegment}
    </select>
    <select id="selectProcessPath" resultType="com.nercar.ingredient.domain.vo.ProcessPathVO">
        select * from process_path
    </select>
</mapper>
