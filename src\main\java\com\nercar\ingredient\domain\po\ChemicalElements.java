package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 化学元素表
 * @TableName chemical_elements
 */
@TableName(value ="chemical_elements")
@Data
public class ChemicalElements implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 元素名称
     */
    private String elementName;

    /**
     * 元素符号
     */
    private String elementSymbol;

    /**
     * 
     */
    private Date createtime;

    /**
     * 
     */
    private Date updatetime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}