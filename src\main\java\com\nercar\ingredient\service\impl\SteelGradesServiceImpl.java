package com.nercar.ingredient.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.dto.SteelGradesDTO;
import com.nercar.ingredient.domain.po.ProcessPath;
import com.nercar.ingredient.domain.po.SteelGrades;
import com.nercar.ingredient.domain.vo.ProcessPathVO;
import com.nercar.ingredient.mapper.ProcessPathMapper;
import com.nercar.ingredient.mapper.SteelGradesMapper;
import com.nercar.ingredient.service.SteelGradesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【steel_grades(钢种表)】的数据库操作Service实现
* @createDate 2025-04-01 13:55:28
*/
@Service
public class SteelGradesServiceImpl extends ServiceImpl<SteelGradesMapper, SteelGrades>
    implements SteelGradesService{

    @Autowired
    private ProcessPathMapper processPathMapper;
    @Autowired
    private SteelGradesMapper steelGradesMapper;

    @Override
    public IPage<ProcessPathVO> selectPageProcessPath(SteelGradesDTO steelGradesDTO,SteelGrades steelGrades) {
        // 创建分页对象，指定当前页和每页显示的记录数
        if (Objects.isNull(steelGradesDTO.getPageNo()) || Objects.isNull(steelGradesDTO.getPageSize())) {
            steelGradesDTO.setPageNo(1);
            steelGradesDTO.setPageSize(10);
        }
        Page<ProcessPathVO> page = new Page<>(steelGradesDTO.getPageNo(), steelGradesDTO.getPageSize());
        if(Objects.isNull(steelGrades)){
            IPage<ProcessPathVO> processPathVOIPage = processPathMapper.selectProcessPath(page);
            //遍历所有的路径去根据路径中的钢种ID查找钢种名称并给路径中的钢种名称赋值
            for (ProcessPathVO processPathVO : processPathVOIPage.getRecords()) {
                SteelGrades steelGrades1 = steelGradesMapper.selectById(processPathVO.getSteelGradesId());
                processPathVO.setSteelGrade(steelGrades1.getSteelGrade());
            }
            return processPathVOIPage;
        }
        LambdaQueryWrapper<ProcessPath> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(steelGrades.getId()), ProcessPath::getSteelGradesId, steelGrades.getId());
        return processPathMapper.searchProcessPath(page, queryWrapper);
    }
}




