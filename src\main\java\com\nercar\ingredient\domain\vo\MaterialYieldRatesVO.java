package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MaterialYieldRatesVO extends BaseVO{
    /**
     * ID
     */
    @TableId
    @Schema(description = "主键")
    private Long id;

    /**
     * 生产车间
     */
    @Schema(description = "生产车间")
    private String productionDept;

    /**
     * 设备
     */
    @Schema(description = "设备")
    private String device;

    /**
     * 作业线
     */
    @Schema(description = "作业线")
    private String lineName;

    /**
     * 供料类别
     */
    @Schema(description = "供料类别")
    private String materialCategoryName;

    /**
     * 产品类别
     */
    @Schema(description = "产品类别")
    private String productCategory;

    /**
     * 钢类
     */
    @Schema(description = "钢类")
    private String steelClass;

    /**
     * 钢号
     */
    @Schema(description = "钢号")
    private String steelGrade;

    /**
     * 规格
     */
    @Schema(description = "规格")
    private String specifications;

    /**
     * 成材率
     */
    @Schema(description = "成材率")
    private String materialYield;

    /**
     * 不定尺（黑皮）
     */
    @Schema(description = "不定尺（黑皮）")
    private String unfixedLengthBlackSkin;

    /**
     * 不定尺成材率（磨光）
     */
    @Schema(description = "不定尺成材率（磨光）")
    private String unfixedYieldPolished;

    /**
     * 不定尺成材率（削皮）
     */
    @Schema(description = "不定尺成材率（削皮）")
    private String unfixedYieldPeeling;

    /**
     * 成材率（定尺+磨光）
     */
    @Schema(description = "成品率（定尺+磨光）")
    private String fixedYieldPolished;

    /**
     * 成材率（定尺+车光）
     */
    @Schema(description = "成品率（定尺+车光）")
    private String fixedYieldLathe;

    /**
     * 成材率（定尺+削皮）
     */
    @Schema(description = "成品率（定尺+削皮）")
    private String fixedYieldPeeling;

    /**
     * 不定尺磨光车光
     */
    @Schema(description = "不定尺磨光车光")
    private String unfixedPolishedLathe;

    /**
     * 不定尺磨光削皮
     */
    @Schema(description = "不定尺磨光削皮")
    private String unfixedPolishedPeeling;

    /**
     * 定尺磨光车光
     */
    @Schema(description = "定尺磨光车光")
    private String fixedPolishedLathe;

    /**
     * 定尺磨光削皮
     */
    @Schema(description = "定尺磨光削皮")
    private String fixedPolishedPeeling;

    /**
     * 定尺率
     */
    @Schema(description = "定尺率")
    private String fixedLengthRate;

    /**
     * 磨光率
     */
    @Schema(description = "磨光率")
    private String polishedRate;

    /**
     * 车光率
     */
    @Schema(description = "车光率")
    private String latheRate;

    /**
     * 削皮率
     */
    @Schema(description = "削皮率")
    private String peelingRate;

    /**
     * 烧损
     */
    @Schema(description = "烧损")
    private String burningLoss;

    /**
     * 定尺（黑皮）
     */
    @Schema(description = "定尺（黑皮）")
    private String fixedLengthBlackSkin;

    /**
     * 不定尺成材率（车光）
     */
    @Schema(description = "不定尺成材率（车光）")
    private String unfixedYieldLathe;

    /**
     * 成材率（定尺+铣光）
     */
    @Schema(description = "成材率（定尺+铣光）")
    private String fixedYieldMilling;

    /**
     * 定尺+铣光率
     */
    @Schema(description = "定尺+铣光率")
    private String fixedMillingRate;

    /**
     * 成材率（调质）
     */
    @Schema(description = "成材率（调质）")
    private String quenchingYield;

    /**
     * 调质率
     */
    @Schema(description = "调质率")
    private String quenchingRate;

    /**
     * 成材率（定尺+调质）
     */
    @Schema(description = "成材率（定尺+调质）")
    private String fixedQuenchingYield;

    /**
     * 定制单位
     */
    @Schema(description = "定制单位")
    private String specifiedUnit;

    /**
     * 修改内容及原因
     */
    @Schema(description = "修改内容及原因")
    private String modificationReason;

    /**
     * 锭型
     */
    @Schema(description = "锭型")
    private String ingotType;

}
