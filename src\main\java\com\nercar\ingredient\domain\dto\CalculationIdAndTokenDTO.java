package com.nercar.ingredient.domain.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CalculationIdAndTokenDTO extends BaseDTO{
    @Schema(description = "测算ID")
    String calculationId;
    @Schema(description = "token")
    String token;
    @Schema(description = "合同ID")
    String contractId;
}
