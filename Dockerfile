# 使用官方JDK 17基础镜像（根据需求可以选择不同变体）
FROM eclipse-temurin:17-jdk-jammy

LABEL authors="hnb"
# 设置工作目录（后续操作都在此目录进行）
WORKDIR /app

# 将构建的jar包复制到容器中（注意保持与实际路径一致）
COPY target/tech-doc-process-0.0.1-SNAPSHOT.jar ./app.jar

# 复制配置文件（根据Spring Boot配置优先级，文件可以放在以下位置：
# 1. /config目录（最高优先级） 2. 当前目录 3. classpath/config 4. classpath）
COPY application-test.yml ./config/

# 声明暴露端口（实际映射需要在运行时通过-p参数指定）
EXPOSE 9090

# 设置启动命令（激活test profile并指定配置文件路径）
ENTRYPOINT ["java", "-jar", "-Dspring.profiles.active=test", "app.jar"]