-- 标准成分查询验证脚本
-- 执行时间：2025-07-29
-- 目的：验证配料记录 1950042128071708673 对应的标准成分数据

-- 1. 查询指定配料记录的基本信息
SELECT 
    sir.id AS record_id,
    sir.steel_grade_id,
    sir.execution_standard_id,
    sir.process_path_id,
    sir.status,
    sg.steel_grade,
    es.standard_name,
    sir.createtime
FROM standard_ingredient_records sir
LEFT JOIN steel_grades sg ON sir.steel_grade_id = sg.id
LEFT JOIN execution_standard es ON sir.execution_standard_id = es.id
WHERE sir.id = 1950042128071708673;

-- 2. 查询该配料记录对应的执行标准ID的标准成分
-- 首先获取执行标准ID
WITH record_info AS (
    SELECT execution_standard_id 
    FROM standard_ingredient_records 
    WHERE id = 1950042128071708673
)
SELECT 
    sc.id,
    sc.element_name,
    sc.min_value,
    sc.max_value,
    sc.standard_id,
    sc.is_customize,
    sc.createuser,
    sc.createtime
FROM standard_compositions sc
JOIN record_info ri ON sc.standard_id = ri.execution_standard_id
ORDER BY sc.element_name;

-- 3. 查看 standard_compositions 表的所有数据
SELECT 
    id,
    element_name,
    min_value,
    max_value,
    standard_id,
    is_customize,
    createuser,
    createtime
FROM standard_compositions
ORDER BY standard_id, element_name;

-- 4. 查看 execution_standard 表的所有数据
SELECT 
    id,
    standard_name,
    createuser,
    createtime
FROM execution_standard
ORDER BY id;

-- 5. 检查是否存在标准ID为该配料记录执行标准ID的标准成分
SELECT 
    '配料记录执行标准ID' as info_type,
    execution_standard_id as value
FROM standard_ingredient_records 
WHERE id = 1950042128071708673
UNION ALL
SELECT 
    '该标准ID对应的标准成分数量',
    COUNT(*)::text
FROM standard_compositions sc
WHERE sc.standard_id = (
    SELECT execution_standard_id 
    FROM standard_ingredient_records 
    WHERE id = 1950042128071708673
);

-- 6. 查看所有执行标准及其对应的标准成分数量
SELECT 
    es.id,
    es.standard_name,
    COUNT(sc.id) as composition_count
FROM execution_standard es
LEFT JOIN standard_compositions sc ON es.id = sc.standard_id
GROUP BY es.id, es.standard_name
ORDER BY es.id;

-- 验证完成提示
SELECT '标准成分查询验证脚本执行完成' as status;
