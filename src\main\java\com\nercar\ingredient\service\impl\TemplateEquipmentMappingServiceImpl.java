package com.nercar.ingredient.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.po.TemplateEquipmentMapping;
import com.nercar.ingredient.mapper.TemplateEquipmentMappingMapper;
import com.nercar.ingredient.service.TemplateEquipmentMappingService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【template_equipment_mapping(模板与设备关联表)】的数据库操作Service实现
* @createDate 2025-04-01 13:55:28
*/
@Service
public class TemplateEquipmentMappingServiceImpl extends ServiceImpl<TemplateEquipmentMappingMapper, TemplateEquipmentMapping>
    implements TemplateEquipmentMappingService{

}




