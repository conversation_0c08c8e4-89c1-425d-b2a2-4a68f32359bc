package com.nercar.ingredient.excel;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@ColumnWidth(20) //列宽
@ContentRowHeight(20)//数据行高
@HeadRowHeight(30)//表头高
@Data
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class StandardRawMaterialsExcel implements Serializable {

    /**
     *
     */
    @ExcelProperty("ID")
    private String id;

    /**
     * 名称
     */
    @ExcelProperty("名称")
    private String name;


    /**
     * 基准品味%
     */
    @ExcelProperty("基准品味%")
    private BigDecimal composition;

    /**
     * 收得率%
     */
    @ExcelProperty("收得率%")
    private BigDecimal yieldRate;

    /**
     * 价格
     */
    @ExcelProperty("价格")
    private BigDecimal price;

//    /**
//     *
//     */
//    @ExcelProperty("创建用户")
//    private String createuser;
//
//    /**
//     *
//     */
//    @ExcelProperty("创建时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private LocalDateTime createtime;
//
//    /**
//     *
//     */
//    @ExcelProperty("更新时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private LocalDateTime updatetime;


    /**
     * 配入量
     */
    @ExcelProperty("配入量")
    private BigDecimal averageValue;
    /**
     * 重量
     */
    @ExcelProperty("重量")
    private BigDecimal wieght;

    @ExcelProperty("生产成本")
    private BigDecimal productionCost;
    @ExcelProperty("单耗kg")
    private String singleConsume;

    /**
     * 物料代码
     */
    @ExcelProperty("物料代码")
    private String materialCode;

    /**
     * 标准号
     */
    @ExcelProperty("标准号")
    private String standardNo;

}
