package com.nercar.ingredient.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nercar.ingredient.domain.dto.ContractReviewQueryDTO;

import com.nercar.ingredient.domain.vo.ContractReviewInfoVO;
import com.nercar.ingredient.domain.vo.ContractReviewOptionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 合同评审数据转换服务
 * 负责在我们的数据格式和合同评审系统数据格式之间进行转换
 */
@Slf4j
@Service
public class ContractReviewDataConverter {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    // ==================== 请求参数转换 ====================
    
    /**
     * 转换分页查询参数
     * @param queryDTO 我们的查询DTO
     * @return 合同评审系统的查询参数
     */
    public Map<String, Object> convertPageQueryParams(ContractReviewQueryDTO queryDTO) {
        Map<String, Object> params = new HashMap<>();
        
        // 分页参数转换：pageNo/pageSize → current/page
        params.put("current", queryDTO.getPageNo() != null ? queryDTO.getPageNo() : 1);
        params.put("page", queryDTO.getPageSize() != null ? queryDTO.getPageSize() : 10);
        
        // 查询条件转换
        params.put("code", queryDTO.getCodeExact() != null ? queryDTO.getCodeExact() : "");
        params.put("customerName", queryDTO.getCustomerNameExact() != null ? queryDTO.getCustomerNameExact() : "");
        params.put("steelGradeId", ""); // 合同评审系统使用ID，我们暂时传空
        params.put("steelTypeId", "");  // 合同评审系统使用ID，我们暂时传空
        
        // 时间范围转换
        if (queryDTO.getStartTime() != null) {
            params.put("startDate", queryDTO.getStartTime().format(DATE_TIME_FORMATTER));
        } else {
            params.put("startDate", "");
        }
        
        if (queryDTO.getEndTime() != null) {
            params.put("endDate", queryDTO.getEndTime().format(DATE_TIME_FORMATTER));
        } else {
            params.put("endDate", "");
        }
        
        log.debug("转换查询参数: {} → {}", queryDTO, params);
        return params;
    }
    
    // ==================== 响应数据转换 ====================
    
    /**
     * 转换分页响应数据
     * @param responseJson 合同评审系统的响应
     * @return 转换后的合同评审信息列表和总数
     */
    public PageResult<ContractReviewInfoVO> convertPageResponse(JsonNode responseJson) {
        try {
            if (!responseJson.get("success").asBoolean()) {
                throw new RuntimeException("合同评审系统返回失败: " + responseJson.get("message").asText());
            }
            
            int total = responseJson.get("total").asInt();
            JsonNode rowsNode = responseJson.get("rows");
            
            List<ContractReviewInfoVO> records = new ArrayList<>();
            if (rowsNode != null && rowsNode.isArray()) {
                for (JsonNode rowNode : rowsNode) {
                    ContractReviewInfoVO vo = convertToContractReviewInfoVO(rowNode);
                    records.add(vo);
                }
            }
            
            log.debug("转换分页响应: 总数={}, 记录数={}", total, records.size());
            return new PageResult<>(records, total);
            
        } catch (Exception e) {
            log.error("转换分页响应失败: {}", e.getMessage(), e);
            throw new RuntimeException("转换合同评审分页数据失败", e);
        }
    }
    
    /**
     * 转换单个合同评审信息
     * @param jsonNode 合同评审系统的数据节点
     * @return 合同评审信息VO
     */
    private ContractReviewInfoVO convertToContractReviewInfoVO(JsonNode jsonNode) {
        ContractReviewInfoVO vo = new ContractReviewInfoVO();
        
        // 基础信息
        vo.setId(jsonNode.get("id").asLong());
        vo.setCode(getStringValue(jsonNode, "code"));
        vo.setCustomerName(getStringValue(jsonNode, "customerName"));
        vo.setCustomerPhone(getStringValue(jsonNode, "customerPhone"));
        
        // 钢材信息
        vo.setSteelTypeName(getStringValue(jsonNode, "steelTypeName"));
        vo.setSteelGradeName(getStringValue(jsonNode, "steelGradeName"));
        vo.setSpecification(getStringValue(jsonNode, "specification"));
        vo.setSteelNumber(getIntegerValue(jsonNode, "steelNumber"));
        vo.setSteelNumberUnit(getStringValue(jsonNode, "steelNumberUnit"));
        
        // 标准和工艺信息
        vo.setStandardName(getStringValue(jsonNode, "standardName"));
        vo.setTechnicalStandardName(getStringValue(jsonNode, "technicalStandardName"));
        vo.setDeliveryStatus(getStringValue(jsonNode, "deliveryStatus"));
        vo.setProcessingPurpose(getStringValue(jsonNode, "processingPurpose"));
        
        // 冶炼工艺（已经是数组格式，直接转换为JSON字符串）
        JsonNode smeltingProcessNode = jsonNode.get("smeltingProcess");
        if (smeltingProcessNode != null && smeltingProcessNode.isArray()) {
            try {
                vo.setSmeltingProcess(objectMapper.writeValueAsString(smeltingProcessNode));
            } catch (Exception e) {
                log.warn("转换冶炼工艺失败: {}", e.getMessage());
                vo.setSmeltingProcess("[]");
            }
        }
        
        // 其他信息
        vo.setSpecialRequirements(getStringValue(jsonNode, "specialRequirements"));
        vo.setRemark(getStringValue(jsonNode, "remark"));
        vo.setAuthorName(getStringValue(jsonNode, "authorName"));
        vo.setSalesmanName(getStringValue(jsonNode, "salesmanName"));
        vo.setIsHead(getIntegerValue(jsonNode, "isHead"));
        vo.setIsCostCalculation(getBooleanValue(jsonNode, "isCostCalculation"));
        vo.setIsProduce(getBooleanValue(jsonNode, "isProduce"));
        vo.setIsOutsourcingFirm(getBooleanValue(jsonNode, "isOutsourcingFirm"));
        vo.setRecommendRoute(getIntegerValue(jsonNode, "recommendRoute"));
        
        // 时间信息
        vo.setSubmitTime(parseDateTime(getStringValue(jsonNode, "submitTime")));
        vo.setCreateTime(parseDateTime(getStringValue(jsonNode, "createTime")));
        
        return vo;
    }
    
    /**
     * 转换下拉框选项数据
     * @param responseJson 合同评审系统的响应
     * @param valueField 值字段名
     * @param labelField 标签字段名
     * @return 下拉框选项列表
     */
    public List<ContractReviewOptionVO> convertToOptions(JsonNode responseJson, String valueField, String labelField) {
        try {
            if (!responseJson.get("success").asBoolean()) {
                throw new RuntimeException("合同评审系统返回失败: " + responseJson.get("message").asText());
            }
            
            JsonNode dataNode = responseJson.get("data");
            List<ContractReviewOptionVO> options = new ArrayList<>();
            
            if (dataNode != null && dataNode.isArray()) {
                for (JsonNode itemNode : dataNode) {
                    ContractReviewOptionVO option = ContractReviewOptionVO.builder()
                            .value(getStringValue(itemNode, valueField))
                            .label(getStringValue(itemNode, labelField))
                            .count(0L) // 合同评审系统不提供count，设为0
                            .build();
                    options.add(option);
                }
            }
            
            log.debug("转换下拉框选项: 数量={}", options.size());
            return options;
            
        } catch (Exception e) {
            log.error("转换下拉框选项失败: {}", e.getMessage(), e);
            throw new RuntimeException("转换下拉框选项数据失败", e);
        }
    }
    
    /**
     * 转换合同详情数据
     * @param responseJson 合同评审系统的响应
     * @return 合同评审信息VO
     */
    public ContractReviewInfoVO convertContractDetail(JsonNode responseJson) {
        try {
            if (!responseJson.get("success").asBoolean()) {
                throw new RuntimeException("合同评审系统返回失败: " + responseJson.get("message").asText());
            }
            
            JsonNode dataNode = responseJson.get("data");
            return convertToContractReviewInfoVO(dataNode);
            
        } catch (Exception e) {
            log.error("转换合同详情失败: {}", e.getMessage(), e);
            throw new RuntimeException("转换合同详情数据失败", e);
        }
    }
    

    
    /**
     * 转换文件预览链接
     * @param responseJson 合同评审系统的响应
     * @return 预览链接
     */
    public String convertPreviewUrl(JsonNode responseJson) {
        try {
            if (!responseJson.get("success").asBoolean()) {
                throw new RuntimeException("合同评审系统返回失败: " + responseJson.get("message").asText());
            }
            
            return responseJson.get("data").asText();
            
        } catch (Exception e) {
            log.error("转换预览链接失败: {}", e.getMessage(), e);
            throw new RuntimeException("转换预览链接数据失败", e);
        }
    }
    
    // ==================== 工具方法 ====================
    
    private String getStringValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        return fieldNode != null && !fieldNode.isNull() ? fieldNode.asText() : null;
    }
    
    private Integer getIntegerValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        return fieldNode != null && !fieldNode.isNull() ? fieldNode.asInt() : null;
    }
    
    private Boolean getBooleanValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        return fieldNode != null && !fieldNode.isNull() ? fieldNode.asBoolean() : null;
    }
    
    private LocalDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }
        try {
            // 处理ISO格式的时间字符串
            if (dateTimeStr.contains("T")) {
                return LocalDateTime.parse(dateTimeStr.substring(0, 19));
            } else {
                return LocalDateTime.parse(dateTimeStr, DATE_TIME_FORMATTER);
            }
        } catch (Exception e) {
            log.warn("解析时间字符串失败: {}", dateTimeStr);
            return null;
        }
    }
    
    /**
     * 分页结果包装类
     */
    public static class PageResult<T> {
        private final List<T> records;
        private final int total;
        
        public PageResult(List<T> records, int total) {
            this.records = records;
            this.total = total;
        }
        
        public List<T> getRecords() {
            return records;
        }
        
        public int getTotal() {
            return total;
        }
    }
}
