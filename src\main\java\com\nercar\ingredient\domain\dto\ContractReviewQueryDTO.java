package com.nercar.ingredient.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 合同评审信息查询DTO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "合同评审信息查询DTO")
public class ContractReviewQueryDTO extends BaseDTO {
    
    /**
     * 合同ID（主键）
     */
    @Schema(description = "合同ID（主键）")
    private Long contractId;
    
    /**
     * 合同编号
     */
    @Schema(description = "合同编号，如：F0003-2025")
    private String code;
    
    /**
     * 客户名称
     */
    @Schema(description = "客户名称，支持模糊查询")
    private String customerName;
    
    /**
     * 钢材类型名称
     */
    @Schema(description = "钢材类型名称，如：汽车钢、工具钢、不锈钢")
    private String steelTypeName;
    
    /**
     * 钢材牌号名称
     */
    @Schema(description = "钢材牌号名称，如：N07718、20CrMnTi、304")
    private String steelGradeName;
    
    /**
     * 是否进行成本测算
     */
    @Schema(description = "是否进行成本测算，true-是，false-否")
    private Boolean isCostCalculation;

    // ==================== 新增查询参数 ====================

    /**
     * 顾客名称（精确匹配，用于下拉框选择）
     */
    @Schema(description = "顾客名称，精确匹配，用于下拉框选择")
    private String customerNameExact;

    /**
     * 钢种（精确匹配，用于下拉框选择）
     */
    @Schema(description = "钢种，精确匹配，用于下拉框选择")
    private String steelGradeNameExact;

    /**
     * 钢类（精确匹配，用于下拉框选择）
     */
    @Schema(description = "钢类，精确匹配，用于下拉框选择")
    private String steelTypeNameExact;

    /**
     * 合同编号（精确匹配）
     */
    @Schema(description = "合同编号，精确匹配")
    private String codeExact;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间，格式：yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间，格式：yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    // ==================== 分页参数 ====================

    /**
     * 分页页码
     */
    @Schema(description = "分页页码，从1开始")
    private Integer pageNo = 1;

    /**
     * 分页大小
     */
    @Schema(description = "分页大小，默认10")
    private Integer pageSize = 10;
}
