package com.nercar.ingredient.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

public class DateUtils {
    public static final String DATE_FORMAT = "yyyy-MM-dd";

    /**
     * Date转LocalDateTime
     */
    public static LocalDateTime ConvertDateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return Instant.ofEpochMilli(date.getTime())
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
    }

    /**
     * LocalDateTime转Date
     */
    public static Date ConvertLocalDateTimeToDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 计算两个LocalDateTime之间相差的时间毫秒数
     */
    public static long CalculateMillisecondsBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }

        Instant startInstant = start.atZone(ZoneId.systemDefault()).toInstant();
        Instant endInstant = end.atZone(ZoneId.systemDefault()).toInstant();
        return java.time.Duration.between(startInstant, endInstant).toMillis();
    }

    /**
     * 计算两个Date之间相差的时间毫秒数
     */
    public static long CalculateMillisecondsBetween(Date start, Date end) {

        if (start == null || end == null) {
            return 0;
        }
        Instant startInstant = start.toInstant();
        Instant endInstant = end.toInstant();
        return java.time.Duration.between(startInstant, endInstant).toMillis();
    }
}
