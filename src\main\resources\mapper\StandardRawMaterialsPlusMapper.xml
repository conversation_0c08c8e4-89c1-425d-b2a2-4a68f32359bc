<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.StandardRawMaterialsPlusMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.StandardRawMaterialsPlus">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="departmentId" column="department_id" jdbcType="BIGINT"/>
            <result property="productionEquipmentId" column="production_equipment_id" jdbcType="BIGINT"/>
            <result property="yieldRate" column="yield_rate" jdbcType="NUMERIC"/>
            <result property="price" column="price" jdbcType="NUMERIC"/>
            <result property="isCustom" column="is_custom" jdbcType="CHAR"/>
            <result property="category" column="category" jdbcType="VARCHAR"/>
            <result property="priority" column="priority" jdbcType="INTEGER"/>
            <result property="cContent" column="c_content" jdbcType="INTEGER"/>
            <result property="singleConsume" column="single_consume" jdbcType="VARCHAR"/>
            <result property="departmentName" column="department_name" jdbcType="VARCHAR"/>
            <result property="createuser" column="createuser" jdbcType="VARCHAR"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
            <result property="steelGrade" column="steel_grade" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="c" column="C" jdbcType="NUMERIC"/>
            <result property="mn" column="Mn" jdbcType="NUMERIC"/>
            <result property="si" column="Si" jdbcType="NUMERIC"/>
            <result property="p" column="P" jdbcType="NUMERIC"/>
            <result property="cr" column="Cr" jdbcType="NUMERIC"/>
            <result property="v" column="V" jdbcType="NUMERIC"/>
            <result property="mo" column="Mo" jdbcType="NUMERIC"/>
            <result property="ni" column="Ni" jdbcType="NUMERIC"/>
            <result property="w" column="W" jdbcType="NUMERIC"/>
            <result property="cu" column="Cu" jdbcType="NUMERIC"/>
            <result property="ti" column="Ti" jdbcType="NUMERIC"/>
            <result property="nb" column="Nb" jdbcType="NUMERIC"/>
            <result property="co" column="Co" jdbcType="NUMERIC"/>
            <result property="s" column="S" jdbcType="NUMERIC"/>
            <result property="sn" column="Sn" jdbcType="NUMERIC"/>
            <result property="al" column="Al" jdbcType="NUMERIC"/>
            <result property="fe" column="Fe" jdbcType="NUMERIC"/>
            <result property="b" column="B" jdbcType="NUMERIC"/>
            <result property="zr" column="Zr" jdbcType="NUMERIC"/>
            <result property="la" column="La" jdbcType="NUMERIC"/>
            <result property="ce" column="Ce" jdbcType="NUMERIC"/>
            <result property="ca" column="Ca" jdbcType="NUMERIC"/>
            <result property="pb" column="Pb" jdbcType="NUMERIC"/>
            <result property="bi" column="Bi" jdbcType="NUMERIC"/>
            <result property="sb" column="Sb" jdbcType="NUMERIC"/>
            <result property="as" column="As" jdbcType="NUMERIC"/>
            <result property="als" column="Als" jdbcType="NUMERIC"/>
            <result property="ta" column="Ta" jdbcType="NUMERIC"/>
            <result property="mg" column="Mg" jdbcType="NUMERIC"/>
            <result property="ag" column="Ag" jdbcType="NUMERIC"/>
            <result property="hg" column="Hg" jdbcType="NUMERIC"/>
            <result property="cd" column="Cd" jdbcType="NUMERIC"/>
            <result property="zn" column="Zn" jdbcType="NUMERIC"/>
            <result property="te" column="Te" jdbcType="NUMERIC"/>
            <result property="se" column="Se" jdbcType="NUMERIC"/>
            <result property="pr" column="Pr" jdbcType="NUMERIC"/>
            <result property="nd" column="Nd" jdbcType="NUMERIC"/>
            <result property="sc" column="Sc" jdbcType="NUMERIC"/>
            <result property="y" column="Y" jdbcType="NUMERIC"/>
            <result property="hf" column="Hf" jdbcType="NUMERIC"/>
            <result property="pcm" column="Pcm" jdbcType="NUMERIC"/>
            <result property="h" column="H" jdbcType="NUMERIC"/>
            <result property="o" column="O" jdbcType="NUMERIC"/>
            <result property="n" column="N" jdbcType="NUMERIC"/>
            <result property="materialCode" column="material_code" jdbcType="VARCHAR"/>
            <result property="standardNo" column="standard_no" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,department_id,
        production_equipment_id,yield_rate,price,
        is_custom,category,priority,
        c_content,single_consume,department_name,
        createuser,createtime,updatetime,
        steel_grade,type,"C",
        "Mn","Si","P",
        "Cr","V","Mo",
        "Ni","W","Cu",
        "Ti","Nb","Co",
        "S","Sn","Al",
        "Fe","B","Zr",
        "La","Ce","Ca",
        "Pb","Bi","Sb",
        "As","Als","Ta",
        "Mg","Ag","Hg",
        "Cd","Zn","Te",
        "Se","Pr","Nd",
        "Sc","Y","Hf",
        "Pcm","H","O",
        "N",material_code,standard_no
    </sql>
    <update id="updateByMaterialId" parameterType="com.nercar.ingredient.domain.po.StandardRawMaterialsPlus">
        update standard_raw_materials_plus
        <set>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="departmentId != null">department_id = #{departmentId},</if>
            <if test="productionEquipmentId != null">production_equipment_id = #{productionEquipmentId},</if>
            <if test="yieldRate != null">yield_rate = #{yieldRate},</if>
            <if test="price != null">price = #{price},</if>
            <if test="isCustom != null and isCustom != ''">is_custom = #{isCustom},</if>
            <if test="category != null and category != ''">category = #{category},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="cContent != null">c_content = #{cContent},</if>
            <if test="singleConsume != null and singleConsume != ''">single_consume = #{singleConsume},</if>
            <if test="departmentName != null and departmentName != ''">department_name = #{departmentName},</if>
            <if test="steelGrade != null and steelGrade != ''">steel_grade = #{steelGrade},</if>
            <if test="materialCode != null and materialCode != ''">material_code = #{materialCode},</if>
            <if test="standardNo != null and standardNo != ''">standard_no = #{standardNo},</if>
            "C" = #{C},
            "Mn" = #{Mn},
            "Si" = #{Si},
            "P" = #{P},
            "Cr" = #{Cr},
            "V" = #{V},
            "Mo" = #{Mo},
            "Ni" = #{Ni},
            "W" = #{W},
            "Cu" = #{Cu},
            "Ti" = #{Ti},
            "Nb" = #{Nb},
            "Co" = #{Co},
            "S" = #{S},
            "Sn" = #{Sn},
            "Al" = #{Al},
            "Fe" = #{Fe},
            "B" = #{B},
            "Zr" = #{Zr},
            "La" = #{La},
            "Ce" = #{Ce},
            "Ca" = #{Ca},
            "Pb" = #{Pb},
            "Bi" = #{Bi},
            "Sb" = #{Sb},
            "As" = #{As},
            "Als" = #{Als},
            "Ta" = #{Ta},
            "Mg" = #{Mg},
            "Ag" = #{Ag},
            "Hg" = #{Hg},
            "Cd" = #{Cd},
            "Zn" = #{Zn},
            "Te" = #{Te},
            "Se" = #{Se},
            "Pr" = #{Pr},
            "Nd" = #{Nd},
            "Sc" = #{Sc},
            "Y" = #{Y},
            "Hf" = #{Hf},
            "Pcm" = #{Pcm},
            "H" = #{H},
            "O" = #{O},
            "N" = #{N}
        </set>
        where material_id = #{materialId} and type = #{type}
    </update>
    <delete id="deleteByMaterialId">
    delete from standard_raw_materials_plus where material_id = #{materialId}
  </delete>
    <select id="selectMaterials" resultType="com.nercar.ingredient.domain.po.StandardRawMaterialsPlus">
        SELECT *
        FROM (
            SELECT 
                *,
                ROW_NUMBER() OVER (PARTITION BY material_id ORDER BY id) as rn
            FROM standard_raw_materials_plus
            ${ew.customSqlSegment}
        ) t
        WHERE rn = 1
    </select>
</mapper>
