package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 目标成分
 * @TableName purpose_compositions
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PurposeCompositionsVO extends BaseVO {
    /**
     * 主键
     */
    @TableId
    @Schema(description = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 标准配料ID
     */
    @Schema(description = "标准配料ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long standardIngredientRecordId;

    /**
     * 元素名称
     */
    @Schema(description = "元素名称")
    private String elementName;

    /**
     * 最小值
     */
    @Schema(description = "最小值")
    private BigDecimal minValue;

    /**
     * 最大值
     */
    @Schema(description = "最大值")
    private BigDecimal maxValue;

    /**
     * 平均值（目标值）
     */
    @Schema(description = "平均值（目标值）")
    private BigDecimal averageValue;

    /**
     * 数学符号
     */
    @Schema(description = "数学符号")
    private String code;

    /**
     * 成分类型
     */
    @Schema(description = "成分类型：内控成分、配料内控")
    private String type;

    /**
     * 排序序号
     */
    @Schema(description = "排序序号")
    private Integer sortOrder;

    /**
     * 创建人
     */
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createuser;

    /**
     * 创建时间
     */
    @TableField(value = "createtime",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatetime;
}