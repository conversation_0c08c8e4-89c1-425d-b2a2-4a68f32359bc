package com.nercar.ingredient.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nercar.ingredient.domain.po.StandardRawMaterials;
import com.nercar.ingredient.domain.vo.StandardRawMaterialsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【standard_rwa_materials(标准原料)】的数据库操作Mapper
* @createDate 2025-04-01 13:55:28
* @Entity com.nercar.ingredient.domain.po.StandardRwaMaterials
*/
@Mapper
public interface StandardRawMaterialsMapper extends BaseMapper<StandardRawMaterials> {


    IPage<StandardRawMaterialsVO> selectMaterials(Page<StandardRawMaterialsVO> page, @Param(Constants.WRAPPER) LambdaQueryWrapper<StandardRawMaterials> wrapper);
}




