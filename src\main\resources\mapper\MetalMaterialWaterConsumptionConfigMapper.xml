<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.MetalMaterialWaterConsumptionConfigMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.MetalMaterialWaterConsumptionConfig">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="metalMaterialWaterConsumption" column="metal_material_water_consumption" jdbcType="INTEGER"/>
        <result property="method" column="method" jdbcType="VARCHAR"/>
        <result property="device" column="device" jdbcType="VARCHAR"/>
        <result property="createuser" column="createuser" jdbcType="VARCHAR"/>
        <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
        <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="VOResultMap" type="com.nercar.ingredient.domain.vo.MetalMaterialWaterConsumptionConfigVO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="metalMaterialWaterConsumption" column="metal_material_water_consumption" jdbcType="INTEGER"/>
        <result property="method" column="method" jdbcType="VARCHAR"/>
        <result property="device" column="device" jdbcType="VARCHAR"/>
        <result property="createuser" column="createuser" jdbcType="VARCHAR"/>
        <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
        <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, metal_material_water_consumption, method, device, createuser, createtime, updatetime
    </sql>

    <!-- 分页查询金属料吨水单耗配置列表 -->
    <select id="selectConfigPage" resultMap="VOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM metal_material_water_consumption_config
        <where>
            <if test="method != null and method != ''">
                AND method LIKE CONCAT('%', #{method}, '%')
            </if>
            <if test="device != null and device != ''">
                AND device LIKE CONCAT('%', #{device}, '%')
            </if>
        </where>
        ORDER BY createtime DESC
    </select>

</mapper>
