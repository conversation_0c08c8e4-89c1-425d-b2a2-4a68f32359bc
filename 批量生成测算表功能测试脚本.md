# 批量生成测算表功能测试脚本

## 📋 **功能概述**
改造生成测算表接口，支持单个文件下载和批量ZIP压缩包下载两种模式。

## 🎯 **接口信息**

### **新接口**
- **路径**：`POST /ingredientCalculation/excel/download`
- **功能**：智能下载测算表（单个/批量）
- **参数**：`DownloadRequestDTO`

### **原接口（保持兼容）**
- **路径**：`POST /ingredientCalculation/excel/download/{id}`
- **功能**：单个文件下载
- **参数**：路径参数 `id`

## 🧪 **测试用例**

### **测试用例1：单个文件下载**
```json
POST /ingredientCalculation/excel/download
Content-Type: application/json

{
    "ids": [1915305809626275842],
    "fileNamePrefix": "单个测算表"
}
```

**预期结果**：
- 直接下载Excel文件
- 文件名：`产品成本测算流程表.xlsx`
- Content-Type: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`

### **测试用例2：批量文件下载（2个文件）**
```json
POST /ingredientCalculation/excel/download
Content-Type: application/json

{
    "ids": [1915305809626275842, 1915309030243573761],
    "fileNamePrefix": "批量测算表"
}
```

**预期结果**：
- 下载ZIP压缩包
- 文件名：`测算表批量下载_20250114_143000.zip`（时间戳动态生成）
- Content-Type: `application/zip`
- ZIP内容：
  - `测算表_01_ID_1915305809626275842.xlsx`
  - `测算表_02_ID_1915309030243573761.xlsx`
  - `批量处理摘要.txt`

### **测试用例3：批量文件下载（包含错误）**
```json
POST /ingredientCalculation/excel/download
Content-Type: application/json

{
    "ids": [1915305809626275842, 999999999999999999, 1915309030243573761],
    "fileNamePrefix": "测试错误处理"
}
```

**预期结果**：
- 下载ZIP压缩包
- ZIP内容：
  - `测算表_01_ID_1915305809626275842.xlsx`（成功）
  - `错误报告_02_ID_999999999999999999.txt`（失败）
  - `测算表_03_ID_1915309030243573761.xlsx`（成功）
  - `批量处理摘要.txt`（包含成功率统计）

### **测试用例4：参数验证**
```json
POST /ingredientCalculation/excel/download
Content-Type: application/json

{
    "ids": [],
    "fileNamePrefix": "空列表测试"
}
```

**预期结果**：
- HTTP 400 错误
- 错误信息：`ID列表不能为空`

### **测试用例5：批量限制测试**
```json
POST /ingredientCalculation/excel/download
Content-Type: application/json

{
    "ids": [1, 2, 3, ..., 51],  // 51个ID
    "fileNamePrefix": "超限测试"
}
```

**预期结果**：
- HTTP 400 错误
- 错误信息：`批量下载数量不能超过50个`

### **测试用例6：向后兼容性测试**
```http
POST /ingredientCalculation/excel/download/1915305809626275842
```

**预期结果**：
- 正常下载Excel文件
- 行为与原接口完全一致

## 📊 **数据验证**

### **验证点1：ZIP文件结构**
```
测算表批量下载_20250114_143000.zip
├── 测算表_01_ID_1915305809626275842.xlsx
├── 测算表_02_ID_1915309030243573761.xlsx
├── 错误报告_03_ID_999999999999999999.txt
└── 批量处理摘要.txt
```

### **验证点2：摘要文件内容**
```
批量生成测算表摘要
生成时间: 2025-01-14 14:30:00
总数量: 3
成功数量: 2
失败数量: 1
成功率: 66.67%

处理的ID列表:
[1915305809626275842, 1915309030243573761, 999999999999999999]
```

### **验证点3：错误报告内容**
```
文件生成失败
ID: 999999999999999999
时间: 2025-01-14 14:30:00
错误信息: 未找到对应的配料记录
```

## 🔧 **性能测试**

### **测试场景1：小批量（5个文件）**
- 预期响应时间：< 10秒
- 内存使用：< 100MB

### **测试场景2：中批量（20个文件）**
- 预期响应时间：< 30秒
- 内存使用：< 300MB

### **测试场景3：大批量（50个文件）**
- 预期响应时间：< 60秒
- 内存使用：< 500MB

## 🚨 **错误处理测试**

### **错误场景1：无效ID**
- 输入：不存在的ID
- 处理：生成错误报告文件，继续处理其他文件

### **错误场景2：数据库连接失败**
- 输入：正常ID，但数据库不可用
- 处理：抛出运行时异常，返回HTTP 500

### **错误场景3：磁盘空间不足**
- 输入：大量文件，磁盘空间不足
- 处理：抛出IO异常，返回HTTP 500

## 📝 **日志验证**

### **成功日志示例**
```
2025-01-14 14:30:00 INFO  - 接收到下载测算表请求，ID数量: 3
2025-01-14 14:30:00 INFO  - 批量文件下载，ID列表: [1915305809626275842, 1915309030243573761, 999999999999999999]
2025-01-14 14:30:00 INFO  - 开始批量生成测算表，数量: 3
2025-01-14 14:30:05 ERROR - 生成测算表失败, ID: 999999999999999999, 错误: 未找到对应的配料记录
2025-01-14 14:30:10 INFO  - 批量生成测算表完成，总数: 3, 成功: 2, 失败: 1
```

## 🎯 **验收标准**

### **功能验收**
- ✅ 单个文件下载正常
- ✅ 批量文件下载生成ZIP
- ✅ 错误处理机制完善
- ✅ 向后兼容性保持

### **性能验收**
- ✅ 50个文件批量下载在60秒内完成
- ✅ 内存使用控制在合理范围
- ✅ 并发请求不影响系统稳定性

### **用户体验验收**
- ✅ 文件命名清晰易懂
- ✅ 错误信息详细准确
- ✅ 处理进度日志完整
- ✅ ZIP文件结构合理

## 🔄 **回归测试**

### **原有功能验证**
- ✅ 单个测算表生成功能正常
- ✅ 计算结果数据完整性
- ✅ Excel模板填充正确
- ✅ 其他相关接口不受影响

测试完成后，确保所有验收标准通过，功能可以正式发布使用。
