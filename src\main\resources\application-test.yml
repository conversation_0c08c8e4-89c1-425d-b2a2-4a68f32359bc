spring:
  application:
    name: fushun-ingredient
  datasource:
    url: *******************************************
    username: postgres
    password: root
    driver-class-name: org.postgresql.Driver
  devtools:
    restart:
      enabled: false
#  data:
#    # redis配置
#    redis:
#      # Redis数据库索引（默认为0）
#      database: 1
#      # Redis服务器地址
#      host: 127.0.0.1
#      # Redis服务器连接端口
#      port: 6379
#      # Redis服务器连接密码（默认为空）
#      password:
#      # 连接超时时间
#      timeout: 10s
#      lettuce:
#        pool:
#          # 连接池最大连接数
#          max-active: 200
#          # 连接池最大阻塞等待时间（使用负值表示没有限制）
#          max-wait: -1ms
#          # 连接池中的最大空闲连接
#          max-idle: 10
#          # 连接池中的最小空闲连接
#          min-idle: 0

logging:
  level:
    com.baomidou.mybatis plus: DEBUG
    org.apache.ibatis: DEBUG

file:
#  path: /home/<USER>/java/tech-doc-mng/uploadPath
  template-docx-path: /home/<USER>/java/tech-doc-mng/template
  preview-url: http://*************:8012

# Minio配置
minio:
  url: http://127.0.0.1:9000
  bucketName: documents
  access-key: hVGtqkUc6qkwQ0N9d7zW
  secret-key: Qnijj532VrNrElesVuiFjjpVzd2YW2D8OCr9rgC4