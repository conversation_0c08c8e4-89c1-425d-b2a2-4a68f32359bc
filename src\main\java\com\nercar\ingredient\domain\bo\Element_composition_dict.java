package com.nercar.ingredient.domain.bo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Element_composition_dict extends BaseModel{

    /**
     * 元素名称
     */
    private String element;

    /**
     * 平均值（目标值）
     */
    private float composition;
}
