package com.nercar.ingredient.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StandardIngredientRecordsResultVO {
    private List<PurposeCompositionsVO> purposeCompositionsVOList;

    private List<StandardRawMaterialsVO> standardRawMaterialsVOList;
}
