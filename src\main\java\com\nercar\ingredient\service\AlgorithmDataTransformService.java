package com.nercar.ingredient.service;

import com.nercar.ingredient.domain.dto.CalculationQueryDataDTO;
import com.nercar.ingredient.domain.dto.NewAlgorithmRequest;
import com.nercar.ingredient.domain.po.CalculationResult;
import com.nercar.ingredient.domain.vo.NewAlgorithmResponse;

import java.util.List;

/**
 * 算法数据转换服务接口
 */
public interface AlgorithmDataTransformService {
    
    /**
     * 将系统数据转换为新算法接口请求格式
     * @param dto 计算查询数据DTO
     * @return 新算法请求对象
     */
    NewAlgorithmRequest transformToNewAlgorithmRequest(CalculationQueryDataDTO dto);
    
    /**
     * 处理新算法接口响应，更新计算结果
     * @param response 新算法响应
     * @param calculationResults 计算结果列表
     */
    void processNewAlgorithmResponse(NewAlgorithmResponse response, List<CalculationResult> calculationResults);
}
