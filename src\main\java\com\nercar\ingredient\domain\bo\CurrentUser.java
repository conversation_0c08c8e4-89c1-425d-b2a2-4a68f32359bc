package com.nercar.ingredient.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

@RequiredArgsConstructor
@Data
@AllArgsConstructor
@Accessors(chain = true)
public class CurrentUser {
    private String userId;
    private String username;
    private String token;
    private String department;
    private String costEstimattionId;
}
