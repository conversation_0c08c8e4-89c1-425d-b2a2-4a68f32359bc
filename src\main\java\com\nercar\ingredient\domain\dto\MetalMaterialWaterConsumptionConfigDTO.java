package com.nercar.ingredient.domain.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 金属料吨水单耗配置DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "金属料吨水单耗配置请求参数")
public class MetalMaterialWaterConsumptionConfigDTO {
    
    /**
     * 主键
     */
    @TableId
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 金属料吨水单耗
     */
    @Schema(description = "金属料吨水单耗", example = "100")
    private Integer metalMaterialWaterConsumption;

    /**
     * 冶炼方法
     */
    @Schema(description = "冶炼方法", example = "电炉冶炼")
    private String method;

    /**
     * 冶炼设备
     */
    @Schema(description = "冶炼设备", example = "电炉1号")
    private String device;

    /**
     * 分页页码
     */
    @Schema(description = "页码", example = "1")
    private Integer pageNo = 1;

    /**
     * 分页大小
     */
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;

    /**
     * 创建人
     */
    @TableField(value = "createuser", fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createuser;

    /**
     * 创建时间
     */
    @TableField(value = "createtime", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;
}
