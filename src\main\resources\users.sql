/*
 Navicat Premium Data Transfer

 Source Server         : ************_5432
 Source Server Type    : PostgreSQL
 Source Server Version : 90224
 Source Host           : ************:5432
 Source Catalog        : ingredient2
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 90224
 File Encoding         : 65001

 Date: 25/04/2025 15:56:34
*/


-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS "public"."users";
CREATE TABLE "public"."users" (
  "id" int8 NOT NULL,
  "account" varchar(32) COLLATE "pg_catalog"."default",
  "user_name" varchar(32) COLLATE "pg_catalog"."default",
  "department_id" int8,
  "createuser" varchar(32) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6),
  "pass_word" varchar(50) COLLATE "pg_catalog"."default",
  "nick_name" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."users"."id" IS '主键';
COMMENT ON COLUMN "public"."users"."account" IS '工号';
COMMENT ON COLUMN "public"."users"."user_name" IS '用户名';
COMMENT ON COLUMN "public"."users"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."users"."pass_word" IS '密码';
COMMENT ON COLUMN "public"."users"."nick_name" IS '用户名';
COMMENT ON TABLE "public"."users" IS '用户表';

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO "public"."users" VALUES (20, NULL, '********', NULL, NULL, NULL, '2025-04-24 16:39:23.193854', 'c4ca4238a0b923820dcc509a6f75849b', '陈德利');
INSERT INTO "public"."users" VALUES (112, NULL, '********', NULL, NULL, NULL, '2025-04-25 11:51:23.120859', 'c4ca4238a0b923820dcc509a6f75849b', '刘振天');
INSERT INTO "public"."users" VALUES (13, NULL, '********', NULL, NULL, NULL, '2025-04-25 13:32:00.818475', 'c4ca4238a0b923820dcc509a6f75849b', '杨立军');
INSERT INTO "public"."users" VALUES (21, NULL, '********', NULL, NULL, '2025-04-25 14:14:20.955494', '2025-04-25 14:14:20.955533', 'c4ca4238a0b923820dcc509a6f75849b', '王树财');
INSERT INTO "public"."users" VALUES (3, NULL, 'A配料室', 12, NULL, NULL, '2025-04-25 14:21:36.749024', 'c4ca4238a0b923820dcc509a6f75849b', 'A配料室');
INSERT INTO "public"."users" VALUES (118, NULL, '20063355', NULL, NULL, NULL, '2025-04-25 14:46:33.61473', 'c4ca4238a0b923820dcc509a6f75849b', '唐亮');
INSERT INTO "public"."users" VALUES (1, NULL, '张三', 9, NULL, NULL, '2025-04-24 16:12:38.006219', 'c4ca4238a0b923820dcc509a6f75849b', '张三');
INSERT INTO "public"."users" VALUES (9, NULL, '2000', NULL, NULL, '2025-04-24 16:14:19.501248', '2025-04-24 16:14:19.501248', 'c4ca4238a0b923820dcc509a6f75849b', '王树财');
INSERT INTO "public"."users" VALUES (11, NULL, '20049565', 10, NULL, NULL, '2025-04-24 15:34:38.126727', 'c4ca4238a0b923820dcc509a6f75849b', '20049565');
INSERT INTO "public"."users" VALUES (29, NULL, '20063409', 12, NULL, NULL, '2025-04-22 16:22:26.667517', 'c4ca4238a0b923820dcc509a6f75849b', '20063409');
INSERT INTO "public"."users" VALUES (36, NULL, '20077979', 13, NULL, NULL, '2025-04-23 17:07:11.807212', 'c4ca4238a0b923820dcc509a6f75849b', '20077979');
INSERT INTO "public"."users" VALUES (********, NULL, '********', 12, NULL, NULL, NULL, 'c4ca4238a0b923820dcc509a6f75849b', '********');
INSERT INTO "public"."users" VALUES (12, NULL, '20049623', 10, NULL, NULL, '2025-04-24 15:53:54.635649', 'c4ca4238a0b923820dcc509a6f75849b', '20049623');
INSERT INTO "public"."users" VALUES (99999, '1', 'test', 1, NULL, NULL, NULL, '098f6bcd4621d373cade4e832627b4f6', '测试账号');

-- ----------------------------
-- Primary Key structure for table users
-- ----------------------------
ALTER TABLE "public"."users" ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");
