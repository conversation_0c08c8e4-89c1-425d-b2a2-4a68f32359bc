14:29:57.154 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 72548 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:29:57.158 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:29:57.159 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:30:01.401 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:30:02.288 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:30:02.290 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:30:02.290 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:30:02.408 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:30:02.733 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:30:02.777 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@514bbbfa, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:30:03.050 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:30:03.094 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:30:03.121 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:30:03.146 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:30:03.167 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:30:03.186 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:30:03.206 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:30:03.217 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:30:03.218 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:30:03.225 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:30:03.253 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:30:03.276 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:30:03.300 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:30:03.326 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:30:03.347 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:30:03.369 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:30:03.422 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:30:03.445 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:30:03.467 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:30:03.505 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:30:03.517 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:30:03.517 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:30:03.527 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:30:03.539 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:30:03.540 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:30:03.547 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:30:03.569 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:30:03.589 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:30:03.718 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:30:03.727 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:30:03.734 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:13
14:30:05.404 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:30:05.688 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 8.976 seconds (process running for 11.195)
14:30:05.698 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:30:09.627 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:30:10.381 [http-nio-9090-exec-7] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
14:30:10.829 [http-nio-9090-exec-4] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 422 ms
14:30:24.636 [http-nio-9090-exec-8] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
14:30:56.262 [http-nio-9090-exec-5] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
14:30:56.607 [http-nio-9090-exec-5] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4bf2a08a
14:30:56.609 [http-nio-9090-exec-5] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
14:30:56.635 [http-nio-9090-exec-5] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,createuser,createtime,updatetime FROM standard_raw_materials
14:30:56.648 [http-nio-9090-exec-5] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 
14:30:56.677 [http-nio-9090-exec-5] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 16
14:31:14.390 [http-nio-9090-exec-4] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,createuser,createtime,updatetime FROM standard_raw_materials WHERE (name = ?)
14:31:14.391 [http-nio-9090-exec-4] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 铁矿石 A(String)
14:31:14.394 [http-nio-9090-exec-4] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 2
14:33:34.281 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
14:33:34.286 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
14:33:39.094 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 512 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:33:39.097 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:33:39.098 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:33:40.139 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:33:40.794 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:33:40.801 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:33:40.801 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:33:40.858 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:33:41.050 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:33:41.090 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@1395bf40, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:33:41.200 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:33:41.231 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:33:41.247 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:33:41.264 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:33:41.281 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:33:41.300 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:33:41.315 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:33:41.320 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:33:41.320 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:33:41.331 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:33:41.351 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:33:41.368 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:33:41.385 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:33:41.402 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:33:41.416 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:33:41.430 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:33:41.473 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:33:41.491 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:33:41.505 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:33:41.522 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:33:41.526 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:33:41.526 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:33:41.534 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:33:41.538 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:33:41.539 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:33:41.547 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:33:41.561 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:33:41.574 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:33:41.702 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:33:41.711 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:33:41.717 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:5
14:33:42.854 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:33:42.885 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.25 seconds (process running for 5.256)
14:33:42.893 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:33:46.933 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:33:47.344 [http-nio-9090-exec-6] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
14:33:47.819 [http-nio-9090-exec-8] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 459 ms
14:33:51.146 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
14:33:54.542 [http-nio-9090-exec-7] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
14:33:54.968 [http-nio-9090-exec-3] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
14:33:56.001 [http-nio-9090-exec-2] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
14:33:56.571 [http-nio-9090-exec-6] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
14:33:56.914 [http-nio-9090-exec-8] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
14:33:58.028 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
14:33:59.018 [http-nio-9090-exec-10] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
14:33:59.155 [http-nio-9090-exec-10] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1c3619d4
14:33:59.159 [http-nio-9090-exec-10] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
14:33:59.165 [http-nio-9090-exec-10] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,createuser,createtime,updatetime FROM standard_raw_materials
14:33:59.179 [http-nio-9090-exec-10] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 
14:33:59.203 [http-nio-9090-exec-10] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 16
14:34:17.028 [http-nio-9090-exec-9] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,createuser,createtime,updatetime FROM standard_raw_materials WHERE (category = ?)
14:34:17.029 [http-nio-9090-exec-9] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 矿石类(String)
14:34:17.032 [http-nio-9090-exec-9] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 6
14:34:48.907 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
14:34:48.911 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
14:49:50.141 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 61164 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:49:50.143 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:49:50.144 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:49:51.148 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:49:51.661 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:49:51.663 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:49:51.663 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:49:51.709 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:49:51.879 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:49:51.921 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@46adaa57, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:49:52.038 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:49:52.076 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:49:52.106 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:49:52.131 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:49:52.159 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:49:52.180 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:49:52.195 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:49:52.200 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:49:52.200 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:49:52.208 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:49:52.225 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:49:52.239 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:49:52.253 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:49:52.266 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:49:52.281 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:49:52.292 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:49:52.339 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:49:52.354 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:49:52.367 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:49:52.381 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:49:52.385 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:49:52.385 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:49:52.395 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:49:52.401 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:49:52.401 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:49:52.407 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:49:52.419 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:49:52.429 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:49:52.559 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:49:52.571 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:49:52.577 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:3
14:49:53.755 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:49:53.783 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.999 seconds (process running for 4.889)
14:49:53.792 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:49:58.741 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:49:59.072 [http-nio-9090-exec-6] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
14:49:59.505 [http-nio-9090-exec-5] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 407 ms
14:50:01.760 [http-nio-9090-exec-7] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
14:50:06.090 [http-nio-9090-exec-9] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
14:50:06.246 [http-nio-9090-exec-9] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@404bdb01
14:50:06.248 [http-nio-9090-exec-9] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
14:50:06.255 [http-nio-9090-exec-9] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments
14:50:06.268 [http-nio-9090-exec-9] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 
14:50:06.293 [http-nio-9090-exec-9] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 2
14:50:28.147 [Thread-5] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
14:50:28.300 [Thread-5] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
14:50:28.303 [Thread-5] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
14:50:28.460 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 61164 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:50:28.461 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:50:28.461 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:50:29.150 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:50:29.151 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:50:29.151 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:50:29.193 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:50:29.245 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:50:29.264 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@524f4511, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:50:29.283 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:50:29.309 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:50:29.328 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:50:29.342 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:50:29.358 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:50:29.375 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:50:29.391 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:50:29.393 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:50:29.394 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:50:29.401 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:50:29.418 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:50:29.433 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:50:29.447 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:50:29.461 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:50:29.472 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:50:29.483 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:50:29.498 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:50:29.510 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:50:29.518 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:50:29.530 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:50:29.533 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:50:29.533 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:50:29.540 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:50:29.543 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:50:29.544 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:50:29.547 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:50:29.558 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:50:29.568 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:50:29.731 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:50:29.743 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:50:29.750 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:3
14:50:30.283 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:50:30.291 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 1.878 seconds (process running for 41.397)
14:50:30.293 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:50:31.694 [Thread-7] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
14:50:31.897 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 61164 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:50:31.897 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:50:31.897 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:50:32.227 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:50:32.227 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:50:32.227 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:50:32.241 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:50:32.284 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:50:32.300 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@76273f3e, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:50:32.310 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:50:32.318 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:50:32.326 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:50:32.334 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:50:32.341 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:50:32.351 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:50:32.358 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:50:32.361 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:50:32.361 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:50:32.365 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:50:32.373 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:50:32.380 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:50:32.387 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:50:32.393 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:50:32.400 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:50:32.409 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:50:32.420 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:50:32.428 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:50:32.437 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:50:32.445 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:50:32.447 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:50:32.448 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:50:32.452 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:50:32.455 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:50:32.456 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:50:32.460 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:50:32.468 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:50:32.476 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:50:32.605 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:50:32.613 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:50:32.618 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:3
14:50:33.049 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
14:50:33.056 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 1.189 seconds (process running for 44.162)
14:50:33.058 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
14:51:13.658 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:51:13.670 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-2 - Starting...
14:51:13.734 [http-nio-9090-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@77ac23eb
14:51:13.734 [http-nio-9090-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-2 - Start completed.
14:51:13.734 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (department_name LIKE ?)
14:51:13.734 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: %部门%(String)
14:51:13.737 [http-nio-9090-exec-1] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
14:51:24.314 [http-nio-9090-exec-2] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments
14:51:24.314 [http-nio-9090-exec-2] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 
14:51:24.315 [http-nio-9090-exec-2] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 3
14:53:52.351 [Thread-11] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
14:53:52.450 [Thread-11] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-2 - Shutdown initiated...
14:53:52.453 [Thread-11] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-2 - Shutdown completed.
14:53:52.562 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 61164 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:53:52.563 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:53:52.563 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:53:52.922 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
14:53:52.922 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:53:52.922 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:53:52.935 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:53:52.995 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:53:53.017 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@665dae8a, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:53:53.027 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:53:53.038 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:53:53.048 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:53:53.060 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:53:53.079 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:53:53.088 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:53:53.100 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:53:53.103 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:53:53.103 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:53:53.107 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:53:53.119 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:53:53.131 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:53:53.140 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:53:53.148 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:53:53.176 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:53:53.228 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:53:53.264 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:53:53.278 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:53:53.291 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:53:53.299 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:53:53.302 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:53:53.302 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:53:53.306 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:53:53.309 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:53:53.309 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:53:53.312 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:53:53.324 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:53:53.332 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:53:53.470 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
14:53:53.478 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:53:53.485 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:3
14:53:53.934 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 1.4 seconds (process running for 245.039)
14:53:53.942 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
15:22:05.568 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 17608 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
15:22:05.570 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
15:22:05.570 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
15:22:06.683 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
15:22:07.188 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
15:22:07.189 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:22:07.189 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
15:22:07.235 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:22:07.418 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
15:22:07.466 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@250ca295, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
15:22:07.577 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
15:22:07.605 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
15:22:07.623 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
15:22:07.641 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
15:22:07.661 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
15:22:07.683 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
15:22:07.703 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
15:22:07.730 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
15:22:07.734 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
15:22:07.734 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
15:22:07.739 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
15:22:07.755 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
15:22:07.770 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
15:22:07.784 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
15:22:07.795 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
15:22:07.808 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
15:22:07.818 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
15:22:07.862 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
15:22:07.877 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
15:22:07.892 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
15:22:07.905 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
15:22:07.912 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
15:22:07.912 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
15:22:07.918 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
15:22:07.922 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
15:22:07.922 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
15:22:07.929 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
15:22:07.939 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
15:22:07.952 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
15:22:08.090 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
15:22:08.099 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
15:22:08.105 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:7
15:22:09.361 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
15:22:09.391 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.183 seconds (process running for 5.148)
15:22:09.416 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
15:23:45.384 [http-nio-9090-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:23:45.877 [http-nio-9090-exec-8] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
15:23:46.365 [http-nio-9090-exec-3] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 463 ms
15:23:48.552 [http-nio-9090-exec-10] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
15:23:50.902 [http-nio-9090-exec-7] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
15:23:51.043 [http-nio-9090-exec-7] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@26686344
15:23:51.044 [http-nio-9090-exec-7] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
15:23:51.051 [http-nio-9090-exec-7] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:23:51.064 [http-nio-9090-exec-7] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:23:51.081 [http-nio-9090-exec-7] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:24:04.632 [http-nio-9090-exec-4] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates WHERE (production_dept LIKE ?)
15:24:04.633 [http-nio-9090-exec-4] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: %A%(String)
15:24:04.635 [http-nio-9090-exec-4] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 4
15:25:00.620 [Thread-5] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
15:25:00.766 [Thread-5] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
15:25:00.771 [Thread-5] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
15:25:00.921 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 17608 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
15:25:00.921 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
15:25:00.921 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
15:25:01.424 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
15:25:01.425 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:25:01.425 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
15:25:01.443 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:25:01.498 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
15:25:01.523 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@3a0dc480, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
15:25:01.537 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
15:25:01.550 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
15:25:01.563 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
15:25:01.575 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
15:25:01.584 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
15:25:01.595 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
15:25:01.610 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
15:25:01.627 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
15:25:01.632 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
15:25:01.632 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
15:25:01.642 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
15:25:01.657 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
15:25:01.672 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
15:25:01.687 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
15:25:01.703 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
15:25:01.719 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
15:25:01.737 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
15:25:01.760 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
15:25:01.777 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
15:25:01.793 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
15:25:01.809 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
15:25:01.814 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
15:25:01.814 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
15:25:01.821 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
15:25:01.824 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
15:25:01.824 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
15:25:01.832 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
15:25:01.844 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
15:25:01.857 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
15:25:02.007 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
15:25:02.018 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
15:25:02.026 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:7
15:25:02.678 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
15:25:02.692 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 1.819 seconds (process running for 178.45)
15:25:02.695 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
15:28:38.700 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 14016 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
15:28:38.703 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
15:28:38.704 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
15:28:39.734 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
15:28:40.298 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
15:28:40.300 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:28:40.300 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
15:28:40.347 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:28:40.531 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
15:28:40.577 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@250ca295, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
15:28:40.685 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
15:28:40.715 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
15:28:40.734 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
15:28:40.754 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
15:28:40.783 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
15:28:40.806 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
15:28:40.826 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
15:28:40.845 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
15:28:40.850 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
15:28:40.851 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
15:28:40.856 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
15:28:40.872 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
15:28:40.886 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
15:28:40.901 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
15:28:40.914 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
15:28:40.927 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
15:28:40.943 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
15:28:41.012 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
15:28:41.048 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
15:28:41.069 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
15:28:41.091 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
15:28:41.098 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
15:28:41.099 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
15:28:41.110 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
15:28:41.116 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
15:28:41.117 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
15:28:41.126 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
15:28:41.146 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
15:28:41.174 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
15:28:41.347 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
15:28:41.358 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
15:28:41.365 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:1
15:28:42.788 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
15:28:42.821 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.504 seconds (process running for 5.454)
15:28:42.834 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
15:28:48.381 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:28:48.715 [http-nio-9090-exec-3] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
15:28:49.177 [http-nio-9090-exec-9] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 438 ms
15:28:57.584 [http-nio-9090-exec-5] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
15:29:00.391 [http-nio-9090-exec-4] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
15:29:00.546 [http-nio-9090-exec-4] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@126f5eb0
15:29:00.547 [http-nio-9090-exec-4] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
15:29:00.556 [http-nio-9090-exec-4] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:29:00.570 [http-nio-9090-exec-4] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:29:00.588 [http-nio-9090-exec-4] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:29:19.723 [http-nio-9090-exec-6] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates WHERE (material_category_name LIKE ?)
15:29:19.724 [http-nio-9090-exec-6] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: %A%(String)
15:29:19.725 [http-nio-9090-exec-6] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 3
15:29:30.898 [http-nio-9090-exec-8] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:29:30.899 [http-nio-9090-exec-8] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:29:30.904 [http-nio-9090-exec-8] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:29:45.109 [http-nio-9090-exec-3] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:29:45.109 [http-nio-9090-exec-3] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:29:45.110 [http-nio-9090-exec-3] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:29:46.291 [http-nio-9090-exec-9] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:29:46.291 [http-nio-9090-exec-9] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:29:46.292 [http-nio-9090-exec-9] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:29:46.459 [http-nio-9090-exec-5] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:29:46.459 [http-nio-9090-exec-5] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:29:46.461 [http-nio-9090-exec-5] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:29:54.641 [http-nio-9090-exec-7] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:29:54.641 [http-nio-9090-exec-7] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:29:54.682 [http-nio-9090-exec-7] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:30:25.336 [http-nio-9090-exec-8] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:30:25.337 [http-nio-9090-exec-8] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:30:25.337 [http-nio-9090-exec-8] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:30:44.014 [http-nio-9090-exec-3] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:30:44.014 [http-nio-9090-exec-3] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:30:44.015 [http-nio-9090-exec-3] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:31:29.368 [http-nio-9090-exec-9] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:31:29.369 [http-nio-9090-exec-9] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:31:29.370 [http-nio-9090-exec-9] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:31:56.166 [http-nio-9090-exec-5] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:31:56.166 [http-nio-9090-exec-5] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:31:56.167 [http-nio-9090-exec-5] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:31:59.412 [http-nio-9090-exec-2] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:31:59.413 [http-nio-9090-exec-2] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:31:59.414 [http-nio-9090-exec-2] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:31:59.589 [http-nio-9090-exec-10] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:31:59.589 [http-nio-9090-exec-10] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:31:59.590 [http-nio-9090-exec-10] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:31:59.920 [http-nio-9090-exec-1] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:31:59.920 [http-nio-9090-exec-1] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:31:59.921 [http-nio-9090-exec-1] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:32:00.065 [http-nio-9090-exec-7] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:32:00.065 [http-nio-9090-exec-7] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:32:00.066 [http-nio-9090-exec-7] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:32:15.864 [http-nio-9090-exec-4] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:32:15.865 [http-nio-9090-exec-4] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:32:15.866 [http-nio-9090-exec-4] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:32:22.149 [http-nio-9090-exec-10] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
15:32:25.635 [http-nio-9090-exec-2] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:32:25.635 [http-nio-9090-exec-2] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:32:25.636 [http-nio-9090-exec-2] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:33:19.876 [http-nio-9090-exec-7] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates WHERE (material_category_name LIKE ?)
15:33:19.877 [http-nio-9090-exec-7] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: %A%(String)
15:33:19.878 [http-nio-9090-exec-7] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 3
15:33:27.716 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
15:33:30.334 [http-nio-9090-exec-5] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:33:30.335 [http-nio-9090-exec-5] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:33:30.336 [http-nio-9090-exec-5] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:33:39.834 [http-nio-9090-exec-2] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates WHERE (steel_class LIKE ?)
15:33:39.834 [http-nio-9090-exec-2] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: %碳%(String)
15:33:39.835 [http-nio-9090-exec-2] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 4
15:33:44.303 [http-nio-9090-exec-7] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
15:33:46.207 [http-nio-9090-exec-8] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates
15:33:46.208 [http-nio-9090-exec-8] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 
15:33:46.209 [http-nio-9090-exec-8] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 10
15:33:55.114 [http-nio-9090-exec-9] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield FROM material_yield_rates WHERE (specifications LIKE ?)
15:33:55.115 [http-nio-9090-exec-9] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: %10%(String)
15:33:55.115 [http-nio-9090-exec-9] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 1
15:51:22.737 [Thread-5] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
15:51:23.069 [Thread-5] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
15:51:23.074 [Thread-5] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
15:51:23.300 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 14016 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
15:51:23.301 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
15:51:23.301 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
15:51:24.832 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
15:51:24.833 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:51:24.833 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
15:51:24.850 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:51:24.899 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
15:51:24.921 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@3cbed1e3, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
15:51:24.965 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
15:51:24.997 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
15:51:25.015 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
15:51:25.040 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
15:51:25.058 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
15:51:25.085 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
15:51:25.106 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
15:51:25.151 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
15:51:25.155 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
15:51:25.155 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
15:51:25.163 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
15:51:25.182 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
15:51:25.198 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
15:51:25.210 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
15:51:25.226 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
15:51:25.242 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
15:51:25.256 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
15:51:25.277 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
15:51:25.293 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
15:51:25.309 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
15:51:25.326 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
15:51:25.330 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
15:51:25.330 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
15:51:25.338 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
15:51:25.342 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
15:51:25.342 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
15:51:25.352 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
15:51:25.376 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
15:51:25.390 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
15:51:25.574 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
15:51:25.586 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
15:51:25.593 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:1
15:51:26.597 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.368 seconds (process running for 1369.229)
15:51:26.606 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
16:11:59.492 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 70964 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
16:11:59.507 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
16:11:59.507 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
16:12:01.178 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
16:12:02.434 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
16:12:02.435 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:12:02.435 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
16:12:02.617 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:12:02.818 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
16:12:02.862 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@7c06cf9d, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
16:12:03.161 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
16:12:03.188 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
16:12:03.205 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
16:12:03.218 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
16:12:03.231 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
16:12:03.245 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
16:12:03.261 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
16:12:03.273 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
16:12:03.277 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
16:12:03.278 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
16:12:03.283 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
16:12:03.296 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
16:12:03.307 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
16:12:03.321 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
16:12:03.331 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
16:12:03.344 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
16:12:03.358 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
16:12:03.428 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
16:12:03.446 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
16:12:03.462 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
16:12:03.476 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
16:12:03.481 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
16:12:03.481 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
16:12:03.487 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
16:12:03.492 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
16:12:03.492 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
16:12:03.498 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
16:12:03.512 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
16:12:03.525 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
16:12:03.655 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
16:12:03.664 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
16:12:03.671 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:13
16:12:04.846 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
16:12:04.873 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 5.984 seconds (process running for 10.595)
16:12:04.883 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
16:12:14.361 [http-nio-9090-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:12:15.035 [http-nio-9090-exec-10] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
16:12:15.485 [http-nio-9090-exec-8] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 426 ms
16:12:18.170 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
16:13:13.326 [http-nio-9090-exec-2] INFO  c.n.i.c.StandardRawMaterialsController - [saveStandardRawMaterials,48] - 新增原料
16:13:13.403 [http-nio-9090-exec-2] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
16:13:13.677 [http-nio-9090-exec-2] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@f79c6fc
16:13:13.678 [http-nio-9090-exec-2] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
16:13:13.685 [http-nio-9090-exec-2] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (department_name LIKE ?)
16:13:13.698 [http-nio-9090-exec-2] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: %部门名称%(String)
16:13:13.714 [http-nio-9090-exec-2] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
16:13:13.737 [http-nio-9090-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (equipment_name LIKE ?)
16:13:13.737 [http-nio-9090-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: %101%(String)
16:13:13.739 [http-nio-9090-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
16:13:13.811 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.insert - [debug,135] - ==>  Preparing: INSERT INTO standard_raw_materials ( id, name, element, secondary_element, carbon_element, department_id, production_equipment_id, composition, yield_rate, price, is_custom, category, createuser ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
16:13:13.830 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.insert - [debug,135] - ==> Parameters: 0(Long), 333(String), 333(String), 333(String), 333(String), 3(Long), 101(Long), 333(BigDecimal), 333(BigDecimal), 333(BigDecimal), 1(String), 333(String), (String)
16:13:13.831 [http-nio-9090-exec-2] DEBUG c.n.i.m.S.insert - [debug,135] - <==    Updates: 1
16:19:55.502 [Thread-5] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
16:19:55.651 [Thread-5] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
16:19:55.655 [Thread-5] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
16:19:55.801 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 70964 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
16:19:55.801 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
16:19:55.801 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
16:19:56.361 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
16:19:56.361 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:19:56.361 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
16:19:56.376 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:19:56.394 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - [refresh,633] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'basicInfoController': Unsatisfied dependency expressed through field 'steelGradesService': Error creating bean with name 'steelGradesServiceImpl': Injection of autowired dependencies failed
16:19:56.395 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
16:19:56.470 [restartedMain] ERROR o.s.b.SpringApplication - [reportFailure,859] - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'basicInfoController': Unsatisfied dependency expressed through field 'steelGradesService': Error creating bean with name 'steelGradesServiceImpl': Injection of autowired dependencies failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.nercar.ingredient.Application.main(Application.java:16)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'steelGradesServiceImpl': Injection of autowired dependencies failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:515)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1448)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1358)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 25 common frames omitted
Caused by: java.lang.TypeNotPresentException: Type com.nercar.ingredient.domain.po.SteelGrades not present
	at java.base/sun.reflect.generics.factory.CoreReflectionFactory.makeNamedType(CoreReflectionFactory.java:117)
	at java.base/sun.reflect.generics.visitor.Reifier.visitClassTypeSignature(Reifier.java:125)
	at java.base/sun.reflect.generics.tree.ClassTypeSignature.accept(ClassTypeSignature.java:49)
	at java.base/sun.reflect.generics.visitor.Reifier.reifyTypeArguments(Reifier.java:68)
	at java.base/sun.reflect.generics.visitor.Reifier.visitClassTypeSignature(Reifier.java:138)
	at java.base/sun.reflect.generics.tree.ClassTypeSignature.accept(ClassTypeSignature.java:49)
	at java.base/sun.reflect.generics.repository.ClassRepository.computeSuperInterfaces(ClassRepository.java:117)
	at java.base/sun.reflect.generics.repository.ClassRepository.getSuperInterfaces(ClassRepository.java:95)
	at java.base/java.lang.Class.getGenericInterfaces(Class.java:1211)
	at org.springframework.core.ResolvableType.getInterfaces(ResolvableType.java:517)
	at org.springframework.core.ResolvableType.as(ResolvableType.java:465)
	at org.springframework.core.ResolvableType.as(ResolvableType.java:466)
	at org.springframework.core.ResolvableType.forField(ResolvableType.java:1230)
	at org.springframework.beans.factory.config.DependencyDescriptor.getResolvableType(DependencyDescriptor.java:291)
	at org.springframework.beans.factory.support.GenericTypeAwareAutowireCandidateResolver.checkGenericTypeMatch(GenericTypeAwareAutowireCandidateResolver.java:77)
	at org.springframework.beans.factory.support.GenericTypeAwareAutowireCandidateResolver.isAutowireCandidate(GenericTypeAwareAutowireCandidateResolver.java:69)
	at org.springframework.beans.factory.annotation.QualifierAnnotationAutowireCandidateResolver.isAutowireCandidate(QualifierAnnotationAutowireCandidateResolver.java:157)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.isAutowireCandidate(DefaultListableBeanFactory.java:885)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.isAutowireCandidate(DefaultListableBeanFactory.java:844)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.isAutowireCandidate(DefaultListableBeanFactory.java:827)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1657)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1402)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1358)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	... 36 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.nercar.ingredient.domain.po.SteelGrades
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:467)
	at org.springframework.boot.devtools.restart.classloader.RestartClassLoader.loadClass(RestartClassLoader.java:121)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:467)
	at java.base/sun.reflect.generics.factory.CoreReflectionFactory.makeNamedType(CoreReflectionFactory.java:114)
	... 62 common frames omitted
16:19:58.842 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 70964 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
16:19:58.842 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
16:19:58.842 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
16:19:59.652 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
16:19:59.652 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:19:59.653 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
16:19:59.669 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:19:59.722 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
16:19:59.742 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@15cdec9, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
16:19:59.758 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
16:19:59.775 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
16:19:59.786 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
16:19:59.799 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
16:19:59.810 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
16:19:59.820 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
16:19:59.832 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
16:19:59.846 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
16:19:59.849 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
16:19:59.850 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
16:19:59.855 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
16:19:59.870 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
16:19:59.887 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
16:19:59.902 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
16:19:59.917 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
16:19:59.931 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
16:19:59.942 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
16:19:59.960 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
16:19:59.970 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
16:19:59.982 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
16:19:59.993 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
16:19:59.995 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
16:19:59.996 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
16:20:00.000 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
16:20:00.004 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
16:20:00.005 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
16:20:00.011 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
16:20:00.024 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
16:20:00.036 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
16:20:01.354 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
16:20:01.363 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
16:20:01.369 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:13
16:20:01.879 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
16:20:01.887 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.088 seconds (process running for 487.61)
16:20:01.895 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
16:25:20.876 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 66044 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
16:25:20.878 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
16:25:20.879 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
16:25:21.877 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
16:25:22.417 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
16:25:22.418 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:25:22.418 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
16:25:22.463 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:25:22.658 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
16:25:22.707 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@674e4fc1, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
16:25:22.827 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
16:25:22.860 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
16:25:22.881 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
16:25:22.898 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
16:25:22.914 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
16:25:22.929 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
16:25:22.944 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
16:25:22.958 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
16:25:22.962 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
16:25:22.962 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
16:25:22.967 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
16:25:22.980 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
16:25:22.994 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
16:25:23.009 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
16:25:23.019 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
16:25:23.030 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
16:25:23.043 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
16:25:23.084 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
16:25:23.098 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
16:25:23.111 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
16:25:23.124 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
16:25:23.128 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
16:25:23.128 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
16:25:23.135 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
16:25:23.138 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
16:25:23.139 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
16:25:23.144 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
16:25:23.160 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
16:25:23.174 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
16:25:23.301 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
16:25:23.309 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
16:25:23.315 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:31
16:25:24.535 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
16:25:24.559 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.141 seconds (process running for 5.106)
16:25:24.568 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
16:25:29.823 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:25:30.193 [http-nio-9090-exec-2] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
16:25:30.725 [http-nio-9090-exec-1] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 494 ms
16:25:35.460 [http-nio-9090-exec-10] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
16:25:38.370 [http-nio-9090-exec-3] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
16:25:38.517 [http-nio-9090-exec-3] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@20c5c030
16:25:38.520 [http-nio-9090-exec-3] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
16:25:38.528 [http-nio-9090-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments
16:25:38.545 [http-nio-9090-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 
16:25:38.564 [http-nio-9090-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 2
16:25:49.889 [http-nio-9090-exec-4] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (equipment_name LIKE ?)
16:25:49.889 [http-nio-9090-exec-4] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: %03%(String)
16:25:49.891 [http-nio-9090-exec-4] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
16:26:04.296 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
16:26:04.302 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
18:03:33.628 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 70988 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
18:03:33.630 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
18:03:33.631 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
18:03:34.645 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
18:03:35.166 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
18:03:35.167 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:03:35.168 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
18:03:35.221 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:03:35.437 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
18:03:35.476 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@38e7b9e8, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
18:03:35.596 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
18:03:35.625 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
18:03:35.643 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
18:03:35.660 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
18:03:35.675 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
18:03:35.690 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
18:03:35.706 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
18:03:35.720 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
18:03:35.725 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
18:03:35.726 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
18:03:35.731 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
18:03:35.744 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
18:03:35.757 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
18:03:35.771 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
18:03:35.781 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
18:03:35.795 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
18:03:35.809 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
18:03:35.849 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
18:03:35.863 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
18:03:35.876 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
18:03:35.890 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
18:03:35.894 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
18:03:35.894 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
18:03:35.899 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
18:03:35.904 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
18:03:35.904 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
18:03:35.912 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
18:03:35.923 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
18:03:35.937 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
18:03:36.067 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
18:03:36.075 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
18:03:36.081 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:15
18:03:37.221 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
18:03:37.252 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.978 seconds (process running for 4.887)
18:03:37.260 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
18:04:29.099 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:04:29.496 [http-nio-9090-exec-3] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
18:04:29.979 [http-nio-9090-exec-2] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 457 ms
18:04:33.351 [http-nio-9090-exec-5] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
18:04:57.101 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 41668 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
18:04:57.105 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
18:04:57.106 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
18:04:58.111 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
18:04:58.638 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
18:04:58.640 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:04:58.641 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
18:04:58.694 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:04:58.874 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
18:04:58.919 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@2bc0ed71, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
18:04:59.067 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
18:04:59.105 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
18:04:59.125 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
18:04:59.144 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
18:04:59.162 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
18:04:59.176 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
18:04:59.187 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
18:04:59.199 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
18:04:59.203 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
18:04:59.203 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
18:04:59.209 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
18:04:59.221 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
18:04:59.235 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
18:04:59.252 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
18:04:59.263 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
18:04:59.274 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
18:04:59.285 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
18:04:59.328 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
18:04:59.343 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
18:04:59.357 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
18:04:59.372 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
18:04:59.377 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
18:04:59.377 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
18:04:59.381 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
18:04:59.385 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
18:04:59.385 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
18:04:59.391 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
18:04:59.403 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
18:04:59.417 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
18:04:59.545 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
18:04:59.554 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
18:04:59.559 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:10
18:05:00.702 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
18:05:00.730 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.01 seconds (process running for 4.891)
18:05:00.738 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
18:05:14.791 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:05:15.124 [http-nio-9090-exec-7] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
18:05:15.700 [http-nio-9090-exec-5] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 545 ms
18:05:18.706 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
18:05:21.658 [http-nio-9090-exec-8] INFO  c.n.i.c.StandardRawMaterialsController - [getStandardRawMaterials,57] - 查看原料详情
18:05:21.681 [http-nio-9090-exec-8] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
18:05:21.815 [http-nio-9090-exec-8] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3e55a71
18:05:21.818 [http-nio-9090-exec-8] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
18:05:21.825 [http-nio-9090-exec-8] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
18:05:21.839 [http-nio-9090-exec-8] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
18:05:21.874 [http-nio-9090-exec-8] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
18:05:22.018 [http-nio-9090-exec-8] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
18:05:22.018 [http-nio-9090-exec-8] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 101(Long)
18:05:22.020 [http-nio-9090-exec-8] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
18:05:22.021 [http-nio-9090-exec-8] DEBUG c.n.i.m.D.selectList - [debug,135] - ==>  Preparing: SELECT id,department_name,createuser,createtime,updatetime FROM departments WHERE (id = ?)
18:05:22.022 [http-nio-9090-exec-8] DEBUG c.n.i.m.D.selectList - [debug,135] - ==> Parameters: 1(Long)
18:05:22.023 [http-nio-9090-exec-8] DEBUG c.n.i.m.D.selectList - [debug,135] - <==      Total: 1
18:08:05.137 [http-nio-9090-exec-4] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
18:08:44.798 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
18:08:44.801 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
