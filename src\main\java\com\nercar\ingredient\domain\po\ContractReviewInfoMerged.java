package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 合同评审信息合并表
 * @TableName contract_review_info_merged
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("contract_review_info_merged")
@Schema(description = "合同评审信息合并表")
public class ContractReviewInfoMerged extends BaseEntity {
    
    /**
     * 合同信息主键ID
     */
    @TableId
    @Schema(description = "合同信息主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    
    /**
     * 合同编号
     */
    @Schema(description = "合同编号，如：F0003-2025")
    private String code;
    
    /**
     * 客户名称
     */
    @Schema(description = "客户名称，如：小米公司")
    private String customerName;
    
    /**
     * 客户联系电话
     */
    @Schema(description = "客户联系电话，如：12345678901")
    private String customerPhone;
    
    /**
     * 钢材类型名称
     */
    @Schema(description = "钢材类型名称，如：汽车钢、工具钢、不锈钢")
    private String steelTypeName;
    
    /**
     * 钢材牌号名称
     */
    @Schema(description = "钢材牌号名称，如：N07718、20CrMnTi、304")
    private String steelGradeName;
    
    /**
     * 规格
     */
    @Schema(description = "格式化后的规格显示，如：薄板:长100000mm*宽1000mm（备注）")
    private String specification;
    
    /**
     * 规格尺寸值1
     */
    @Schema(description = "规格尺寸值1，多个值用逗号分隔，如：100000,1000")
    private String value1;
    
    /**
     * 规格尺寸值2
     */
    @Schema(description = "规格尺寸值2，扩展字段")
    private String value2;
    
    /**
     * 规格尺寸值3
     */
    @Schema(description = "规格尺寸值3，扩展字段")
    private String value3;
    
    /**
     * 钢材数量
     */
    @Schema(description = "钢材数量")
    private Integer steelNumber;
    
    /**
     * 钢材数量单位
     */
    @Schema(description = "钢材数量单位，如：吨、捆、根、件")
    private String steelNumberUnit;
    
    /**
     * 执行标准名称
     */
    @Schema(description = "执行标准名称，如：GB/T14841-2008、QJ/DT01.13590-2014")
    private String standardName;
    
    /**
     * 技术条件名称
     */
    @Schema(description = "技术条件名称，如：技术中心推荐、技术中心提供、用户提供")
    private String technicalStandardName;
    
    /**
     * 交货状态
     */
    @Schema(description = "交货状态，如：锻造退火车光削皮、热轧、冷拉")
    private String deliveryStatus;
    
    /**
     * 加工用途
     */
    @Schema(description = "加工用途，如：不说明、军工用钢、航空航天")
    private String processingPurpose;
    
    /**
     * 冶炼工艺
     */
    @Schema(description = "冶炼工艺，JSON格式，如：[\"电炉\",\"LF\",\"真空感应炉\",\"连铸\"]")
    private String smeltingProcess;
    
    /**
     * 特殊要求说明
     */
    @Schema(description = "特殊要求说明，如：特殊要求测试2")
    private String specialRequirements;
    
    /**
     * 备注信息
     */
    @Schema(description = "备注信息，如：备注测试2")
    private String remark;
    
    /**
     * 退回原因
     */
    @Schema(description = "退回原因，如：需核定外委、技术条件不明确")
    private String returnReason;
    
    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名，如：刘晓斌")
    private String authorName;
    
    /**
     * 业务员姓名
     */
    @Schema(description = "业务员姓名，如：业务员C")
    private String salesmanName;
    
    /**
     * 是否为头材
     */
    @Schema(description = "是否为头材，1-是，0-否")
    private Integer isHead;
    
    /**
     * 是否进行成本测算
     */
    @Schema(description = "是否进行成本测算，true-是，false-否")
    private Boolean isCostCalculation;
    
    /**
     * 是否评审能否生产
     */
    @Schema(description = "是否评审能否生产，true-是，false-否")
    private Boolean isProduce;
    
    /**
     * 是否评审外委厂商
     */
    @Schema(description = "是否评审外委厂商，true-是，false-否")
    private Boolean isOutsourcingFirm;
    
    /**
     * 外委业务ID
     */
    @Schema(description = "外委业务ID，关联外委厂商")
    private Long outsourcingId;
    
    /**
     * 外委厂商名称
     */
    @Schema(description = "外委厂商名称，如：外委厂商1")
    private String outsourcingName;
    
    /**
     * 外委厂商联系电话
     */
    @Schema(description = "外委厂商联系电话，如：12345678901")
    private String outsourcingPhone;
    
    /**
     * 外委价格
     */
    @Schema(description = "外委价格，单位：元")
    private BigDecimal outsourcingPrice;
    
    /**
     * 附件ID
     */
    @Schema(description = "附件ID，关联附件表")
    private Long attachmentId;
    
    /**
     * 推荐路线
     */
    @Schema(description = "推荐路线，0-常规流程，1-特殊流程")
    private Integer recommendRoute;
    
    /**
     * 提交时间
     */
    @Schema(description = "提交时间，如：2025-07-16T10:26:01.189084")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;
    
    /**
     * 记录创建时间
     */
    @Schema(description = "记录创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 记录更新时间
     */
    @Schema(description = "记录更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 创建用户
     */
    @Schema(description = "创建用户")
    private String createUser;
    
    /**
     * 更新用户
     */
    @Schema(description = "更新用户")
    private String updateUser;
    
    /**
     * 记录状态
     */
    @Schema(description = "记录状态，1-有效，0-无效")
    private Integer status;
    
    /**
     * 版本号
     */
    @Schema(description = "版本号，用于乐观锁")
    private Integer version;
}
