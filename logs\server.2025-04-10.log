09:21:51.821 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 39604 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
09:21:51.885 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
09:21:51.886 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
09:22:02.795 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:22:03.872 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
09:22:03.874 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:22:03.875 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
09:22:03.964 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:22:04.344 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:22:04.393 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@2cbe455c, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
09:22:04.723 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
09:22:04.760 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
09:22:04.785 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
09:22:04.808 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
09:22:04.861 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
09:22:04.895 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
09:22:04.923 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
09:22:05.073 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
09:22:05.085 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
09:22:05.086 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:22:05.093 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
09:22:05.256 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
09:22:05.292 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
09:22:05.361 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
09:22:05.387 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
09:22:05.483 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
09:22:05.540 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
09:22:05.631 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
09:22:05.651 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
09:22:05.669 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
09:22:05.689 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
09:22:05.701 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
09:22:05.701 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:22:05.707 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
09:22:05.716 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
09:22:05.717 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:22:05.721 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
09:22:05.740 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
09:22:05.764 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
09:22:05.921 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************ network interface 
09:22:05.931 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
09:22:05.938 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:3
09:22:08.256 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
09:22:08.310 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 18.686 seconds (process running for 25.789)
09:22:08.318 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
09:22:13.793 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:22:13.938 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
09:22:14.708 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@72478c1a
09:22:14.710 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
09:22:14.735 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
09:22:14.772 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
09:22:14.863 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
09:22:14.943 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
09:22:14.943 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
09:22:15.009 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 6
09:22:15.132 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.id, srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from calculation_result as cr left join standard_raw_materials as srm on cr.raw_material_id = srm.id where cr.id=?
09:22:15.132 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long)
09:22:15.222 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 1
09:23:15.988 [http-nio-9021-exec-4] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
09:23:15.989 [http-nio-9021-exec-4] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
09:23:16.069 [http-nio-9021-exec-4] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
09:23:16.071 [http-nio-9021-exec-4] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
09:23:16.071 [http-nio-9021-exec-4] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
09:23:16.150 [http-nio-9021-exec-4] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 6
09:23:16.151 [http-nio-9021-exec-4] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.id, srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from calculation_result as cr left join standard_raw_materials as srm on cr.raw_material_id = srm.id where cr.id=?
09:23:16.152 [http-nio-9021-exec-4] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long)
09:23:16.228 [http-nio-9021-exec-4] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 1
09:24:41.671 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
09:24:41.675 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
09:24:44.894 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 70844 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
09:24:44.896 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
09:24:44.897 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
09:24:45.797 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:24:46.255 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
09:24:46.257 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:24:46.258 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
09:24:46.313 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:24:46.498 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:24:46.537 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@cfd5cd2, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
09:24:46.642 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
09:24:46.669 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
09:24:46.687 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
09:24:46.702 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
09:24:46.725 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
09:24:46.742 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
09:24:46.756 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
09:24:46.776 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
09:24:46.784 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
09:24:46.784 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:24:46.791 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
09:24:46.824 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
09:24:46.836 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
09:24:46.849 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
09:24:46.860 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
09:24:46.870 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
09:24:46.882 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
09:24:46.901 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
09:24:46.913 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
09:24:46.924 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
09:24:46.935 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
09:24:46.939 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
09:24:46.939 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:24:46.945 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
09:24:46.950 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
09:24:46.950 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:24:46.955 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
09:24:46.966 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
09:24:46.979 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
09:24:47.105 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************ network interface 
09:24:47.113 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
09:24:47.119 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:14
09:24:48.262 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
09:24:48.290 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.832 seconds (process running for 4.889)
09:24:48.297 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
09:27:53.029 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 55880 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
09:27:53.033 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
09:27:53.034 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
09:27:54.204 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:27:54.774 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
09:27:54.776 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:27:54.777 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
09:27:54.835 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:27:55.028 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:27:55.075 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@1d283d1, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
09:27:55.197 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
09:27:55.227 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
09:27:55.251 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
09:27:55.285 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
09:27:55.305 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
09:27:55.319 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
09:27:55.332 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
09:27:55.354 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
09:27:55.359 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
09:27:55.359 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:27:55.365 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
09:27:55.401 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
09:27:55.415 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
09:27:55.431 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
09:27:55.450 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
09:27:55.463 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
09:27:55.478 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
09:27:55.500 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
09:27:55.517 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
09:27:55.531 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
09:27:55.546 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
09:27:55.550 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
09:27:55.551 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:27:55.557 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
09:27:55.561 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
09:27:55.561 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:27:55.567 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
09:27:55.579 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
09:27:55.592 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
09:27:55.723 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************ network interface 
09:27:55.732 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
09:27:55.739 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:31
09:27:57.030 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
09:27:57.056 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.526 seconds (process running for 5.823)
09:27:57.065 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
09:28:01.042 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:28:02.743 [http-nio-9021-exec-3] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 646 ms
09:28:18.130 [http-nio-9021-exec-2] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
09:28:18.764 [http-nio-9021-exec-2] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2ce5ef4b
09:28:18.766 [http-nio-9021-exec-2] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
09:28:18.773 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
09:28:18.788 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
09:28:18.874 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
09:28:18.926 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
09:28:18.926 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
09:28:18.991 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 6
09:28:19.064 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.id, srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from calculation_result as cr left join standard_raw_materials as srm on cr.raw_material_id = srm.id where cr.id=?
09:28:19.065 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long)
09:28:19.144 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 1
09:28:43.489 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE id=?
09:28:43.489 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
09:28:43.560 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
09:28:43.561 [http-nio-9021-exec-6] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
09:28:43.562 [http-nio-9021-exec-6] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
09:28:43.640 [http-nio-9021-exec-6] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 6
09:28:43.642 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==>  Preparing: select srm.id, srm.name, srm.composition, srm.yield_rate, srm.price, cr.wieght from calculation_result as cr left join standard_raw_materials as srm on cr.raw_material_id = srm.id where cr.id=?
09:28:43.642 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - ==> Parameters: 1(Long)
09:28:43.719 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectMaterials - [debug,135] - <==      Total: 1
09:32:43.347 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
09:32:43.350 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
09:47:51.337 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 88888 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
09:47:51.340 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
09:47:51.340 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
09:47:53.305 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:47:53.795 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
09:47:53.796 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:47:53.796 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
09:47:53.853 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:47:54.041 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:47:54.085 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@5ce3409b, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
09:47:54.192 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
09:47:54.218 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
09:47:54.236 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
09:47:54.254 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
09:47:54.277 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
09:47:54.294 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
09:47:54.309 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
09:47:54.332 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
09:47:54.336 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
09:47:54.337 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:47:54.342 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
09:47:54.373 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
09:47:54.385 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
09:47:54.400 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
09:47:54.410 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
09:47:54.420 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
09:47:54.432 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
09:47:54.450 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
09:47:54.464 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
09:47:54.476 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
09:47:54.489 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
09:47:54.492 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
09:47:54.493 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:47:54.498 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
09:47:54.502 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
09:47:54.503 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:47:54.508 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
09:47:54.521 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
09:47:54.536 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
09:47:54.663 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************ network interface 
09:47:54.671 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
09:47:54.677 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:7
09:47:55.787 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
09:47:55.814 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.825 seconds (process running for 5.786)
09:47:55.821 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
09:49:21.835 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:49:23.050 [http-nio-9021-exec-5] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 931 ms
09:49:36.555 [http-nio-9021-exec-6] WARN  c.n.i.c.IngredientCalculationController - [getStandardRawMaterials,158] - 未找到符合给定条件的原料
09:53:44.065 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 82284 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
09:53:44.068 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
09:53:44.069 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
09:53:45.025 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:53:45.489 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
09:53:45.491 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:53:45.491 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
09:53:45.546 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:53:45.717 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:53:45.756 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@6b6def36, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
09:53:45.872 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
09:53:45.897 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
09:53:45.915 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
09:53:45.932 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
09:53:45.953 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
09:53:45.971 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
09:53:45.985 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
09:53:46.005 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
09:53:46.013 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
09:53:46.013 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:53:46.019 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
09:53:46.051 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
09:53:46.063 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
09:53:46.077 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
09:53:46.088 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
09:53:46.099 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
09:53:46.110 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
09:53:46.130 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
09:53:46.143 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
09:53:46.157 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
09:53:46.169 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
09:53:46.173 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
09:53:46.173 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:53:46.178 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
09:53:46.182 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
09:53:46.182 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:53:46.188 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
09:53:46.200 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
09:53:46.214 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
09:53:46.359 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************ network interface 
09:53:46.368 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
09:53:46.373 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:3
09:53:47.494 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
09:53:47.525 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.91 seconds (process running for 4.882)
09:53:47.535 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
09:53:51.150 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:53:52.226 [http-nio-9021-exec-4] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 790 ms
09:53:57.058 [http-nio-9021-exec-5] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
09:53:57.706 [http-nio-9021-exec-5] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@56cf1378
09:53:57.707 [http-nio-9021-exec-5] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
09:53:57.715 [http-nio-9021-exec-5] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,createuser,createtime,updatetime FROM standard_raw_materials
09:53:57.730 [http-nio-9021-exec-5] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 
09:53:57.821 [http-nio-9021-exec-5] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 23
09:58:51.813 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
09:58:51.816 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
09:58:57.024 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 82268 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
09:58:57.027 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
09:58:57.028 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
09:58:58.673 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:58:59.345 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
09:58:59.347 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:58:59.348 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
09:58:59.427 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:58:59.682 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
09:58:59.737 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@70d3cdbf, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
09:58:59.869 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
09:58:59.906 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
09:58:59.931 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
09:58:59.951 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
09:58:59.971 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
09:58:59.994 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
09:59:00.017 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
09:59:00.053 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
09:59:00.059 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
09:59:00.059 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:59:00.067 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
09:59:00.116 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
09:59:00.146 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
09:59:00.176 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
09:59:00.202 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
09:59:00.229 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
09:59:00.251 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
09:59:00.280 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
09:59:00.301 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
09:59:00.318 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
09:59:00.335 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
09:59:00.342 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
09:59:00.342 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:59:00.352 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
09:59:00.357 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
09:59:00.357 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
09:59:00.368 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
09:59:00.387 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
09:59:00.407 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
09:59:00.566 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************ network interface 
09:59:00.577 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
09:59:00.583 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:9
09:59:01.825 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
09:59:01.860 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 5.402 seconds (process running for 6.573)
09:59:01.869 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
09:59:10.193 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:59:10.325 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
09:59:10.986 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1bd1d998
09:59:10.987 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
09:59:10.994 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,createuser,createtime,updatetime FROM standard_raw_materials
09:59:11.007 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 
09:59:11.123 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 23
09:59:36.460 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,createuser,createtime,updatetime FROM standard_raw_materials WHERE (name LIKE ?)
09:59:36.461 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: %焦炭 B%(String)
09:59:36.529 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
10:02:58.141 [http-nio-9021-exec-5] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT path_id,raw_material_id FROM path_materials WHERE (path_id = ?)
10:02:58.142 [http-nio-9021-exec-5] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
10:02:58.201 [http-nio-9021-exec-5] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 7
10:02:58.204 [http-nio-9021-exec-5] DEBUG c.n.i.m.S.selectByIds - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,createuser,createtime,updatetime FROM standard_raw_materials WHERE id IN ( ? , ? , ? , ? , ? , ? , ? )
10:02:58.205 [http-nio-9021-exec-5] DEBUG c.n.i.m.S.selectByIds - [debug,135] - ==> Parameters: 1(Long), 2(Long), 3(Long), 4(Long), 5(Long), 6(Long), 7(Long)
10:02:58.281 [http-nio-9021-exec-5] DEBUG c.n.i.m.S.selectByIds - [debug,135] - <==      Total: 7
10:59:27.275 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
10:59:27.279 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
11:40:53.667 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 89240 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
11:40:53.670 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
11:40:53.671 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
11:40:54.703 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
11:40:55.173 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
11:40:55.175 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:40:55.175 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
11:40:55.231 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:40:55.414 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
11:40:55.457 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@6d7740f0, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
11:40:55.567 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
11:40:55.593 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
11:40:55.611 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
11:40:55.628 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
11:40:55.652 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
11:40:55.670 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
11:40:55.685 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
11:40:55.711 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
11:40:55.716 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
11:40:55.717 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:40:55.722 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
11:40:55.757 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
11:40:55.772 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
11:40:55.788 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
11:40:55.802 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
11:40:55.815 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
11:40:55.827 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
11:40:55.850 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
11:40:55.865 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
11:40:55.877 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
11:40:55.889 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
11:40:55.893 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
11:40:55.894 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:40:55.900 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
11:40:55.904 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
11:40:55.905 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:40:55.911 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
11:40:55.922 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
11:40:55.936 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
11:40:56.082 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************ network interface 
11:40:56.091 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
11:40:56.096 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:28
11:40:57.278 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
11:40:57.315 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.159 seconds (process running for 5.379)
11:40:57.324 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
11:41:09.257 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:41:10.246 [http-nio-9021-exec-4] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 646 ms
11:41:53.790 [http-nio-9021-exec-10] INFO  c.n.i.c.IngredientCalculationController - [saveOrUpdateIngotYieldRate,206] - 接收到保存成锭率的请求，DTO: IngotYieldRatesDTO(id=0, processPath=, equipments=, device=123123, updateuser=, departmentName=123123, ingotYield=123, createuser=, createtime=null, updatetime=null)
11:41:53.896 [http-nio-9021-exec-10] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
11:41:54.425 [http-nio-9021-exec-10] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@697fd366
11:41:54.427 [http-nio-9021-exec-10] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
11:41:54.481 [http-nio-9021-exec-10] DEBUG c.n.i.m.I.updateById - [debug,135] - ==>  Preparing: UPDATE ingot_yield_rates SET device=?, department_name=?, ingot_yield=?, updatetime=? WHERE id=?
11:41:54.496 [http-nio-9021-exec-10] DEBUG c.n.i.m.I.updateById - [debug,135] - ==> Parameters: 123123(String), 123123(String), 123(BigDecimal), 2025-04-10T11:41:54.471633300(LocalDateTime), 0(Long)
11:41:54.559 [http-nio-9021-exec-10] DEBUG c.n.i.m.I.updateById - [debug,135] - <==    Updates: 0
11:42:32.395 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
11:42:32.398 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
11:42:37.809 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 81936 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
11:42:37.811 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
11:42:37.812 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
11:42:38.745 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
11:42:39.256 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
11:42:39.258 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:42:39.258 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
11:42:39.316 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:42:39.511 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
11:42:39.553 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@7be38eba, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
11:42:39.716 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
11:42:39.766 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
11:42:39.798 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
11:42:39.817 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
11:42:39.840 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
11:42:39.857 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
11:42:39.870 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
11:42:39.892 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
11:42:39.896 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
11:42:39.897 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:42:39.902 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
11:42:39.943 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
11:42:39.959 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
11:42:39.976 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
11:42:39.991 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
11:42:40.004 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
11:42:40.022 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
11:42:40.038 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
11:42:40.051 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
11:42:40.062 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
11:42:40.072 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
11:42:40.076 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
11:42:40.076 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:42:40.081 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
11:42:40.084 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
11:42:40.084 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:42:40.089 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
11:42:40.100 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
11:42:40.115 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
11:42:40.248 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************ network interface 
11:42:40.257 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
11:42:40.262 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:18
11:42:41.357 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
11:42:41.384 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 3.967 seconds (process running for 4.951)
11:42:41.392 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
11:42:51.269 [http-nio-9021-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:42:51.369 [http-nio-9021-exec-2] INFO  c.n.i.c.IngredientCalculationController - [saveOrUpdateIngotYieldRate,206] - 接收到保存成锭率的请求，DTO: IngotYieldRatesDTO(id=0, processPath=, equipments=, device=1231234, updateuser=, departmentName=1231234, ingotYield=1234, createuser=, createtime=null, updatetime=null)
11:42:51.380 [http-nio-9021-exec-2] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
11:42:52.230 [http-nio-9021-exec-2] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@465d3359
11:42:52.232 [http-nio-9021-exec-2] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
11:42:52.288 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.updateById - [debug,135] - ==>  Preparing: UPDATE ingot_yield_rates SET device=?, department_name=?, ingot_yield=?, updatetime=? WHERE id=?
11:42:52.303 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.updateById - [debug,135] - ==> Parameters: 1231234(String), 1231234(String), 1234(BigDecimal), 2025-04-10T11:42:52.278421600(LocalDateTime), 0(Long)
11:42:52.981 [http-nio-9021-exec-2] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 
### Error updating database.  Cause: org.postgresql.util.PSQLException: ERROR: numeric field overflow
  详细：A field with precision 5, scale 2 must round to an absolute value less than 10^3.
### The error may exist in com/nercar/ingredient/mapper/IngotYieldRatesMapper.java (best guess)
### The error may involve com.nercar.ingredient.mapper.IngotYieldRatesMapper.updateById-Inline
### The error occurred while setting parameters
### SQL: UPDATE ingot_yield_rates  SET device=?,  department_name=?, ingot_yield=?,   updatetime=?  WHERE id=?
### Cause: org.postgresql.util.PSQLException: ERROR: numeric field overflow
  详细：A field with precision 5, scale 2 must round to an absolute value less than 10^3.
; ERROR: numeric field overflow
  详细：A field with precision 5, scale 2 must round to an absolute value less than 10^3.
11:43:18.195 [http-nio-9021-exec-3] INFO  c.n.i.c.IngredientCalculationController - [saveOrUpdateIngotYieldRate,206] - 接收到保存成锭率的请求，DTO: IngotYieldRatesDTO(id=0, processPath=, equipments=, device=1231234, updateuser=, departmentName=1231234, ingotYield=10, createuser=, createtime=null, updatetime=null)
11:43:18.261 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.updateById - [debug,135] - ==>  Preparing: UPDATE ingot_yield_rates SET device=?, department_name=?, ingot_yield=?, updatetime=? WHERE id=?
11:43:18.262 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.updateById - [debug,135] - ==> Parameters: 1231234(String), 1231234(String), 10(BigDecimal), 2025-04-10T11:43:18.261209700(LocalDateTime), 0(Long)
11:43:18.313 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.updateById - [debug,135] - <==    Updates: 0
11:43:42.085 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
11:43:42.088 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
11:43:45.079 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 86100 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
11:43:45.083 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
11:43:45.083 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
11:43:46.214 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
11:43:46.756 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
11:43:46.758 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:43:46.758 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
11:43:46.819 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:43:47.032 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
11:43:47.084 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@6b6def36, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
11:43:47.228 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
11:43:47.267 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
11:43:47.296 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
11:43:47.319 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
11:43:47.344 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
11:43:47.360 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
11:43:47.376 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
11:43:47.402 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
11:43:47.406 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
11:43:47.406 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:43:47.413 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
11:43:47.451 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
11:43:47.467 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
11:43:47.488 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
11:43:47.508 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
11:43:47.528 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
11:43:47.549 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
11:43:47.578 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
11:43:47.597 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
11:43:47.618 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
11:43:47.635 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
11:43:47.640 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
11:43:47.640 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:43:47.648 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
11:43:47.653 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
11:43:47.654 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:43:47.661 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
11:43:47.674 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
11:43:47.693 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
11:43:47.873 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************ network interface 
11:43:47.882 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
11:43:47.887 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:2
11:43:49.004 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
11:43:49.035 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.476 seconds (process running for 5.701)
11:43:49.045 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
11:44:05.707 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:44:05.803 [http-nio-9021-exec-1] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- JSON parse error: Cannot construct instance of `com.nercar.ingredient.domain.dto.IngotYieldRatesDTO` (although at least one Creator exists): no String-argument constructor/factory method to deserialize from String value ('{
  "id": ,
  "processPath": "",
  "equipments": "",
  "device": "12312345",
  "updateuser": "",
  "departmentName": "12312345",
  "ingotYield": 10,
  "createuser": "",
  "createtime": "",
  "updatetime": ""
}')
11:44:23.253 [http-nio-9021-exec-3] INFO  c.n.i.c.IngredientCalculationController - [saveOrUpdateIngotYieldRate,206] - 接收到保存成锭率的请求，DTO: IngotYieldRatesDTO(id=null, processPath=, equipments=, device=12312345, updateuser=, departmentName=12312345, ingotYield=10, createuser=, createtime=null, updatetime=null)
11:44:23.263 [http-nio-9021-exec-3] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
11:44:23.851 [http-nio-9021-exec-3] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3900e68a
11:44:23.852 [http-nio-9021-exec-3] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
11:44:24.003 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.insert - [debug,135] - ==>  Preparing: INSERT INTO ingot_yield_rates ( id, device, department_name, ingot_yield, createuser, createtime, updatetime ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
11:44:24.017 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.insert - [debug,135] - ==> Parameters: 1910177013260492801(Long), 12312345(String), 12312345(String), 10(BigDecimal), null, 2025-04-10T11:44:23.992257500(LocalDateTime), 2025-04-10T11:44:23.994251700(LocalDateTime)
11:44:24.084 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.insert - [debug,135] - <==    Updates: 1
11:44:24.134 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,process_path,equipments,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE (department_name = ? AND ingot_yield = ? AND device = ?)
11:44:24.135 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 12312345(String), 10(BigDecimal), 12312345(String)
11:44:24.375 [http-nio-9021-exec-3] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: column "equipments" does not exist
  位置：25
### The error may exist in com/nercar/ingredient/mapper/IngotYieldRatesMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT  id,process_path,equipments,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime  FROM ingot_yield_rates      WHERE  (department_name = ? AND ingot_yield = ? AND device = ?)
### Cause: org.postgresql.util.PSQLException: ERROR: column "equipments" does not exist
  位置：25
; bad SQL grammar []
11:45:24.880 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
11:45:24.882 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
11:45:30.737 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 74440 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
11:45:30.740 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
11:45:30.741 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
11:45:31.751 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
11:45:32.258 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
11:45:32.260 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:45:32.261 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
11:45:32.318 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:45:32.495 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
11:45:32.539 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@2fba0dac, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
11:45:32.649 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
11:45:32.724 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
11:45:32.746 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
11:45:32.786 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
11:45:32.848 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
11:45:32.898 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
11:45:32.923 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
11:45:32.963 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
11:45:32.970 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
11:45:32.972 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:45:32.986 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
11:45:33.063 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
11:45:33.083 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
11:45:33.106 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
11:45:33.126 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
11:45:33.138 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
11:45:33.155 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
11:45:33.176 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
11:45:33.192 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
11:45:33.207 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
11:45:33.222 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
11:45:33.226 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
11:45:33.227 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:45:33.236 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
11:45:33.240 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
11:45:33.241 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:45:33.249 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
11:45:33.264 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
11:45:33.283 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
11:45:33.434 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************ network interface 
11:45:33.446 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
11:45:33.454 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:2
11:45:34.791 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
11:45:34.819 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.497 seconds (process running for 5.593)
11:45:34.828 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
11:45:38.124 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:45:38.258 [http-nio-9021-exec-1] INFO  c.n.i.c.IngredientCalculationController - [saveOrUpdateIngotYieldRate,206] - 接收到保存成锭率的请求，DTO: IngotYieldRatesDTO(id=null, processPath=, equipments=, device=12312345, updateuser=, departmentName=12312345, ingotYield=10, createuser=, createtime=null, updatetime=null)
11:45:38.276 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
11:45:38.950 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1dc454
11:45:38.951 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
11:45:38.996 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.insert - [debug,135] - ==>  Preparing: INSERT INTO ingot_yield_rates ( id, device, department_name, ingot_yield, createuser, createtime, updatetime ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
11:45:39.010 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.insert - [debug,135] - ==> Parameters: 1910177327808126977(Long), 12312345(String), 12312345(String), 10(BigDecimal), null, 2025-04-10T11:45:38.987131500(LocalDateTime), 2025-04-10T11:45:38.988128700(LocalDateTime)
11:45:39.084 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.insert - [debug,135] - <==    Updates: 1
11:45:39.112 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE (department_name = ? AND ingot_yield = ? AND device = ?)
11:45:39.112 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 12312345(String), 10(BigDecimal), 12312345(String)
11:45:39.188 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 1
11:46:09.125 [http-nio-9021-exec-4] INFO  c.n.i.c.IngredientCalculationController - [saveOrUpdateIngotYieldRate,206] - 接收到保存成锭率的请求，DTO: IngotYieldRatesDTO(id=1910177327808126977, processPath=, equipments=, device=123, updateuser=, departmentName=123, ingotYield=123, createuser=, createtime=null, updatetime=null)
11:46:09.197 [http-nio-9021-exec-4] DEBUG c.n.i.m.I.updateById - [debug,135] - ==>  Preparing: UPDATE ingot_yield_rates SET device=?, department_name=?, ingot_yield=?, updatetime=? WHERE id=?
11:46:09.197 [http-nio-9021-exec-4] DEBUG c.n.i.m.I.updateById - [debug,135] - ==> Parameters: 123(String), 123(String), 123(BigDecimal), 2025-04-10T11:46:09.197037(LocalDateTime), 1910177327808126977(Long)
11:46:09.254 [http-nio-9021-exec-4] DEBUG c.n.i.m.I.updateById - [debug,135] - <==    Updates: 1
11:47:35.981 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
11:47:35.985 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
11:56:56.246 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 75632 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
11:56:56.248 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
11:56:56.248 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
11:56:57.339 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
11:56:57.831 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
11:56:57.833 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:56:57.833 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
11:56:57.886 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:56:58.061 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
11:56:58.101 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@5ce3409b, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
11:56:58.208 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
11:56:58.235 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
11:56:58.253 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
11:56:58.270 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
11:56:58.292 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
11:56:58.308 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
11:56:58.323 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
11:56:58.350 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
11:56:58.354 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
11:56:58.354 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:56:58.364 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
11:56:58.399 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
11:56:58.412 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
11:56:58.426 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
11:56:58.436 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
11:56:58.447 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
11:56:58.458 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
11:56:58.474 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
11:56:58.493 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
11:56:58.513 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
11:56:58.535 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
11:56:58.540 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
11:56:58.541 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:56:58.546 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
11:56:58.550 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
11:56:58.551 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
11:56:58.559 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
11:56:58.575 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
11:56:58.592 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
11:56:58.722 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************ network interface 
11:56:58.731 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
11:56:58.737 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:6
11:56:59.876 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
11:56:59.904 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.018 seconds (process running for 4.88)
11:56:59.912 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
11:57:08.433 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:57:09.277 [http-nio-9021-exec-4] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 557 ms
11:58:01.609 [http-nio-9021-exec-6] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- JSON parse error: Cannot construct instance of `com.nercar.ingredient.domain.dto.MaterialYieldRatesDTO` (although at least one Creator exists): no String-argument constructor/factory method to deserialize from String value ('{
  "id":,
  "device": "123",
  "productionDept": "123",
  "materialYield": "123",
}')
11:58:07.085 [http-nio-9021-exec-7] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- JSON parse error: Cannot construct instance of `com.nercar.ingredient.domain.dto.MaterialYieldRatesDTO` (although at least one Creator exists): no String-argument constructor/factory method to deserialize from String value ('{
  "device": "123",
  "productionDept": "123",
  "materialYield": "123",
}')
11:59:35.158 [http-nio-9021-exec-10] INFO  c.n.i.c.IngredientCalculationController - [saveOrUpdateMaterialYieldRate,217] - 接收到保存成材率的请求，DTO: MaterialYieldRatesDTO(pageNo=null, pageSize=null, id=null, device=123, productionDept=123, lineName=null, materialCategoryName=null, productCategory=null, steelClass=null, steelGrade=null, specifications=null, materialYield=123, unfixedLengthBlackSkin=null, unfixedYieldPolished=null, unfixedYieldPeeling=null, fixedYieldPolished=null, fixedYieldLathe=null, fixedYieldPeeling=null, unfixedPolishedLathe=null, unfixedPolishedPeeling=null, fixedPolishedLathe=null, fixedPolishedPeeling=null, fixedLengthRate=null, polishedRate=null, latheRate=null, peelingRate=null, burningLoss=null)
11:59:35.212 [http-nio-9021-exec-10] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
11:59:35.741 [http-nio-9021-exec-10] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@41171e94
11:59:35.742 [http-nio-9021-exec-10] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
11:59:35.750 [http-nio-9021-exec-10] DEBUG c.n.i.m.M.insert - [debug,135] - ==>  Preparing: INSERT INTO material_yield_rates ( id, device, production_dept, material_yield ) VALUES ( ?, ?, ?, ? )
11:59:35.762 [http-nio-9021-exec-10] DEBUG c.n.i.m.M.insert - [debug,135] - ==> Parameters: 1910180835177422849(Long), 123(String), 123(String), 123(String)
11:59:35.934 [http-nio-9021-exec-10] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 
### Error updating database.  Cause: org.postgresql.util.PSQLException: ERROR: null value in column "line_name" violates not-null constraint
  详细：Failing row contains (1910180835177422849, 123, null, null, null, null, null, null, 123, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 123).
### The error may exist in com/nercar/ingredient/mapper/MaterialYieldRatesMapper.java (best guess)
### The error may involve com.nercar.ingredient.mapper.MaterialYieldRatesMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO material_yield_rates  ( id, device, production_dept,       material_yield )  VALUES (  ?, ?, ?,       ?  )
### Cause: org.postgresql.util.PSQLException: ERROR: null value in column "line_name" violates not-null constraint
  详细：Failing row contains (1910180835177422849, 123, null, null, null, null, null, null, 123, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 123).
; ERROR: null value in column "line_name" violates not-null constraint
  详细：Failing row contains (1910180835177422849, 123, null, null, null, null, null, null, 123, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 123).
12:00:10.928 [http-nio-9021-exec-1] INFO  c.n.i.c.IngredientCalculationController - [saveOrUpdateMaterialYieldRate,217] - 接收到保存成材率的请求，DTO: MaterialYieldRatesDTO(pageNo=null, pageSize=null, id=null, device=123, productionDept=123, lineName=null, materialCategoryName=null, productCategory=null, steelClass=null, steelGrade=null, specifications=null, materialYield=123, unfixedLengthBlackSkin=null, unfixedYieldPolished=null, unfixedYieldPeeling=null, fixedYieldPolished=null, fixedYieldLathe=null, fixedYieldPeeling=null, unfixedPolishedLathe=null, unfixedPolishedPeeling=null, fixedPolishedLathe=null, fixedPolishedPeeling=null, fixedLengthRate=null, polishedRate=null, latheRate=null, peelingRate=null, burningLoss=null)
12:00:10.990 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.insert - [debug,135] - ==>  Preparing: INSERT INTO material_yield_rates ( id, device, production_dept, material_yield ) VALUES ( ?, ?, ?, ? )
12:00:10.990 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.insert - [debug,135] - ==> Parameters: 1910180984951824386(Long), 123(String), 123(String), 123(String)
12:00:11.045 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.insert - [debug,135] - <==    Updates: 1
12:00:11.084 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectList - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE (device = ? AND material_yield = ? AND production_dept = ?)
12:00:11.085 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectList - [debug,135] - ==> Parameters: 123(String), 123(String), 123(String)
12:00:11.165 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectList - [debug,135] - <==      Total: 1
12:00:36.442 [http-nio-9021-exec-3] INFO  c.n.i.c.IngredientCalculationController - [saveOrUpdateMaterialYieldRate,217] - 接收到保存成材率的请求，DTO: MaterialYieldRatesDTO(pageNo=null, pageSize=null, id=1910180984951824386, device=1234, productionDept=1234, lineName=null, materialCategoryName=null, productCategory=null, steelClass=null, steelGrade=null, specifications=null, materialYield=1234, unfixedLengthBlackSkin=null, unfixedYieldPolished=null, unfixedYieldPeeling=null, fixedYieldPolished=null, fixedYieldLathe=null, fixedYieldPeeling=null, unfixedPolishedLathe=null, unfixedPolishedPeeling=null, fixedPolishedLathe=null, fixedPolishedPeeling=null, fixedLengthRate=null, polishedRate=null, latheRate=null, peelingRate=null, burningLoss=null)
12:00:36.526 [http-nio-9021-exec-3] DEBUG c.n.i.m.M.updateById - [debug,135] - ==>  Preparing: UPDATE material_yield_rates SET device=?, production_dept=?, material_yield=? WHERE id=?
12:00:36.527 [http-nio-9021-exec-3] DEBUG c.n.i.m.M.updateById - [debug,135] - ==> Parameters: 1234(String), 1234(String), 1234(String), 1910180984951824386(Long)
12:00:36.648 [http-nio-9021-exec-3] DEBUG c.n.i.m.M.updateById - [debug,135] - <==    Updates: 1
12:02:16.415 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
12:02:16.419 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
14:13:57.397 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 80456 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
14:13:57.400 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
14:13:57.401 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
14:13:59.974 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:14:00.444 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
14:14:00.446 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:14:00.446 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
14:14:00.502 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:14:00.662 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
14:14:00.710 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@66234b0f, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
14:14:00.825 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
14:14:00.850 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
14:14:00.865 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
14:14:00.891 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
14:14:00.915 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
14:14:00.935 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
14:14:00.955 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
14:14:00.986 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
14:14:00.996 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
14:14:00.997 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:14:01.002 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
14:14:01.056 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
14:14:01.096 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
14:14:01.147 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
14:14:01.178 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
14:14:01.208 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
14:14:01.247 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
14:14:01.293 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
14:14:01.327 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
14:14:01.352 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
14:14:01.373 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
14:14:01.384 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
14:14:01.384 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:14:01.390 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
14:14:01.401 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
14:14:01.402 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
14:14:01.407 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
14:14:01.420 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
14:14:01.443 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
14:14:01.569 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************ network interface 
14:14:01.578 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
14:14:01.583 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:12
14:14:02.673 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
14:14:02.696 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 5.746 seconds (process running for 7.607)
14:14:02.704 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
14:14:21.645 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:14:23.621 [http-nio-9021-exec-3] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 570 ms
14:14:39.281 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
14:14:40.348 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2059b52d
14:14:40.350 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
14:14:40.358 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,steel_grades_id,path_name,purpose,note,frequence,material_yield,createuser,createtime,updatetime FROM process_path ORDER BY frequence DESC limit 2
14:14:40.369 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 
14:14:40.462 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 2
14:14:40.609 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,step_number,equipment_id,description,path_id,createtime,updatetime FROM process_path_steps WHERE (path_id = ?)
14:14:40.646 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1907250255376855041(Long)
14:14:40.701 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
14:14:40.717 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
14:14:40.717 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
14:14:40.781 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
14:14:40.782 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,step_number,equipment_id,description,path_id,createtime,updatetime FROM process_path_steps WHERE (path_id = ?)
14:14:40.783 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1907248067447603202(Long)
14:14:40.838 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 0
14:14:40.838 [http-nio-9021-exec-1] INFO  c.n.i.c.IngredientCalculationController - [recommend,91] - 工艺路径查询结果: [ProcessPathVO(id=1907250255376855041, steelGrade=null, steelGradesId=null, pathName=测试名字1, purpose=测试目的1, note=测试备注1, frequence=null, materialYield=null, createuser=null, createtime=null, updatetime=null, steps=[PathSaveDTO(stepNumber=1, equipmentId=1, equipmentName=炉子1, imageUrl=炉子1)]), ProcessPathVO(id=1907248067447603202, steelGrade=null, steelGradesId=null, pathName=测试名字, purpose=测试目的, note=测试备注, frequence=null, materialYield=null, createuser=null, createtime=null, updatetime=null, steps=[])]
14:16:03.459 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,steel_grades_id,path_name,purpose,note,frequence,material_yield,createuser,createtime,updatetime FROM process_path ORDER BY frequence DESC limit 2
14:16:03.459 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 
14:16:03.515 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 2
14:16:03.852 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,step_number,equipment_id,description,path_id,createtime,updatetime FROM process_path_steps WHERE (path_id = ?)
14:16:03.852 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
14:16:03.909 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 4
14:16:03.910 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
14:16:03.910 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
14:16:03.964 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
14:16:03.965 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
14:16:03.965 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 2(Long)
14:16:04.022 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
14:16:04.023 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
14:16:04.024 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 3(Long)
14:16:04.079 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
14:16:04.080 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,equipment_name,type,images_url,department_id,createuser,createtime,updatetime FROM production_equipments WHERE (id = ?)
14:16:04.081 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 4(Long)
14:16:04.136 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 1
14:16:04.139 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,step_number,equipment_id,description,path_id,createtime,updatetime FROM process_path_steps WHERE (path_id = ?)
14:16:04.139 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 5(Long)
14:16:04.195 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 0
14:16:04.195 [http-nio-9021-exec-3] INFO  c.n.i.c.IngredientCalculationController - [recommend,91] - 工艺路径查询结果: [ProcessPathVO(id=1, steelGrade=null, steelGradesId=1, pathName=热轧工艺路径, purpose=生产热轧板材, note=常规热轧流程, frequence=100, materialYield=90, createuser=张三, createtime=Mon Jan 01 08:00:00 CST 2024, updatetime=Mon Jan 01 08:00:00 CST 2024, steps=[PathSaveDTO(stepNumber=1, equipmentId=1, equipmentName=炉子1, imageUrl=炉子1), PathSaveDTO(stepNumber=2, equipmentId=2, equipmentName=炉子2, imageUrl=炉子1), PathSaveDTO(stepNumber=3, equipmentId=3, equipmentName=炉子3, imageUrl=炉子1), PathSaveDTO(stepNumber=4, equipmentId=4, equipmentName=炉子4, imageUrl=炉子1)]), ProcessPathVO(id=5, steelGrade=null, steelGradesId=null, pathName=焊接工艺路径, purpose=连接金属部件, note=常用焊接流程, frequence=90, materialYield=87, createuser=孙七, createtime=Fri Jan 05 12:00:00 CST 2024, updatetime=Fri Jan 05 12:00:00 CST 2024, steps=[])]
14:26:39.678 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
14:26:39.699 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
16:18:50.123 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 70068 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
16:18:50.127 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
16:18:50.129 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
16:18:51.642 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
16:18:52.119 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
16:18:52.120 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:18:52.121 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
16:18:52.184 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:18:52.363 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
16:18:52.412 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@5c5a91b4, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
16:18:52.541 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
16:18:52.581 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
16:18:52.619 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
16:18:52.649 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
16:18:52.675 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
16:18:52.694 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
16:18:52.712 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
16:18:52.735 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
16:18:52.739 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
16:18:52.740 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
16:18:52.745 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
16:18:52.781 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
16:18:52.799 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
16:18:52.819 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
16:18:52.840 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
16:18:52.856 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
16:18:52.877 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
16:18:52.898 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
16:18:52.915 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
16:18:52.926 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
16:18:52.936 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
16:18:52.939 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
16:18:52.939 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
16:18:52.947 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
16:18:52.952 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
16:18:52.953 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
16:18:52.960 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
16:18:52.970 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
16:18:52.982 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
16:18:53.118 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
16:18:53.127 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
16:18:53.132 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:8
16:18:55.042 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
16:18:55.069 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 5.601 seconds (process running for 6.55)
16:18:55.078 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
16:18:58.324 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:19:00.185 [http-nio-9021-exec-4] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 954 ms
16:19:15.647 [http-nio-9021-exec-6] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- JSON parse error: Unrecognized field "standardName" (class com.nercar.ingredient.domain.dto.SteelGradesDTO), not marked as ignorable
16:47:13.569 [http-nio-9021-exec-4] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- JSON parse error: Cannot deserialize value of type `java.util.ArrayList<com.nercar.ingredient.domain.dto.StandardRawMaterialsDTO>` from String value (token `JsonToken.VALUE_STRING`)
16:48:15.417 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 81340 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
16:48:15.420 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
16:48:15.421 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
16:48:16.346 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
16:48:16.808 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
16:48:16.810 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:48:16.810 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
16:48:16.870 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:48:17.045 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
16:48:17.088 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@2fba0dac, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
16:48:17.237 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
16:48:17.283 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
16:48:17.316 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
16:48:17.336 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
16:48:17.354 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
16:48:17.367 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
16:48:17.382 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
16:48:17.404 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
16:48:17.406 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
16:48:17.407 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
16:48:17.412 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
16:48:17.446 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
16:48:17.462 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
16:48:17.479 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
16:48:17.493 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
16:48:17.511 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
16:48:17.531 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
16:48:17.560 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
16:48:17.573 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
16:48:17.586 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
16:48:17.596 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
16:48:17.599 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
16:48:17.600 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
16:48:17.605 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
16:48:17.608 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
16:48:17.608 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
16:48:17.613 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
16:48:17.622 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
16:48:17.633 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
16:48:17.769 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
16:48:17.778 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
16:48:17.783 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:5
16:48:19.010 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
16:48:19.037 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.042 seconds (process running for 5.025)
16:48:19.045 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
16:48:28.139 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:48:28.253 [http-nio-9021-exec-1] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- JSON parse error: Cannot deserialize value of type `java.util.ArrayList<com.nercar.ingredient.domain.dto.StandardRawMaterialsDTO>` from String value (token `JsonToken.VALUE_STRING`)
16:50:22.308 [http-nio-9021-exec-3] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
16:50:23.358 [http-nio-9021-exec-3] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6b905927
16:50:23.360 [http-nio-9021-exec-3] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
16:50:23.386 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials
16:50:23.400 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 
16:50:23.497 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 23
16:57:55.840 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
16:57:55.842 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
17:17:34.262 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 82452 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
17:17:34.264 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
17:17:34.265 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
17:17:35.353 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
17:17:35.847 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
17:17:35.849 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:17:35.850 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
17:17:35.912 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:17:36.102 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
17:17:36.140 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@56da96b3, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
17:17:36.249 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
17:17:36.275 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
17:17:36.293 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
17:17:36.311 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
17:17:36.332 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
17:17:36.348 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
17:17:36.364 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
17:17:36.391 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
17:17:36.395 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
17:17:36.395 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:17:36.400 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
17:17:36.433 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
17:17:36.445 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
17:17:36.458 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
17:17:36.469 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
17:17:36.479 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
17:17:36.490 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
17:17:36.510 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
17:17:36.523 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
17:17:36.534 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
17:17:36.545 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
17:17:36.549 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
17:17:36.550 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:17:36.554 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
17:17:36.558 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
17:17:36.558 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
17:17:36.563 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
17:17:36.573 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
17:17:36.585 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
17:17:36.710 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
17:17:36.718 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
17:17:36.723 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:6
17:17:37.924 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
17:17:37.969 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.057 seconds (process running for 4.937)
17:17:37.976 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
17:17:41.975 [http-nio-9021-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:17:43.106 [http-nio-9021-exec-4] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 689 ms
17:17:54.310 [http-nio-9021-exec-6] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
17:17:55.084 [http-nio-9021-exec-6] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@10864b1f
17:17:55.085 [http-nio-9021-exec-6] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
17:17:55.092 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials
17:17:55.106 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 
17:17:55.219 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 23
17:17:56.468 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE (category = ?)
17:17:56.469 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 燃料类(String)
17:17:56.536 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
17:17:56.537 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE (category = ?)
17:17:56.538 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 金属类(String)
17:17:56.617 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 3
17:17:56.617 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE (category = ?)
17:17:56.618 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 熔剂类(String)
17:17:56.686 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 2
17:17:56.686 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE (category = ?)
17:17:56.687 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 铁合金类(String)
17:17:56.764 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 8
17:17:56.765 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE (category = ?)
17:17:56.765 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 矿石类(String)
17:17:56.842 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 8
17:17:56.844 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE (category = ?)
17:17:56.844 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 333(String)
17:17:57.487 [http-nio-9021-exec-6] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
17:18:22.043 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
17:18:22.048 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
23:41:13.646 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 95224 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
23:41:13.673 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
23:41:13.674 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
23:41:18.112 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
23:41:19.458 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
23:41:19.459 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:41:19.460 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
23:41:19.602 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:41:20.082 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
23:41:20.160 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@2fca3eb5, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
23:41:20.568 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
23:41:20.599 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
23:41:20.621 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
23:41:20.661 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
23:41:20.679 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
23:41:20.707 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
23:41:20.721 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
23:41:20.757 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
23:41:20.761 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
23:41:20.761 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
23:41:20.766 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
23:41:20.830 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
23:41:20.844 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
23:41:20.881 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
23:41:20.897 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
23:41:20.920 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
23:41:20.933 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
23:41:20.951 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
23:41:20.969 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
23:41:20.980 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
23:41:20.995 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
23:41:20.998 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
23:41:20.999 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
23:41:21.004 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
23:41:21.007 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
23:41:21.007 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
23:41:21.012 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
23:41:21.028 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
23:41:21.048 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
23:41:21.194 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
23:41:21.203 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
23:41:21.210 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:7
23:41:23.637 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
23:41:23.680 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 11.271 seconds (process running for 14.399)
23:41:23.690 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
23:41:36.328 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:41:38.386 [http-nio-9021-exec-9] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 938 ms
23:49:12.125 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 91948 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
23:49:12.128 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
23:49:12.128 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
23:49:13.651 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
23:49:14.158 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
23:49:14.159 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:49:14.160 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
23:49:14.217 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:49:14.390 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
23:49:14.439 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@59ec7020, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
23:49:14.542 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
23:49:14.565 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
23:49:14.579 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
23:49:14.598 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
23:49:14.618 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
23:49:14.634 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
23:49:14.648 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
23:49:14.669 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
23:49:14.677 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
23:49:14.678 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
23:49:14.684 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
23:49:14.719 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
23:49:14.734 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
23:49:14.753 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
23:49:14.767 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
23:49:14.782 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
23:49:14.796 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
23:49:14.821 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
23:49:14.839 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
23:49:14.853 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
23:49:14.869 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
23:49:14.874 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
23:49:14.874 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
23:49:14.882 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
23:49:14.886 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
23:49:14.886 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
23:49:14.895 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
23:49:14.910 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
23:49:14.924 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
23:49:15.056 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
23:49:15.065 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
23:49:15.073 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:20
23:49:16.552 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
23:49:16.590 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.965 seconds (process running for 6.398)
23:49:16.600 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
23:49:52.827 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 91900 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
23:49:52.829 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
23:49:52.830 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
23:49:53.881 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
23:49:54.470 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
23:49:54.472 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:49:54.473 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
23:49:54.541 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:49:54.745 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
23:49:54.781 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@2cbe455c, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
23:49:54.887 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
23:49:54.913 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
23:49:54.931 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
23:49:54.947 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
23:49:54.967 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
23:49:54.984 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
23:49:54.998 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
23:49:55.019 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
23:49:55.028 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
23:49:55.029 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
23:49:55.035 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
23:49:55.073 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
23:49:55.085 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
23:49:55.099 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
23:49:55.112 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
23:49:55.122 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
23:49:55.132 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
23:49:55.147 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
23:49:55.163 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
23:49:55.180 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
23:49:55.195 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
23:49:55.200 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
23:49:55.202 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
23:49:55.213 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
23:49:55.218 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
23:49:55.219 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
23:49:55.229 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
23:49:55.250 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
23:49:55.273 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
23:49:55.446 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
23:49:55.459 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
23:49:55.467 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:16
23:49:57.494 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
23:49:57.523 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 6.044 seconds (process running for 6.918)
23:49:57.532 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
23:50:31.258 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:50:32.555 [http-nio-9021-exec-4] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 1018 ms
23:52:35.827 [http-nio-9021-exec-7] INFO  c.n.i.c.IngredientCalculationController - [saveStandardCompositions,140] - 接收到保存标准成分的请求，DTO: StandardCompositionsQueryDTO(standardName=自定义标准名称A, elementName=, minValue=0, maxValue=0, averageValue=0, code=, isCustomize=, standardCompositionsList=[StandardCompositionsDTO(id=null, elementName=C, minValue=1, maxValue=2, standardId=null, isCustomize=否, createuser=, createtime=null, updatetime=null), StandardCompositionsDTO(id=null, elementName=P, minValue=1, maxValue=2, standardId=null, isCustomize=否, createuser=, createtime=null, updatetime=null)])
23:52:35.866 [http-nio-9021-exec-7] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
23:52:36.619 [http-nio-9021-exec-7] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@64127b7c
23:52:36.621 [http-nio-9021-exec-7] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
23:52:36.794 [http-nio-9021-exec-7] DEBUG c.n.i.m.E.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_name,createuser,createtime,updatetime FROM execution_standard WHERE (standard_name = ?)
23:52:36.820 [http-nio-9021-exec-7] DEBUG c.n.i.m.E.selectList - [debug,135] - ==> Parameters: 自定义标准名称A(String)
23:52:36.887 [http-nio-9021-exec-7] DEBUG c.n.i.m.E.selectList - [debug,135] - <==      Total: 0
23:52:36.896 [http-nio-9021-exec-7] DEBUG c.n.i.m.E.insert - [debug,135] - ==>  Preparing: INSERT INTO execution_standard ( id, standard_name, createuser, createtime, updatetime ) VALUES ( ?, ?, ?, ?, ? )
23:52:36.909 [http-nio-9021-exec-7] DEBUG c.n.i.m.E.insert - [debug,135] - ==> Parameters: 1910360274578374658(Long), 自定义标准名称A(String), null, 2025-04-10T23:52:36.895641500(LocalDateTime), 2025-04-10T23:52:36.896639900(LocalDateTime)
23:52:36.980 [http-nio-9021-exec-7] DEBUG c.n.i.m.E.insert - [debug,135] - <==    Updates: 1
23:52:36.981 [http-nio-9021-exec-7] DEBUG c.n.i.m.E.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_name,createuser,createtime,updatetime FROM execution_standard WHERE (standard_name = ?)
23:52:36.981 [http-nio-9021-exec-7] DEBUG c.n.i.m.E.selectList - [debug,135] - ==> Parameters: 自定义标准名称A(String)
23:52:37.033 [http-nio-9021-exec-7] DEBUG c.n.i.m.E.selectList - [debug,135] - <==      Total: 1
23:52:37.158 [http-nio-9021-exec-7] DEBUG c.n.i.m.S.insert - [debug,135] - ==>  Preparing: INSERT INTO standard_compositions ( id, element_name, min_value, max_value, is_customize, createuser, createtime, updatetime ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
23:52:37.159 [http-nio-9021-exec-7] DEBUG c.n.i.m.S.insert - [debug,135] - ==> Parameters: 1910360275647922177(Long), C(String), 1(BigDecimal), 2(BigDecimal), 否(String), (String), 2025-04-10T23:52:37.158120700(LocalDateTime), 2025-04-10T23:52:37.158120700(LocalDateTime)
23:52:37.229 [http-nio-9021-exec-7] DEBUG c.n.i.m.S.insert - [debug,135] - <==    Updates: 1
23:52:37.230 [http-nio-9021-exec-7] DEBUG c.n.i.m.S.insert - [debug,135] - ==>  Preparing: INSERT INTO standard_compositions ( id, element_name, min_value, max_value, is_customize, createuser, createtime, updatetime ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
23:52:37.231 [http-nio-9021-exec-7] DEBUG c.n.i.m.S.insert - [debug,135] - ==> Parameters: 1910360275962494978(Long), P(String), 1(BigDecimal), 2(BigDecimal), 否(String), (String), 2025-04-10T23:52:37.230621200(LocalDateTime), 2025-04-10T23:52:37.230621200(LocalDateTime)
23:52:37.284 [http-nio-9021-exec-7] DEBUG c.n.i.m.S.insert - [debug,135] - <==    Updates: 1
23:56:12.894 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
23:56:12.897 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
