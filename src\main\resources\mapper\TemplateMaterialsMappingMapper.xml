<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.TemplateMaterialsMappingMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.TemplateMaterialsMapping">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="standardTemplateId" column="standard_template_id" jdbcType="BIGINT"/>
            <result property="rawMaterialId" column="raw_material_id" jdbcType="BIGINT"/>
            <result property="createuser" column="createuser" jdbcType="VARCHAR"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,standard_template_id,raw_material_id,
        createuser,createtime,updatetime
    </sql>
</mapper>
