package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 标准配料记录表
 * @TableName standard_ingredient_records
 */
@TableName(value ="standard_ingredient_records")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StandardIngredientRecords extends BaseEntity {
    /**
     * 主键
     */
    @TableId
    private Long id;

    private Integer status;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 钢种牌号ID
     */
    private Long steelGradeId;

    /**
     * 计算流程编号
     */
    private String calculationProcessNo;

    /**
     * 执行标准ID
     */
    private Long executionStandardId;

    /**
     * 原材料总量
     */
    private BigDecimal rawMaterialTotal;

    /**
     * 总成本
     */
    private BigDecimal costPrice;

    /**
     * 配料日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime mixingDate;

    /**
     * 发布日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime releaseDate;

    /**
     * 类别;标准配料、优化配料
     */
    private String category;

    /**
     * 计算结果ID
     */
    private Long calculationResultId;

    /**
     * 工艺路径ID
     */
    private Long processPathId;

    /**
     * 特殊说明
     */
    private String specialNotes;

    /**
     * 成本测算ID;当时从车本测算发起时，填写此信息
     */
    private Long costEstimattionId;

    /**
     * 配料人
     */
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    private String createuser;

    /**
     *
     */
    @TableField(value = "createtime",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     *
     */
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;

    /**
     * 金属料吨水单耗配置ID
     */
    private Long metalMaterialWaterConsumptionConfigId;

    /**
     * 金属料吨水单耗值
     */
    private Integer metalMaterialWaterConsumption;

}