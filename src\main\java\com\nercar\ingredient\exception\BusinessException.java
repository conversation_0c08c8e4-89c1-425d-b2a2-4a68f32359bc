package com.nercar.ingredient.exception;

import com.nercar.ingredient.response.IErrorCode;

/**
 * 业务异常
 */
public class BusinessException extends RuntimeException implements IErrorCode {
    private final IErrorCode errorCode;
    private String message;

    public BusinessException(IErrorCode errorCode, String message) {
        this.errorCode = errorCode;
        this.message = message;
    }

    public BusinessException(IErrorCode errorCode) {
        this.errorCode = errorCode;
        this.message = errorCode.getMessage();
    }

    @Override
    public int getCode() {
        return this.errorCode.getCode();
    }

    @Override
    public IErrorCode setMessage(String msg) {
        this.message = msg;
        return this;
    }

    @Override
    public String getMessage() {
        return this.message;
    }
}
