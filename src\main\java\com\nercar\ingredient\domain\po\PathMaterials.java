package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 工艺路径材料关联表
 * @TableName path_materials
 */
@TableName(value ="path_materials")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PathMaterials extends BaseEntity {
    /**
     * 路径ID
     */

    private Long pathId;

    /**
     * 材料ID
     */

    private Long rawMaterialId;



}