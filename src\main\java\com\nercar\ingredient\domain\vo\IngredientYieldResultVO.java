package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 配料单成材率
 * @TableName ingredient_yield_result
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IngredientYieldResultVO extends BaseVO {
    /**
     * 主键
     */
    @TableId
    @Schema(description = "主键")
    private Long id;

    /**
     * 配料单ID
     */
    @Schema(description = "配料单ID")
    private Long standardIngredientRecordId;

    /**
     * 成材率ID
     */
    @Schema(description = "成材率ID")
    private Long yieldId;

    /**
     * 生产车间
     */
    @Schema(description = "生产车间")
    private String productionDept;

    /**
     * 作业线
     */
    @Schema(description = "作业线")
    private String lineName;

    /**
     * 成材率
     */
    @Schema(description = "成材率")
    private String materialYield;



}