package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 成本测算
 * @TableName cost_estimation
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CostEstimationVO extends BaseVO {
    /**
     * 主键
     */
    @TableId
    @Schema(description = "主键")
    private Integer id;

    /**
     * 评审编号
     */
    @Schema(description = "评审编号")
    private String estimationNo;

    /**
     * 用户名称
     */
    @Schema(description = "用户名称")
    private String username;

    /**
     * 所属分公司ID
     */
    @Schema(description = "所属分公司ID")
    private Integer companyId;

    /**
     * 钢号ID
     */
    @Schema(description = "钢号ID")
    private Integer steelNumberId;

    /**
     * 成品规格
     */
    @Schema(description = "成品规格")
    private String finishedProductSpecification;

    /**
     * 预计订货量
     */
    @Schema(description = "预计订货量")
    private String estimatedOrderQuantity;

    /**
     * 报价或测算成本
     */
    @Schema(description = "报价或测算成本")
    private String quotationOrCostEstimation;

    /**
     * 是否锻材
     */
    @Schema(description = "是否锻材")
    private String forgedMaterial;

    /**
     * 是否修改标准成本
     */
    @Schema(description = "是否修改标准成本")
    private String changeStandardCost;

    /**
     * 长度交货状态ID
     */
    @Schema(description = "长度交货状态ID")
    private Integer lengthDeliveryStatusId;

    /**
     * 表面交货状态ID
     */
    @Schema(description = "表面交货状态ID")
    private Integer surfaceDeliveryStatusId;

    /**
     * 热处理交货状态ID
     */
    @Schema(description = "热处理交货状态ID")
    private Integer heatDeliveryStatusId;

    /**
     * 技术标准
     */
    @Schema(description = "技术标准")
    private String technicalStandard;

    /**
     * 工艺路线
     */
    @Schema(description = "工艺路线")
    private String processRoute;

    /**
     * 审核状态
     */
    @Schema(description = "审核状态")
    private String approveStatus;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createdTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updatedBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updatedTime;




}