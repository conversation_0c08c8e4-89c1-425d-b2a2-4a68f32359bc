-- 历史配料查询优化索引脚本
-- 执行时间：2025-01-14

-- 1. 检查现有索引
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'standard_ingredient_records'
ORDER BY indexname;

-- 2. 为process_path_id字段创建索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_standard_ingredient_records_process_path_id 
ON standard_ingredient_records(process_path_id);

-- 3. 为steel_grade_id字段创建索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_standard_ingredient_records_steel_grade_id 
ON standard_ingredient_records(steel_grade_id);

-- 4. 创建复合索引，优化钢种+工艺路径的联合查询
CREATE INDEX IF NOT EXISTS idx_standard_ingredient_records_steel_process 
ON standard_ingredient_records(steel_grade_id, process_path_id);

-- 5. 为status字段创建索引（用于过滤历史配料）
CREATE INDEX IF NOT EXISTS idx_standard_ingredient_records_status 
ON standard_ingredient_records(status);

-- 6. 创建复合索引，优化完整查询条件
CREATE INDEX IF NOT EXISTS idx_standard_ingredient_records_query_optimize 
ON standard_ingredient_records(status, steel_grade_id, process_path_id, mixing_date DESC);

-- 7. 验证索引创建结果
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'standard_ingredient_records'
AND indexname LIKE 'idx_standard_ingredient_records%'
ORDER BY indexname;

-- 8. 分析表统计信息，优化查询计划
ANALYZE standard_ingredient_records;

-- 9. 查看索引使用情况统计
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename = 'standard_ingredient_records'
ORDER BY idx_tup_read DESC;

-- 10. 测试查询性能（示例）
EXPLAIN (ANALYZE, BUFFERS) 
SELECT 
    air.id, air.status, air.user_name, air.department_id,
    air.steel_grade_id, air.process_path_id, air.mixing_date,
    sg.steel_grade, es.standard_name
FROM standard_ingredient_records air
LEFT JOIN steel_grades sg ON air.steel_grade_id = sg.id
LEFT JOIN execution_standard es ON air.execution_standard_id = es.id
WHERE air.status = 1 
  AND air.steel_grade_id = 16 
  AND air.process_path_id = 1917153489721815042
ORDER BY air.mixing_date DESC
LIMIT 20;

-- 索引创建完成提示
SELECT '历史配料查询索引优化完成' as status;
