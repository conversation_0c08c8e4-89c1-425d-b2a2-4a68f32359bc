package com.nercar.ingredient.interceptor;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.stp.StpUtil;
import com.nercar.ingredient.constant.SecurityConstants;
import com.nercar.ingredient.domain.bo.CurrentUser;
import com.nercar.ingredient.domain.po.Departments;
import com.nercar.ingredient.domain.po.Users;
import com.nercar.ingredient.mapper.DepartmentsMapper;
import com.nercar.ingredient.mapper.UsersMapper;
import com.nercar.ingredient.security.UserContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

@Slf4j
@Component
public class TokenInterceptor implements HandlerInterceptor {
    @Autowired
    private UsersMapper usersMapper;

    @Autowired
    private DepartmentsMapper departmentsMapper;
    
    @Autowired
    private ObjectMapper objectMapper;

    @Value("${smart-desktop.token-prefix}")
    private String smartDesktopTokenPrefix;

    @Value("${smart-desktop.auth-url}")
    private String smartDesktopAuthUrl;

    private static final HttpClient httpClient = HttpClient.newBuilder()
            .version(HttpClient.Version.HTTP_2)
            .connectTimeout(Duration.ofSeconds(10))
            .build();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String url = request.getRequestURL().toString();
        String method = request.getMethod();

        // 检查白名单
        for (String pattern : SecurityConstants.WHITE_LIST) {
            if (url.contains(pattern)) {
                log.info("白名单请求放行 - Method: {}, URL: {}", method, url);
                return true;
            }
        }

        String token = request.getHeader(HttpHeaders.AUTHORIZATION);
        if (token == null) {
            handleUnauthorized(response, "未提供认证token");
            return false;
        }

        try {
            String username;
            // 处理智慧桌面的token
            if (token.startsWith(smartDesktopTokenPrefix)) {
                log.info("检测到智慧桌面token: {}", token.substring(0, Math.min(token.length(), 20)) + "...");
                String newToken = token.replace(smartDesktopTokenPrefix, "Bearer ");
                log.info("转换后的token: {}", newToken.substring(0, Math.min(newToken.length(), 20)) + "...");
                
                log.info("开始调用智慧桌面API验证token, URL: {}", smartDesktopAuthUrl);
                username = callSmartDesktopApi(newToken);
                
                if (username == null) {
                    log.error("智慧桌面认证失败，API返回的username为null");
                    handleUnauthorized(response, "智慧桌面认证失败");
                    return false;
                }
                log.info("智慧桌面认证成功，获取到用户名: {}", username);
                
                // 获取或创建用户
                log.info("开始查询用户信息: {}", username);
                Users user = getUserByUsername(username);
                log.info("成功获取用户信息: ID={}, 用户名={}, 部门ID={}", 
                        user.getId(), user.getUserName(), user.getDepartmentId());
                
                setUserContext(user);
                log.info("用户上下文设置完成");
            } else {
                // 原有的Sa-Token认证逻辑
                StpUtil.checkLogin();
                Long userId = StpUtil.getLoginIdAsLong();
                //获得用户userName
                Object userName = StpUtil.getSession().get("userName");
                LambdaQueryWrapper<Users> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Users::getUserName, userName);
                List<Users> usersList = usersMapper.selectList(queryWrapper);
                if (usersList.isEmpty()) {
                    throw new RuntimeException("用户不存在");
                }
                Users user = usersList.get(0); // 取第一条记录
                setUserContext(user);
            }
            return true;
        } catch (NotLoginException e) {
            handleUnauthorized(response, "未登录或token已过期");
            return false;
        }
    }

    private void setUserContext(Users user) {
        Departments dept = departmentsMapper.selectById(user.getDepartmentId());
        UserContext.setCurrentUser(new CurrentUser(
                user.getId().toString(),
                user.getUserName(),
                StpUtil.getTokenValue(),
                dept != null ? dept.getDepartmentName() : "",
                ""
        ));
    }

    private void handleUnauthorized(HttpServletResponse response, String message) throws IOException {
        response.setStatus(401);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("{\"code\":401,\"msg\":\"" + message + "\"}");
    }

    private String callSmartDesktopApi(String token) {
        var request = java.net.http.HttpRequest.newBuilder()
                .uri(URI.create(smartDesktopAuthUrl))
                .header("Content-Type", "application/json")
                .header(HttpHeaders.AUTHORIZATION, token)
                .POST(java.net.http.HttpRequest.BodyPublishers.ofString("{\"empty\":true}"))
                .build();

        try {
            log.info("发送请求到智慧桌面API: {}", smartDesktopAuthUrl);
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            log.info("智慧桌面API响应状态码: {}", response.statusCode());
            
            if (response.statusCode() != 200) {
                log.error("智慧桌面API返回非200状态码: {}, 响应体: {}", 
                        response.statusCode(), response.body());
                return null;
            }
            
            log.info("智慧桌面API响应体: {}", response.body());
            JsonNode root = objectMapper.readTree(response.body());
            
            if (root.path("success").asBoolean()) {
                String userNo = root.get("data").get("userNo").asText();
                log.info("成功从API响应中提取用户编号: {}", userNo);
                return userNo;
            } else {
                log.error("API响应success=false, 错误信息: {}", 
                        root.has("message") ? root.get("message").asText() : "未知错误");
            }
        } catch (Exception e) {
            log.error("调用智慧桌面API失败", e);
            // 打印更详细的异常信息
            StringWriter sw = new StringWriter();
            e.printStackTrace(new PrintWriter(sw));
            log.error("详细异常堆栈: {}", sw.toString());
        }
        return null;
    }

    private Users getUserByUsername(String username) {
        LambdaQueryWrapper<Users> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Users::getUserName, username);
        List<Users> usersList = usersMapper.selectList(queryWrapper);
        if (usersList.isEmpty()) {
            // 如果用户不存在，可以选择创建新用户或抛出异常
            throw new RuntimeException("用户不存在：" + username);
        }
        return usersList.get(0); // 取第一条记录
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        UserContext.clear();
    }
}