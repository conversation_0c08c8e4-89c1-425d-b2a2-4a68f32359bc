package com.nercar.ingredient.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SubmitResVO extends BaseVO{

    /**
     * 锭率ID集合
     */
    @Schema(description = "锭率ID集合")
    private List<String> ingotYieldRatesIds;

    /**
     * 成材率ID集合
     */
    @Schema(description = "成材率ID集合")
    private List<String> materialYieldRatesIds;
}
