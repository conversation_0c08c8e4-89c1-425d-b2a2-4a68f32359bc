package com.nercar.ingredient.security;


import com.nercar.ingredient.domain.bo.CurrentUser;

import java.util.concurrent.ConcurrentHashMap;

public class UserContext {

    public static ConcurrentHashMap<String,String> currentHashMap = new ConcurrentHashMap<>();

    private static final ThreadLocal<CurrentUser> USER_CONTEXT = new ThreadLocal<>();

    public static CurrentUser getCurrentUser(){
        return USER_CONTEXT.get();
    }

    public static void setCurrentUser(CurrentUser currentUser){
        USER_CONTEXT.set(currentUser);
    }

    public static String getUserId(){
        return getCurrentUser().getUserId();
    }

    public static void clear(){
        USER_CONTEXT.remove();
    }
}
