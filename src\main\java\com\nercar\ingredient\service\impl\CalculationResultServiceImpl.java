package com.nercar.ingredient.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nercar.ingredient.domain.bo.*;
import com.nercar.ingredient.domain.dto.CalculationQueryDataDTO;
import com.nercar.ingredient.domain.dto.NewAlgorithmRequest;
import com.nercar.ingredient.domain.dto.PathSaveDTO;
import com.nercar.ingredient.domain.dto.PurposeCompositionsQueryDTO;
import com.nercar.ingredient.domain.po.*;
import com.nercar.ingredient.domain.vo.CalculationResultDataVO;
import com.nercar.ingredient.domain.vo.HttpResVo2;
import com.nercar.ingredient.domain.vo.NewAlgorithmResponse;
import com.nercar.ingredient.domain.vo.ProcessPathVO;
import com.nercar.ingredient.mapper.*;
import com.nercar.ingredient.security.UserContext;
import com.nercar.ingredient.service.AlgorithmDataTransformService;
import com.nercar.ingredient.service.CalculationResultService;
import com.nercar.ingredient.service.ProcessPathStepsService;
import com.nercar.ingredient.service.PurposeCompositionsService;
import io.netty.channel.ChannelOption;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

import java.math.BigDecimal;
import java.net.ConnectException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【calculation_result(计算结果表)】的数据库操作Service实现
* @createDate 2025-04-01 13:55:28
*/
@Service
@Slf4j
public class CalculationResultServiceImpl extends ServiceImpl<CalculationResultMapper, CalculationResult>
    implements CalculationResultService{

    @Autowired
    private PathMaterialsMapper pathMaterialsMapper;
    @Autowired
    private CostEstimationMapper costEstimationMapper;
    @Autowired
    private SteelGradesMapper steelGradesMapper;
    @Autowired
    private ExecutionStandardMapper executionStandardMapper;
    @Autowired
    private StandardIngredientRecordsMapper standardIngredientRecordsMapper;
    @Autowired
    private PurposeCompositionsMapper purposeCompositionsMapper;
    @Autowired
    private StandardRawMaterialsMapper standardRawMaterialsMapper;
    @Autowired
    private CalculationResultMapper calculationResultMapper;
    @Autowired
    private ProcessPathStepsService processPathStepsService;
    @Autowired
    private PurposeCompositionsService purposeCompositionsService;
    @Autowired
    private WebClient.Builder webClientBuilder;
    @Autowired
    private SteelGradeStandardMappingMapper steelGradeStandardMappingMapper;
    @Autowired
    private DepartmentsMapper departmentsMapper;

    @Autowired
    private AlgorithmDataTransformService algorithmDataTransformService;

    @Value("${remote-service.calculation.base-url}")
    private String calculationBaseUrl;

    @Value("${remote-service.calculation.endpoints.calculate}")
    private String calculateEndpoint;

    @Value("${calculation.use-new-algorithm:true}")
    private boolean useNewAlgorithm;

    @Value("${calculation.new-algorithm.base-url:http://49.4.23.163:1998}")
    private String newAlgorithmBaseUrl;

    @Value("${calculation.new-algorithm.endpoint:/cacluate}")
    private String newAlgorithmEndpoint;

    /**
     * 构建草稿保存响应
     * @param standardIngredientRecordId 主表记录ID
     * @param calculationQueryDataDTO 计算参数
     * @return 草稿保存结果
     */
    private CalculationResultDataVO buildDraftResponse(Long standardIngredientRecordId, CalculationQueryDataDTO calculationQueryDataDTO) {
        log.info("构建草稿保存响应，主表ID: {}", standardIngredientRecordId);

        CalculationResultDataVO draftResponse = new CalculationResultDataVO();
        draftResponse.setStandardIngredientRecordId(standardIngredientRecordId);

        // 设置金属料吨水单耗相关信息（如果有）
        if (calculationQueryDataDTO.getMetalMaterialWaterConsumptionConfigId() != null) {
            draftResponse.setMetalMaterialWaterConsumptionConfigId(calculationQueryDataDTO.getMetalMaterialWaterConsumptionConfigId());
        }
        if (calculationQueryDataDTO.getMetalMaterialWaterConsumption() != null) {
            draftResponse.setMetalMaterialWaterConsumption(calculationQueryDataDTO.getMetalMaterialWaterConsumption());
        }

        log.info("草稿保存完成，主表ID: {}, 计算ID: {}",
                 standardIngredientRecordId, calculationQueryDataDTO.getCalculationId());

        return draftResponse;
    }

    /**
     * 处理目标成分数据保存
     * @param calculationQueryDataDTO 计算参数
     * @return 目标成分ID列表
     */
    private List<String> handlePurposeCompositionsData(CalculationQueryDataDTO calculationQueryDataDTO) {
        if (calculationQueryDataDTO.hasPurposeCompositionsData()) {
            // 如果传入了目标成分数据，先保存目标成分
            PurposeCompositionsQueryDTO purposeData = calculationQueryDataDTO.getPurposeCompositionsData();

            // 设置标准配料记录ID（如果已有主表ID）
            if (calculationQueryDataDTO.getStandardIngredientRecordsId() != null) {
                purposeData.setStandardIngredientRecordId(calculationQueryDataDTO.getStandardIngredientRecordsId());
            }

            log.info("开始保存目标成分数据，元素数量: {}",
                     purposeData.getElementList() != null ? purposeData.getElementList().size() : 0);

            List<String> savedPurposeCompositionIds = purposeCompositionsService.updatePurposeCompositions(purposeData);

            log.info("目标成分保存完成，生成ID数量: {}", savedPurposeCompositionIds.size());

            return savedPurposeCompositionIds;
        } else {
            // 使用传入的目标成分ID列表（向后兼容）
            return calculationQueryDataDTO.getPurposeCompositionsIds();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CalculationResultDataVO calculation(CalculationQueryDataDTO calculationQueryDataDTO) {
        //拿到的参数是钢种名字，标准名字，路径id，目标成分id集合，原料id集合，特殊要求，还有一个status,主表id
        if(calculationQueryDataDTO.getSteelGrade()==null){
            throw new RuntimeException("钢种名字不能为空");
        }
        if(calculationQueryDataDTO.getStandardName()==null){
            throw new RuntimeException("标准名字不能为空");
        }
        if(calculationQueryDataDTO.getProcessPathId()==null){
            throw new RuntimeException("路径ID不能为空");
        }
        // 修改参数验证：支持目标成分ID或目标成分数据二选一
        List<String> purposeIds = calculationQueryDataDTO.getPurposeCompositionsIds();
        boolean hasValidIds = purposeIds != null && !purposeIds.isEmpty();
        boolean hasValidData = calculationQueryDataDTO.hasPurposeCompositionsData();

        if (!hasValidIds && !hasValidData) {
            throw new RuntimeException("目标成分ID集合或目标成分数据不能为空");
        }

        log.info("参数验证通过 - 有效ID: {}, 有效数据: {}", hasValidIds, hasValidData);
        List<String> effectiveIds = calculationQueryDataDTO.getEffectiveStandardRawMaterialsIds();
        if(effectiveIds == null || effectiveIds.isEmpty()){
            throw new RuntimeException("原料ID集合不能为空");
        }

        // 兼容性日志记录
        if (calculationQueryDataDTO.getStandardRawMaterialsIds() != null &&
            !calculationQueryDataDTO.getStandardRawMaterialsIds().isEmpty()) {
            log.warn("使用了已废弃的standardRawMaterialsIds字段，请迁移到materialWithFixedWeights");
        }
        if(calculationQueryDataDTO.getSpecialNotes()==null){
            throw new RuntimeException("特殊要求不能为空");
        }
        if(calculationQueryDataDTO.getStatus()==null){
            throw new RuntimeException("状态不能为空");
        }
//        if(calculationQueryDataDTO.getCalculationId()==null){
//            throw new RuntimeException("测算ID不能为空");
//        }

        String steelGrade = calculationQueryDataDTO.getSteelGrade();
        String standardName = calculationQueryDataDTO.getStandardName();
        Long processPathId = calculationQueryDataDTO.getProcessPathId();

        // 处理目标成分数据：优先使用传入的数据，否则使用ID列表
        List<String> purposeCompositionsIds = handlePurposeCompositionsData(calculationQueryDataDTO);

        List<String> standardRawMaterialsIds = calculationQueryDataDTO.getEffectiveStandardRawMaterialsIds();
        String specialNotes = calculationQueryDataDTO.getSpecialNotes();
        Integer status = calculationQueryDataDTO.getStatus();
        String calculationId = calculationQueryDataDTO.getCalculationId();
        // 1、先根据原料id集合以及对应的路径id去保存原料清单
        standardRawMaterialsIds.forEach(id -> {
            // 先查询是否存在关系
            PathMaterials existingPathMaterial = pathMaterialsMapper.selectOne(
                    new LambdaQueryWrapper<PathMaterials>()
                            .eq(PathMaterials::getPathId, processPathId)
                            .eq(PathMaterials::getRawMaterialId, Long.parseLong(id))
            );

            if (existingPathMaterial == null) {
                // 不存在才插入
                PathMaterials pathMaterials = new PathMaterials();
                pathMaterials.setPathId(processPathId);
                pathMaterials.setRawMaterialId(Long.valueOf(id));
                pathMaterialsMapper.insert(pathMaterials);
            }
        });
        LambdaQueryWrapper<PathMaterials> QueryWrapper = new LambdaQueryWrapper<>();
        QueryWrapper.eq(PathMaterials::getPathId, processPathId);
        List<PathMaterials> pathMaterials = pathMaterialsMapper.selectList(QueryWrapper);
        for (PathMaterials pathMaterial : pathMaterials) {
            Long rawMaterialId = pathMaterial.getRawMaterialId();
            int status2=0;//遍历所有对应关系，去跟传入的原料清单做对比，如果没有就删掉，0代表没有
            for (String standardRawMaterialsId : standardRawMaterialsIds) {
                if(standardRawMaterialsId.equals(rawMaterialId.toString())){
                    status2=1;
                }
            }
            if(status2==0){
                int id=pathMaterialsMapper.deleteByRawMaterialId(processPathId,rawMaterialId);
            }
        }
        // 1.2根据钢种名字和标准名称查询钢种表和标准表拿到钢种ID和标准ID
        //根据标准名称查询标准id，如果不存在则创建
        ExecutionStandard executionStandard = executionStandardMapper.selectOne(
                new LambdaQueryWrapper<ExecutionStandard>()
                        .eq(ExecutionStandard::getStandardName, standardName)
                        .last("LIMIT 1")
        );
        Long executionStandardId2;
        if (executionStandard != null) {
            executionStandardId2 = executionStandard.getId();
        } else {
            ExecutionStandard newStandard = new ExecutionStandard();
            newStandard.setStandardName(standardName);
            executionStandardMapper.insert(newStandard);
            executionStandardId2 = newStandard.getId();
            log.info("创建了新的执行标准: {}, ID: {}", standardName, executionStandardId2);
        }
        //根据钢种名字查询钢种
        SteelGrades steelGrades = steelGradesMapper.selectOne(
                new LambdaQueryWrapper<SteelGrades>()
                        .eq(SteelGrades::getSteelGrade, steelGrade)
                        .last("LIMIT 1")
        );
        //判断钢种是否存在
        Long steelGradeId2;
        if (steelGrades != null) {
            //钢种存在，直接查询钢种id
            steelGradeId2=steelGrades.getId();

        }else{
            //钢种不存在，就保存钢种，并且保存钢种标准关系表
            SteelGrades newSteelGrade = new SteelGrades();
            newSteelGrade.setSteelGrade(steelGrade);
            steelGradesMapper.insert(newSteelGrade);
            steelGradeId2=newSteelGrade.getId();
            SteelGradeStandardMapping steelGradeStandardMapping = new SteelGradeStandardMapping();
            steelGradeStandardMapping.setSteelGradeId(steelGradeId2);
            steelGradeStandardMapping.setStandardId(executionStandardId2);
            steelGradeStandardMappingMapper.insert(steelGradeStandardMapping);
        }
        // 2.1 查询是否存在相同条件的记录
        StandardIngredientRecords existingRecord = standardIngredientRecordsMapper.selectOne(
                new LambdaQueryWrapper<StandardIngredientRecords>()
                        .eq(StandardIngredientRecords::getId, calculationQueryDataDTO.getStandardIngredientRecordsId())
        );
        Long standardIngredientRecordId;
        // 2.2、先初始化一个主表对象
        StandardIngredientRecords standardIngredientRecords = new StandardIngredientRecords();
        if (existingRecord != null) {
            // 如果存在，更新现有记录
            existingRecord.setSpecialNotes(specialNotes);
            existingRecord.setStatus(status);
            existingRecord.setSteelGradeId(steelGradeId2);
            existingRecord.setExecutionStandardId(executionStandardId2);
            existingRecord.setProcessPathId(processPathId);
            // 设置金属料吨水单耗配置ID和值
            existingRecord.setMetalMaterialWaterConsumptionConfigId(
                calculationQueryDataDTO.getMetalMaterialWaterConsumptionConfigId());
            existingRecord.setMetalMaterialWaterConsumption(
                calculationQueryDataDTO.getMetalMaterialWaterConsumption());
            // 3、查询最新的成本测算记录
//            LambdaQueryWrapper<CostEstimation> wrapper2 = new LambdaQueryWrapper<>();
//            wrapper2.orderByDesc(CostEstimation::getUpdateTime)
//                    .last("limit 1");
            CostEstimation latestEstimation = null;
            if (calculationId != null && calculationId.length() > 0) {
                latestEstimation = costEstimationMapper.selectByCostId(Long.parseLong(calculationId));
            }

            if (latestEstimation != null) {
                String username = latestEstimation.getUsername();
                String departmentId = latestEstimation.getDepartmentId();
                String costEstimattionId = latestEstimation.getId().toString();
                existingRecord.setUserName(username);
                if(departmentId!=null){
                    existingRecord.setDepartmentId(Long.valueOf(departmentId));
                }
                existingRecord.setCostEstimattionId(Long.valueOf(costEstimattionId));
                // 4、根据成本测算id去cost_estimation表中拿到评审编号
//                CostEstimation costEstimation = costEstimationMapper.selectOne(new LambdaQueryWrapper<CostEstimation>().eq(CostEstimation::getId, Long.valueOf(costEstimattionId)));
                existingRecord.setCalculationProcessNo(latestEstimation.getEstimationNo());
            }else {
                CurrentUser currentUser = UserContext.getCurrentUser();
                existingRecord.setUserName(currentUser.getUsername());
                String department = currentUser.getDepartment();
                if(department != null && !department.isEmpty()){
                    Departments departments = departmentsMapper.selectOne(new LambdaQueryWrapper<Departments>().eq(Departments::getDepartmentName, department).last("LIMIT 1"));
                    if(departments != null) {
                        existingRecord.setDepartmentId(departments.getId());
                    } else {
                        // 部门不存在时的处理：可以设置为null或默认部门ID
                        existingRecord.setDepartmentId(null);
                        log.warn("未找到部门：{}，设置部门ID为null", department);
                    }
                } else {
                    // 用户没有部门信息时的处理
                    existingRecord.setDepartmentId(null);
                    log.info("用户没有部门信息，设置部门ID为null");
                }
            }

            if(status == 1){
                existingRecord.setCategory("标准配料");
                existingRecord.setMixingDate(LocalDateTime.now());
                existingRecord.setReleaseDate(LocalDateTime.now());
            }
            if(status == 0){
                existingRecord.setCategory(null);
                existingRecord.setMixingDate(LocalDateTime.now());
                existingRecord.setReleaseDate(null);
            }
            standardIngredientRecordsMapper.updateById(existingRecord);
            standardIngredientRecordId = existingRecord.getId();
        } else {
            standardIngredientRecords.setSpecialNotes(specialNotes);
            standardIngredientRecords.setStatus(status);
            standardIngredientRecords.setSteelGradeId(steelGradeId2);
            standardIngredientRecords.setExecutionStandardId(executionStandardId2);
            standardIngredientRecords.setProcessPathId(processPathId);
            // 设置金属料吨水单耗配置ID和值
            standardIngredientRecords.setMetalMaterialWaterConsumptionConfigId(
                calculationQueryDataDTO.getMetalMaterialWaterConsumptionConfigId());
            standardIngredientRecords.setMetalMaterialWaterConsumption(
                calculationQueryDataDTO.getMetalMaterialWaterConsumption());
            // 3、查询最新的成本测算记录

            CostEstimation latestEstimation = new CostEstimation();
            latestEstimation=null;
            if(calculationId!=null&&calculationId.length()>0){
                latestEstimation=costEstimationMapper.selectByCostId(Long.parseLong(calculationId));
            }

            if (latestEstimation != null) {
                String username = latestEstimation.getUsername();
                String departmentId = latestEstimation.getDepartmentId();
                String costEstimattionId = latestEstimation.getId().toString();
                standardIngredientRecords.setUserName(username);
                standardIngredientRecords.setDepartmentId(Long.valueOf(departmentId));
                standardIngredientRecords.setCostEstimattionId(Long.valueOf(costEstimattionId));
                // 4、根据成本测算id去cost_estimation表中拿到评审编号
//                CostEstimation costEstimation = costEstimationMapper.selectOne(new LambdaQueryWrapper<CostEstimation>().eq(CostEstimation::getId, Long.valueOf(costEstimattionId)));
                standardIngredientRecords.setCalculationProcessNo(latestEstimation.getEstimationNo());
            }else {
                CurrentUser currentUser = UserContext.getCurrentUser();
                standardIngredientRecords.setUserName(currentUser.getUsername());
                String department = currentUser.getDepartment();
                if(department != null && !department.isEmpty()) {
                    Departments departments = departmentsMapper.selectOne(new LambdaQueryWrapper<Departments>().eq(Departments::getDepartmentName, department).last("LIMIT 1"));
                    if(departments != null){
                        standardIngredientRecords.setDepartmentId(departments.getId());
                    } else {
                        // 部门不存在时的处理：可以设置为null或默认部门ID
                        standardIngredientRecords.setDepartmentId(null);
                        log.warn("未找到部门：{}，设置部门ID为null", department);
                    }
                } else {
                    // 用户没有部门信息时的处理
                    standardIngredientRecords.setDepartmentId(null);
                    log.info("用户没有部门信息，设置部门ID为null");
                }
            }
            // 5、根据钢种名称和标准名称查询钢种表和标准表拿到钢种ID和标准ID
            Long steelGradeId = steelGradesMapper.selectOne(new LambdaQueryWrapper<SteelGrades>().eq(SteelGrades::getSteelGrade, steelGrade).last("LIMIT 1")).getId();
            Long executionStandardId = executionStandardMapper.selectOne(new LambdaQueryWrapper<ExecutionStandard>().eq(ExecutionStandard::getStandardName, standardName).last("LIMIT 1")).getId();
            standardIngredientRecords.setSteelGradeId(steelGradeId);
            standardIngredientRecords.setExecutionStandardId(executionStandardId);
            // 6.1、如果status是1，那么配料日期，发布日期以及类别就是当前时间和标准配料
            standardIngredientRecords.setSpecialNotes(specialNotes);
            standardIngredientRecords.setStatus(status);
            if(status == 1){
                standardIngredientRecords.setCategory("标准配料");
                standardIngredientRecords.setMixingDate(LocalDateTime.now());
                standardIngredientRecords.setReleaseDate(LocalDateTime.now());
            }
            // 6.2、如果status是0，那么配料日期是当前时间，类别为null，发布日期为null
            if(status == 0){
                standardIngredientRecords.setCategory(null);
                standardIngredientRecords.setMixingDate(LocalDateTime.now());
                standardIngredientRecords.setReleaseDate(null);
            }
            //到这里主表初始化完毕，进行保存，拿到返回的主键id,之后需要更新关联表
            standardIngredientRecordsMapper.insert(standardIngredientRecords);
            standardIngredientRecordId=standardIngredientRecords.getId();
        }
        // 7、根据目标成分id集合去目标成分表，把主表id存进去,目标成分就是计算结果表中的配入量
        purposeCompositionsIds.forEach(id -> {
            // 先查询是否已经关联了主表ID
            PurposeCompositions existingComposition = purposeCompositionsMapper.selectOne(
                    new LambdaQueryWrapper<PurposeCompositions>()
                            .eq(PurposeCompositions::getId, Long.parseLong(id))
                            .eq(PurposeCompositions::getStandardIngredientRecordId, standardIngredientRecordId)
            );

            if (existingComposition == null) {
                // 如果没有关联，则更新关联关系
                PurposeCompositions purposeCompositions = purposeCompositionsMapper.selectById(Long.parseLong(id));
                if (purposeCompositions == null) {
                    log.error("未找到目标成分记录，id={}", id);
                    throw new RuntimeException("未找到目标成分记录，id=" + id);
                }
                purposeCompositions.setStandardIngredientRecordId(standardIngredientRecordId);
                purposeCompositionsMapper.updateById(purposeCompositions);
            }
        });
        // 根据status参数决定是否执行计算逻辑
        Integer requestStatus = calculationQueryDataDTO.getStatus();
        if (requestStatus != null && requestStatus == 0) {
            // 草稿状态：只保存主表数据，不保存计算结果，不调用算法接口
            log.info("草稿状态，跳过计算结果保存和算法接口调用，直接返回草稿保存结果");
            return buildDraftResponse(standardIngredientRecordId, calculationQueryDataDTO);
        }

        // 8、根据原料id集合去初始化计算结果对象
        // 9、遍历原料id集合，初始化计算结果对象，把主表id存进去，原料id存进去
            // 9.1 保存完后记录计算结果表id，返回给前端

        // 生成新的计算序号
        Integer nextCalculationSequence = getNextCalculationSequence(standardIngredientRecordId);
        log.info("为配料记录ID: {} 生成计算序号: {}", standardIngredientRecordId, nextCalculationSequence);

        List<String> calculationResultIdList = new ArrayList<>();
        standardRawMaterialsIds.forEach(id -> {
            // ✅ 始终创建新的计算结果记录（新批次）
            CalculationResult calculationResult = new CalculationResult();
            calculationResult.setStandardIngredientRecordId(standardIngredientRecordId);
            calculationResult.setRawMaterialId(Long.parseLong(id));

            // 设置计算序号（新计算或更新现有计算都使用新序号）
            calculationResult.setCalculationSequence(nextCalculationSequence.toString());
            // 10、根据原料id查询原料详细信息，包括原料名称，品位，收得率，然后存进去
            // 设置其他字段
            StandardRawMaterials standardRawMaterials = standardRawMaterialsMapper.selectById(Long.parseLong(id));
            calculationResult.setRawMaterialName(standardRawMaterials.getName());
            calculationResult.setComposition(standardRawMaterials.getComposition());
            calculationResult.setRecoveryRate(standardRawMaterials.getYieldRate());
            //拿到原料的主元素名称，去目标成分表中查询，得到目标成分id以及目标值,把目标成分id和目标值存入计算结果表中，目标值对应配入量
            // 设置目标成分相关字段
            String element = standardRawMaterials.getElement();
            PurposeCompositions purposeCompositions = purposeCompositionsMapper.selectOne(
                    new LambdaQueryWrapper<PurposeCompositions>()
                            .eq(PurposeCompositions::getElementName, element)
                            .eq(PurposeCompositions::getStandardIngredientRecordId, standardIngredientRecordId)
                            .last("LIMIT 1")
            );
            if (purposeCompositions != null) {
                calculationResult.setPurposeCompositionId(purposeCompositions.getId());
                calculationResult.setWieght(purposeCompositions.getAverageValue());
            }else{
                log.error("未找到目标成分记录，element={}", element);
                // 如果没有找到对应元素的目标成分，则获取任意一个可用的目标成分记录
                PurposeCompositions defaultPurposeComposition = purposeCompositionsMapper.selectOne(
                        new LambdaQueryWrapper<PurposeCompositions>()
                                .eq(PurposeCompositions::getStandardIngredientRecordId, standardIngredientRecordId)
                                .last("LIMIT 1")
                );

                if (defaultPurposeComposition != null) {
                    calculationResult.setPurposeCompositionId(defaultPurposeComposition.getId());
                    calculationResult.setWieght(defaultPurposeComposition.getAverageValue());
                } else {
                    log.error("未找到任何可用的目标成分记录");
                    throw new RuntimeException("未找到任何可用的目标成分记录");
                }
            }

            //到这里计算结果表初始化完毕，进行保存，拿到返回的id
            // ✅ 始终插入新记录（新批次）
            calculationResultMapper.insert(calculationResult);
            calculationResultIdList.add(String.valueOf(calculationResult.getId()));
        });
        // 重新计算总量和成本
        List<CalculationResult> calculationResults = calculationResultMapper.selectList(
                new LambdaQueryWrapper<CalculationResult>()
                        .eq(CalculationResult::getStandardIngredientRecordId, standardIngredientRecordId)
        );

        BigDecimal totalWeight = BigDecimal.ZERO;
        BigDecimal totalCost = BigDecimal.ZERO;
        for (CalculationResult result : calculationResults) {
            if (result.getWieght() != null) {
                totalWeight = totalWeight.add(result.getWieght());

                StandardRawMaterials material = standardRawMaterialsMapper.selectById(result.getRawMaterialId());
                if (material != null && material.getPrice() != null) {
                    totalCost = totalCost.add(material.getPrice().multiply(result.getWieght()));
                }
            }
        }
        // 更新主表
        StandardIngredientRecords updateRecord = new StandardIngredientRecords();
        updateRecord.setId(standardIngredientRecordId);
        updateRecord.setCostPrice(totalCost);
        updateRecord.setRawMaterialTotal(totalWeight);
        standardIngredientRecordsMapper.updateById(updateRecord);
        // 11、接下来封装调用计算算法的请求对象
        CalculationQueryData calculationQueryData = new CalculationQueryData();
        // 12、参数包括原料属性集合（名称，主元素，基准品味，收得率，C含量，优先级）
        List<Material_dict> lisMaterialDict = calculationQueryData.getLis_material_dict();
        if (lisMaterialDict == null) {
            lisMaterialDict = new ArrayList<>(); // 初始化列表
            calculationQueryData.setLis_material_dict(lisMaterialDict); // 设置回对象
        }
        List<Long> standardRawMaterialsIdList = new ArrayList<>();
        for (String standardRawMaterialsId : standardRawMaterialsIds) {
            standardRawMaterialsIdList.add(Long.parseLong(standardRawMaterialsId));
        }
        List<StandardRawMaterials> standardRawMaterials = standardRawMaterialsMapper.selectByIds(standardRawMaterialsIdList);
        for (StandardRawMaterials standardRawMaterials1 : standardRawMaterials) {
            Material_dict materialDict = Material_dict.builder()
                    .name("高铬")
                    .element("Cr")
                    .g_rate(50)
                    .r_rate(97)
                    .c_content(7)
                    .priority(0)
                    .build();

//            Material_dict materialDict = Material_dict.builder()
//                    .name(standardRawMaterials1.getName())
//                    .element(standardRawMaterials1.getElement())
//                    .g_rate(standardRawMaterials1.getComposition().floatValue())
//                    .r_rate(standardRawMaterials1.getYieldRate().floatValue())
//                    .c_content(standardRawMaterials1.getCContent() != null ? standardRawMaterials1.getCContent().floatValue() : 0.0f)
//                    .priority(standardRawMaterials1.getPriority())
//                    .build();
            lisMaterialDict.add(materialDict);
        }
        calculationQueryData.setLis_material_dict(lisMaterialDict);
        // 13、设备名字集合（算法要三个设备集合，第一个和第三个一样，第二个传默认值）
        List<String> lisDevice = calculationQueryData.getLis_device();
        if (lisDevice == null) {
            lisDevice = new ArrayList<>(); // 初始化列表
            calculationQueryData.setLis_device(lisDevice); // 设置回对象
        }
        List<String> lisScenario = calculationQueryData.getLis_scenario();
        if (lisScenario == null) {
            lisScenario = new ArrayList<>(); // 初始化列表
            calculationQueryData.setLis_scenario(lisScenario); // 设置回对象
        }
        List<String> lisDeoxygen = calculationQueryData.getLis_deoxygen();
        if (lisDeoxygen == null) {
            lisDeoxygen = new ArrayList<>();
            calculationQueryData.setLis_deoxygen(lisDeoxygen);
        }
            // 13.1、根据路径id找到所有工序，再根据工序找到设备名称，存入设备名字集合
            //查找当前路径对应的工序以及设备信息
            ProcessPathVO processPathVO = new ProcessPathVO();
            processPathVO.setId(processPathId);
            List<PathSaveDTO> processPathSteps=processPathStepsService.selectProcessPathSteps(processPathVO);
            //取出所有的设备名称
//        for (PathSaveDTO pathSaveDTO : processPathSteps) {
//            lisDevice.add(pathSaveDTO.getEquipmentName());
//            lisDeoxygen.add(pathSaveDTO.getEquipmentName());
////            lisDevice.add("高ddsd");
////            lisDeoxygen.add("高sda铬");
//        }
            //默认值
        lisDevice.add("高ddsd");
        lisDevice.add("电炉");
        lisDevice.add("高铬dsd");
        lisDeoxygen.add("高sda铬");
        lisDeoxygen.add("高铬");
        lisDeoxygen.add("VOD品种");
        lisDeoxygen.add("高ffsad铬");
            lisScenario.add("高sdad铬");
            lisScenario.add("高铬");
            lisScenario.add("非AOD连铸");
            calculationQueryData.setLis_device(lisDevice);
            calculationQueryData.setLis_scenario(lisScenario);
            calculationQueryData.setLis_deoxygen(lisDeoxygen);
        // 14、目标成分集合
        List<Element_composition_dict> lisElementCompositionDict = calculationQueryData.getLis_element_composition_dict();
        if (lisElementCompositionDict == null) {
            lisElementCompositionDict = new ArrayList<>(); // 初始化列表
            calculationQueryData.setLis_element_composition_dict(lisElementCompositionDict); // 设置回对象
        }
        List<Long> purposeCompositionsIdList = new ArrayList<>();
        for (String purposeCompositionsId : purposeCompositionsIds) {
            purposeCompositionsIdList.add(Long.parseLong(purposeCompositionsId));
        }
        List<PurposeCompositions> purposeCompositions = purposeCompositionsMapper.selectByIds(purposeCompositionsIdList);
        for (PurposeCompositions purposeCompositions1 : purposeCompositions) {
            Element_composition_dict elementCompositionDict = Element_composition_dict.builder()
                    .element("Cr")
                    .composition(1.275F)
                    .build();
//            Element_composition_dict elementCompositionDict = Element_composition_dict.builder()
//                    .element(purposeCompositions1.getElementName())
//                    .composition(purposeCompositions1.getAverageValue().floatValue())
//                    .build();
            lisElementCompositionDict.add(elementCompositionDict);
        }
        calculationQueryData.setLis_element_composition_dict(lisElementCompositionDict);

        // 15、调用算法接口，传入参数，得到返回结果
        // 根据配置决定使用哪个算法接口
        if (useNewAlgorithm) {
            log.info("使用新配料算法接口");
            callNewAlgorithmAPI(calculationQueryDataDTO, calculationResults);
        } else {
            log.info("使用旧配料算法接口");
            callOldAlgorithmAPI(calculationQueryData, calculationResults);
        }
//        calculationAPIResponses.forEach(calculationAPIResponse -> {
////
//            // 先查询原料，并进行空值检查
//            StandardRawMaterials material = standardRawMaterialsMapper.selectOne(
//                    new LambdaQueryWrapper<StandardRawMaterials>()
//                            .eq(StandardRawMaterials::getName, calculationAPIResponse.getMaterial_name())
//            );
//
//            // 如果找不到原料，记录日志并跳过
//            if (material == null) {
//                log.warn("未找到原料: {}", calculationAPIResponse.getMaterial_name());
//                return; // 跳过当前循环
//            }
//
//            // 找到原料后再处理
//            CalculationResult calculationResult = calculationResultMapper.selectOne(
//                    new LambdaQueryWrapper<CalculationResult>()
//                            .eq(CalculationResult::getRawMaterialId, material.getId())
//                            .eq(CalculationResult::getStandardIngredientRecordId, standardIngredientRecordId)
//            );
//
//            if (calculationResult != null) {
//                calculationResult.setSingleConsume(String.valueOf(calculationAPIResponse.getUnit_consumption_tons()));
//                calculationResult.setUnit_consumption_tons(calculationAPIResponse.getUnit_consumption_tons());
//                calculationResultMapper.updateById(calculationResult);
//            } else {
//                log.warn("未找到计算结果记录，原料ID: {}, 原料名称: {}",
//                        material.getId(), calculationAPIResponse.getMaterial_name());
//            }
//        });
        // 查询主表记录，获取金属料吨水单耗信息
        StandardIngredientRecords record = standardIngredientRecordsMapper.selectById(standardIngredientRecordId);

        // 创建返回VO并设置所有字段
        CalculationResultDataVO calculationResultDataVO = new CalculationResultDataVO();
        calculationResultDataVO.setStandardIngredientRecordId(standardIngredientRecordId);
        calculationResultDataVO.setCalculationResultIdList(calculationResultIdList);

        // 设置金属料吨水单耗相关字段
        if (record != null) {
            calculationResultDataVO.setMetalMaterialWaterConsumptionConfigId(
                record.getMetalMaterialWaterConsumptionConfigId());
            calculationResultDataVO.setMetalMaterialWaterConsumption(
                record.getMetalMaterialWaterConsumption());
        }

        return calculationResultDataVO;
    }



    @Override
    public CostEstimation getCostEstimationById(Long id) {
        log.info("开始查询成本测算，id: {}", id);
        CostEstimation result = baseMapper.selectByCostId(id);
        log.info("查询结果: {}", result);
        return result;
    }

    /**
     * 获取下一个计算序号
     * @param standardIngredientRecordId 配料记录ID
     * @return 下一个计算序号
     */
    private Integer getNextCalculationSequence(Long standardIngredientRecordId) {
        // 查询当前配料记录的最大计算序号
        LambdaQueryWrapper<CalculationResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CalculationResult::getStandardIngredientRecordId, standardIngredientRecordId)
               .isNotNull(CalculationResult::getCalculationSequence)
               .last("ORDER BY CAST(calculation_sequence AS INTEGER) DESC LIMIT 1");

        CalculationResult latestResult = calculationResultMapper.selectOne(wrapper);

        if (latestResult == null || latestResult.getCalculationSequence() == null) {
            // 如果没有历史记录或序号为空，从1开始
            return 1;
        } else {
            // 返回最大序号 + 1
            try {
                return Integer.parseInt(latestResult.getCalculationSequence()) + 1;
            } catch (NumberFormatException e) {
                log.warn("计算序号格式错误: {}, 使用默认值1", latestResult.getCalculationSequence());
                return 1;
            }
        }
    }

    /**
     * 数据迁移：为历史计算结果补充序号
     * 注意：这个方法应该在系统升级时执行一次，用于处理历史数据
     */
    public void migrateHistoricalCalculationSequences() {
        log.info("开始为历史计算结果补充序号...");

        // 查询所有没有序号的计算结果，按配料记录ID和创建时间分组
        LambdaQueryWrapper<CalculationResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNull(CalculationResult::getCalculationSequence)
               .orderBy(true, true, CalculationResult::getStandardIngredientRecordId)
               .orderBy(true, true, CalculationResult::getCreatetime);

        List<CalculationResult> resultsWithoutSequence = calculationResultMapper.selectList(wrapper);

        if (resultsWithoutSequence.isEmpty()) {
            log.info("没有需要迁移的历史数据");
            return;
        }

        // 按配料记录ID分组处理
        Map<Long, List<CalculationResult>> groupedResults = resultsWithoutSequence.stream()
                .collect(Collectors.groupingBy(CalculationResult::getStandardIngredientRecordId));

        int totalUpdated = 0;
        for (Map.Entry<Long, List<CalculationResult>> entry : groupedResults.entrySet()) {
            Long recordId = entry.getKey();
            List<CalculationResult> results = entry.getValue();

            // 为每个配料记录的历史数据分配序号1
            // 假设历史数据都是第一次计算的结果
            for (CalculationResult result : results) {
                result.setCalculationSequence("1");
                calculationResultMapper.updateById(result);
                totalUpdated++;
            }

            log.info("配料记录ID: {} 的 {} 条历史计算结果已补充序号", recordId, results.size());
        }

        log.info("历史数据迁移完成，共更新 {} 条记录", totalUpdated);
    }

    /**
     * 调用新配料算法接口
     */
    private void callNewAlgorithmAPI(CalculationQueryDataDTO dto, List<CalculationResult> calculationResults) {
        try {
            log.info("开始调用新配料算法接口");

            // 1. 转换请求参数
            NewAlgorithmRequest request = algorithmDataTransformService.transformToNewAlgorithmRequest(dto);
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.findAndRegisterModules();
                log.info("新算法接口请求JSON: {}", objectMapper.writeValueAsString(request));
            } catch (Exception e) {
                log.error("序列化新算法请求JSON失败", e);
            }

            // 2. 创建HTTP客户端
            HttpClient httpClient = HttpClient.create()
                    .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 30000)
                    .responseTimeout(Duration.ofSeconds(30));

            // 3. 发起请求
            Mono<NewAlgorithmResponse> responseMono = webClientBuilder
                    .clientConnector(new ReactorClientHttpConnector(httpClient))
                    .build()
                    .post()
                    .uri(newAlgorithmBaseUrl + newAlgorithmEndpoint)
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                    .bodyValue(request)
                    .retrieve()
                    .onStatus(HttpStatusCode::isError, clientResponse -> {
                        log.error("新算法接口调用失败，状态码: {}", clientResponse.statusCode());
                        return clientResponse.bodyToMono(String.class)
                                .flatMap(errorBody -> Mono.error(new RuntimeException("新算法接口调用失败: " + errorBody)));
                    })
                    .bodyToMono(NewAlgorithmResponse.class)
                    .onErrorResume(Exception.class, e -> {
                        log.error("新算法接口调用异常", e);
                        return Mono.error(new RuntimeException("新算法接口调用异常: " + e.getMessage()));
                    });

            // 4. 获取响应
            NewAlgorithmResponse response = responseMono.block();
            log.info("新算法接口响应: {}", response);

            if (response == null || response.getCode() != 200) {
                throw new RuntimeException("新算法接口返回错误: " + (response != null ? response.getMsg() : "响应为空"));
            }

            // 5. 处理响应数据
            algorithmDataTransformService.processNewAlgorithmResponse(response, calculationResults);

            // 6. 更新数据库
            for (CalculationResult calculationResult : calculationResults) {
                calculationResultMapper.updateById(calculationResult);
            }

            log.info("新算法接口调用完成，更新了 {} 条计算结果", calculationResults.size());

        } catch (Exception e) {
            log.error("新算法接口调用失败", e);
            throw new RuntimeException("新算法接口调用失败: " + e.getMessage());
        }
    }

    /**
     * 调用旧配料算法接口（保持原有逻辑）
     */
    private void callOldAlgorithmAPI(CalculationQueryData calculationQueryData, List<CalculationResult> calculationResults) {
        try {
            log.info("开始调用旧配料算法接口");
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.findAndRegisterModules();
                log.info("旧算法接口请求JSON: {}", objectMapper.writeValueAsString(calculationQueryData));
            } catch (Exception e) {
                log.error("序列化旧算法请求JSON失败", e);
            }

            // 创建 HttpClient 并配置超时
            HttpClient httpClient = HttpClient.create()
                    .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 30000) // 设置连接超时
                    .responseTimeout(Duration.ofSeconds(30)); // 设置响应超时

            // 使用 WebClient 发起请求，并通过自定义 HttpClient 配置超时
            Mono<HttpResVo2> responseMono = webClientBuilder
                    .clientConnector(new ReactorClientHttpConnector(httpClient)) // 使用自定义 HttpClient
                    .build()
                    .post() // 使用 POST 方法
                    .uri(calculationBaseUrl + calculateEndpoint) // 使用配置的URL
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                    .bodyValue(calculationQueryData) // 请求体数据
                    .retrieve() // 获取响应
                    .onStatus(HttpStatusCode::isError, clientResponse -> {
                        log.error("HTTP 错误状态码：{}", clientResponse.statusCode());
                        return clientResponse.bodyToMono(String.class)
                                .flatMap(errorBody -> Mono.error(new RuntimeException("服务端错误：" + errorBody)));
                    })
                    .bodyToMono(HttpResVo2.class)
                    .onErrorResume(WebClientRequestException.class, e -> {
                        log.error("请求失败，原因：{}", e.getMessage());
                        if (e.getCause() instanceof ConnectException) {
                            return Mono.just(new HttpResVo2()); // 连接失败时返回一个空的响应
                        }
                        return Mono.just(new HttpResVo2());
                    })
                    .onErrorResume(Exception.class, e -> {
                        log.error("请求失败，原因：{}", e.getMessage());
                        return Mono.just(new HttpResVo2());
                    });

            // 阻塞获取响应结果
            HttpResVo2 response = responseMono.block(); // 阻塞等待响应
            log.info("旧算法接口响应: {}", response);

            if (response == null || response.getData() == null) {
                throw new RuntimeException("API response is null or empty");
            }

            // 封装返回结果对象接收响应结果
            List<CalculationAPIResponse> data = response.getData();
            if (data == null) {
                throw new RuntimeException("API response data is null");
            }

            // 使用 Jackson 的 ObjectMapper 解析 JSON 字符串为对象列表
            ObjectMapper objectMapper = new ObjectMapper();
            List<CalculationAPIResponse> calculationAPIResponses = new ArrayList<>();
            try {
                calculationAPIResponses = objectMapper.convertValue(response.getData(),
                        new TypeReference<List<CalculationAPIResponse>>() {});
            } catch (Exception e) {
                // 处理解析异常，例如记录日志或抛出异常
                log.error("解析响应数据失败: {}", e.getMessage());
                throw new RuntimeException("解析响应数据失败", e);
            }

            //现在拿到了计算之后的原料类型，原料名称和原料单耗
            //根据原料类型和原料名称找到原料id，通过原料id找到计算结果表，并设置上单耗进行更新
            for (int i = 0; i < calculationResults.size(); i++) {
                CalculationResult calculationResult = calculationResults.get(i);
                CalculationAPIResponse calculationAPIResponse = calculationAPIResponses.get(i);
                calculationResult.setSingleConsume(String.valueOf(calculationAPIResponse.getUnit_consumption_tons()));
                calculationResult.setUnit_consumption_tons(calculationAPIResponse.getUnit_consumption_tons());
                calculationResultMapper.updateById(calculationResult);
            }

            log.info("旧算法接口调用完成，更新了 {} 条计算结果", calculationResults.size());

        } catch (Exception e) {
            log.error("旧算法接口调用失败", e);
            throw new RuntimeException("旧算法接口调用失败: " + e.getMessage());
        }
    }
}




