package com.nercar.ingredient.domain.dto;


import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StandardIngredientRecordsQueryDTO extends BaseDTO{

    /**
     * 标准名称
     */
    private String standardName;
    /**
     * 钢种
     */
    private String steelGrade;

    /**
     * 配料日期
     */
    @Schema(name = "配料日期开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime  startmixingDate;
    @Schema(name = "配料日期结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime  endmixingDate;

    private Integer pageNo;
    private Integer pageSize;

}
