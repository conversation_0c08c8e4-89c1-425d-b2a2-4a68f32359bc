# 成锭率和成材率表字段扩展修改总结

## 📋 **修改概述**
根据业务需求，对成锭率表和成材率表进行了字段扩展，并完成了相关Java类的修改。

## ✅ **已完成的修改**

### 1. **数据库修改**
- ✅ 成锭率表（ingot_yield_rates）添加 `is_custom` 字段
  ```sql
  ALTER TABLE "public"."ingot_yield_rates" 
  ADD COLUMN "is_custom" int4 DEFAULT 0;
  
  COMMENT ON COLUMN "public"."ingot_yield_rates"."is_custom" IS '是否自定义(0=系统默认,1=用户自定义)';
  ```

- ✅ 成材率表（material_yield_rates）添加 `ingot_type` 字段
  ```sql
  ALTER TABLE "public"."material_yield_rates" 
  ADD COLUMN "ingot_type" varchar(32);
  
  COMMENT ON COLUMN "public"."material_yield_rates"."ingot_type" IS '锭型';
  ```

### 2. **Java类修改**

#### **成锭率表相关类**
- ✅ **IngotYieldRates.java**（实体类）
  ```java
  /**
   * 是否自定义(0=系统默认,1=用户自定义)
   */
  private Integer isCustom;
  ```

- ✅ **IngotYieldRatesDTO.java**（请求参数）
  ```java
  /**
   * 是否自定义(0=系统默认,1=用户自定义)
   */
  @Schema(description = "是否自定义(0=系统默认,1=用户自定义)")
  private Integer isCustom;
  ```

- ✅ **IngotYieldRatesVO.java**（响应数据）
  ```java
  /**
   * 是否自定义(0=系统默认,1=用户自定义)
   */
  @Schema(description = "是否自定义(0=系统默认,1=用户自定义)")
  private Integer isCustom;
  ```

#### **成材率表相关类**
- ✅ **MaterialYieldRates.java**（实体类）
  ```java
  /**
   * 锭型
   */
  private String ingotType;
  ```

- ✅ **MaterialYieldRatesDTO.java**（请求参数）
  ```java
  /**
   * 锭型
   */
  @Schema(description = "锭型")
  private String ingotType;
  ```

- ✅ **MaterialYieldRatesVO.java**（响应数据）
  ```java
  /**
   * 锭型
   */
  @Schema(description = "锭型")
  private String ingotType;
  ```

## 🔧 **修改说明**

### **1. 成锭率表字段扩展**
- **字段名称**：`is_custom`
- **Java属性**：`isCustom`
- **数据类型**：`int4`（数据库）/ `Integer`（Java）
- **默认值**：`0`（系统默认）
- **业务含义**：区分系统预设的成锭率和用户自定义的成锭率
- **取值范围**：`0`=系统默认，`1`=用户自定义

### **2. 成材率表字段扩展**
- **字段名称**：`ingot_type`
- **Java属性**：`ingotType`
- **数据类型**：`varchar(32)`（数据库）/ `String`（Java）
- **业务含义**：记录不同锭型对应的成材率数据
- **可能的取值**：圆锭、方锭、扁锭等

## 📊 **业务影响**

### **1. 成锭率管理**
- 可以区分系统默认的成锭率和用户自定义的成锭率
- 在查询时可以按自定义类型进行筛选
- 支持优先使用用户自定义的成锭率数据

### **2. 成材率管理**
- 可以记录不同锭型的成材率数据
- 支持按锭型进行数据筛选和查询
- 提高成材率数据的精确性和实用性

## ⚠️ **注意事项**

### **1. 数据兼容性**
- 现有数据的 `is_custom` 字段默认为 `0`（系统默认）
- 现有数据的 `ingot_type` 字段为 `NULL`
- 需要注意历史数据的处理

### **2. 前端界面**
- 前端界面需要添加对应字段的显示和输入
- 成锭率管理界面需要添加"是否自定义"的选择
- 成材率管理界面需要添加"锭型"的输入

### **3. 业务逻辑**
- 目前只完成了基础的数据结构修改
- 后续可能需要根据业务需求添加相关的业务逻辑
- 可能需要优化查询和筛选功能

## 🎯 **后续建议**

### **1. Excel导入导出**
- 建议后续修改Excel导入导出相关类，支持新字段
- 相关文件：`IngotYieldRatesExcel.java`、`MaterialYieldRatesExcel.java`

### **2. 业务逻辑扩展**
- 可以在Service层添加按自定义类型和锭型查询的方法
- 可以优化查询逻辑，支持更灵活的筛选

### **3. 前端界面优化**
- 添加新字段的显示和输入控件
- 支持按新字段进行筛选和排序

## 🎉 **总结**

本次修改完成了成锭率表和成材率表的字段扩展，并修改了相关的Java类，保证了基本的数据读写功能。后续可以根据业务需要进一步完善其他功能。
