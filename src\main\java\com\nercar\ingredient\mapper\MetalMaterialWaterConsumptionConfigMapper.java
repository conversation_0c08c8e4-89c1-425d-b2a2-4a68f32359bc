package com.nercar.ingredient.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nercar.ingredient.domain.po.MetalMaterialWaterConsumptionConfig;
import com.nercar.ingredient.domain.vo.MetalMaterialWaterConsumptionConfigVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 金属料吨水单耗配置Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface MetalMaterialWaterConsumptionConfigMapper extends BaseMapper<MetalMaterialWaterConsumptionConfig> {

    /**
     * 分页查询金属料吨水单耗配置列表
     * @param page 分页参数
     * @param method 冶炼方法（可选）
     * @param device 冶炼设备（可选）
     * @return 分页结果
     */
    IPage<MetalMaterialWaterConsumptionConfigVO> selectConfigPage(
            Page<MetalMaterialWaterConsumptionConfigVO> page,
            @Param("method") String method,
            @Param("device") String device
    );
}
