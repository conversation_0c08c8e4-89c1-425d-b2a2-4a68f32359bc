<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.CostEstimationMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.CostEstimation">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="estimationNo" column="estimation_no" jdbcType="VARCHAR"/>
            <result property="userId" column="userid" jdbcType="VARCHAR"/>
            <result property="username" column="username" jdbcType="VARCHAR"/>
            <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
            <result property="steelNumber" column="steel_number" jdbcType="VARCHAR"/>
            <result property="finishedProductSpecification" column="finished_product_specification" jdbcType="VARCHAR"/>
            <result property="estimatedOrderQuantity" column="estimated_order_quantity" jdbcType="VARCHAR"/>
            <result property="quotationOrCostEstimation" column="quotation_or_cost_estimation" jdbcType="VARCHAR"/>
            <result property="forgedMaterial" column="forged_material" jdbcType="VARCHAR"/>
            <result property="changeStandardCost" column="change_standard_cost" jdbcType="VARCHAR"/>
            <result property="lengthDeliveryStatus" column="length_delivery_status" jdbcType="VARCHAR"/>
            <result property="surfaceDeliveryStatus" column="surface_delivery_status" jdbcType="VARCHAR"/>
            <result property="heatDeliveryStatus" column="heat_delivery_status" jdbcType="VARCHAR"/>
            <result property="technicalStandard" column="technical_standard" jdbcType="VARCHAR"/>
            <result property="processRoute" column="process_route" jdbcType="VARCHAR"/>
            <result property="approveStatus" column="approve_status" jdbcType="VARCHAR"/>
            <result property="subStatus" column="sub_status" jdbcType="INTEGER"/>
            <result property="departmentId" column="department_id" jdbcType="VARCHAR"/>
            <result property="processinstanceid" column="processinstanceid" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="taskId" column="taskid" jdbcType="VARCHAR"/>
            <result property="num" column="num" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,estimation_no,user_id,
        username,company_name,steel_number,
        finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,
        forged_material,change_standard_cost,length_delivery_status,
        surface_delivery_status,heat_delivery_status,technical_standard,
        process_route,approve_status,sub_status,
        department_id,processinstanceid,created_by,
        update_by,update_time,created_time,
        task_id,num,contract_id
    </sql>

    <select id="selectByCostId" resultType="com.nercar.ingredient.domain.po.CostEstimation">
        SELECT
            id,
            estimation_no,
            user_id,
            username,
            company_name,
            steel_number,
            finished_product_specification,
            estimated_order_quantity,
            quotation_or_cost_estimation,
            forged_material,
            change_standard_cost,
            length_delivery_status,
            surface_delivery_status,
            heat_delivery_status,
            technical_standard,
            process_route,
            approve_status,
            sub_status,
            department_id,
            processinstanceid,
            created_by,
            update_by,
            update_time,
            created_time,
            num,
            contract_id
        FROM cost_estimation
        WHERE id = #{l}
    </select>
</mapper>
