package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 模板与设备关联表
 * @TableName template_equipment_mapping
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TemplateEquipmentMappingVO extends BaseVO {
    /**
     * 模板ID
     */
    @TableId
    private Long templateId;

    /**
     * 设备ID
     */
    @TableId
    private Long equipmentId;



}