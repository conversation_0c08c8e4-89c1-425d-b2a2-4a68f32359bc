package com.nercar.ingredient.domain.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 计算结果表
 * @TableName calculation_result
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CalculationResultDTO extends BaseDTO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 主表ID
     */
    private Long standardIngredientRecordId;

    /**
     * 使用的原料ID
     */
    private Long rawMaterialId;

    /**
     * 原料名称
     */
    private String rawMaterialName;

    /**
     * 目标成分ID
     */
    private Long purposeCompositionId;

    /**
     * 品位%
     */
    private BigDecimal composition;

    /**
     * 收得率%
     */
    private BigDecimal recoveryRate;

    /**
     * 配入量%
     */
    private BigDecimal wieght;

    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 单耗
     */
    private String singleConsume;
    /**
     * 单耗
     */
    private Float unit_consumption_tons;

    /**
     * 计算序号
     */
    private String calculationSequence;

    /**
     *
     */
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    private String createuser;

    /**
     *
     */
    @TableField(value = "createtime",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     *
     */
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;



}