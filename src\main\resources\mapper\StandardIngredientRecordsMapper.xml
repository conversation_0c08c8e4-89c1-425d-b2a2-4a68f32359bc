<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.StandardIngredientRecordsMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.StandardIngredientRecords">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userName" column="user_name" jdbcType="VARCHAR"/>
            <result property="departmentId" column="department_id" jdbcType="BIGINT"/>
            <result property="steelGradeId" column="steel_grade_id" jdbcType="BIGINT"/>
            <result property="calculationProcessNo" column="calculation_process_no" jdbcType="VARCHAR"/>
            <result property="executionStandardId" column="execution_standard_id" jdbcType="BIGINT"/>
            <result property="rawMaterialTotal" column="raw_material_total" jdbcType="NUMERIC"/>
            <result property="costPrice" column="cost_price" jdbcType="NUMERIC"/>
            <result property="mixingDate" column="mixing_date" jdbcType="TIMESTAMP"/>
            <result property="releaseDate" column="release_date" jdbcType="TIMESTAMP"/>
            <result property="category" column="category" jdbcType="VARCHAR"/>
            <result property="calculationResultId" column="calculation_result_id" jdbcType="BIGINT"/>
            <result property="processPathId" column="process_path_id" jdbcType="BIGINT"/>
            <result property="specialNotes" column="special_notes" jdbcType="VARCHAR"/>
            <result property="costEstimattionId" column="cost_estimattion_id" jdbcType="BIGINT"/>
            <result property="createuser" column="createuser" jdbcType="VARCHAR"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_name,department_id,
        steel_grade_id,calculation_process_no,execution_standard_id,
        raw_material_total,cost_price,mixing_date,
        release_date,category,calculation_result_id,
        process_path_id,special_notes,cost_estimattion_id,
        createuser,createtime,updatetime
    </sql>
    <select id="search" resultType="com.nercar.ingredient.domain.vo.StandardIngredientRecordsVO">
        select
        <include refid="Base_Column_List"/>
        from standard_ingredient_records
        ${ew.customSqlSegment}
    </select>
    <select id="selectRecords" resultType="com.nercar.ingredient.domain.vo.StandardIngredientRecordsVO">
        select
            air.id,
            air.status,
            air.user_name,
            air.department_id,
            air.steel_grade_id,
            air.calculation_process_no,
            air.execution_standard_id,
            air.raw_material_total,
            air.cost_price,
            air.mixing_date,
            air.release_date,
            air.category,
            air.calculation_result_id,
            air.process_path_id,
            air.special_notes,
            air.cost_estimattion_id,
            air.createuser,
            air.createtime,
            air.updatetime,
            sg.steel_grade as steelGrade,
            es.standard_name as standardName
        from standard_ingredient_records as air
        left join steel_grades as sg on air.steel_grade_id = sg.id
        left join execution_standard as es on air.execution_standard_id = es.id
        ${ew.customSqlSegment}
    </select>
    <select id="selectMaterials" resultType="com.nercar.ingredient.domain.vo.StandardRawMaterialsVO">
        select
            srm.id,
            srm.name,
            srm.composition,
            srm.yield_rate,
            srm.price,
            cr.wieght
        from calculation_result as cr
        left join standard_raw_materials as srm on cr.raw_material_id = srm.id
        where cr.id=#{calculationResultId}
    </select>
</mapper>
