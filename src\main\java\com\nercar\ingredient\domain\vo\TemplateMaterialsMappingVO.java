package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 配料模板与原料关联表
 * @TableName template_materials_mapping
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TemplateMaterialsMappingVO extends BaseVO {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 模板ID
     */
    private Long standardTemplateId;

    /**
     * 原料ID
     */
    private Long rawMaterialId;

    /**
     * 
     */
    private String createuser;

    /**
     * 
     */
    private Date createtime;

    /**
     * 
     */
    private Date updatetime;



}