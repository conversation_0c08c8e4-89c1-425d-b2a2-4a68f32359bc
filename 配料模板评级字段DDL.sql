-- 配料模板评级字段DDL脚本
-- 执行时间：2025-01-14

-- 1. 备份现有数据
CREATE TABLE standard_template_backup_20250114 AS 
SELECT * FROM standard_template;

-- 2. 新增评级字段
ALTER TABLE standard_template 
ADD COLUMN rating INTEGER DEFAULT 3 NOT NULL;

-- 3. 添加字段注释
COMMENT ON COLUMN standard_template.rating IS '模板评级：1-5星级，默认3星';

-- 4. 验证字段添加
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'standard_template' 
AND column_name = 'rating';

-- 5. 根据创建时间设置初始评级（安全更新）
UPDATE standard_template 
SET rating = CASE 
    WHEN createtime > '2024-01-01' THEN 4  -- 新模板默认4星
    ELSE 3  -- 老模板默认3星
END
WHERE id IS NOT NULL;  -- 添加WHERE条件确保安全

-- 6. 验证数据更新结果
SELECT rating, COUNT(*) as count 
FROM standard_template 
GROUP BY rating 
ORDER BY rating;

-- 7. 查看更新后的数据样例
SELECT id, path_name, rating, createtime 
FROM standard_template 
ORDER BY rating DESC, createtime DESC 
LIMIT 10;

-- 8. 为评级字段创建索引（可选，用于排序优化）
CREATE INDEX idx_standard_template_rating ON standard_template(rating);

-- 验证完成
SELECT 'DDL执行完成' as status;
