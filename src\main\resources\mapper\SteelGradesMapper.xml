<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.SteelGradesMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.SteelGrades">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="steelGrade" column="steel_grade" jdbcType="VARCHAR"/>
            <result property="createuser" column="createuser" jdbcType="VARCHAR"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,steel_grade,createuser,
        createtime,updatetime
    </sql>
    <select id="searchProcessPath" resultType="com.nercar.ingredient.domain.vo.ProcessPathVO">
        select pp.id,sg.steel_grade,pp.path_name,pp.frequence from steel_grades as sg
        left join process_path as pp on sg.id = pp.steel_grades_id
        ${ew.customSqlSegment}
    </select>
</mapper>
