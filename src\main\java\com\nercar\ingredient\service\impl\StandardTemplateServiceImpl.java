package com.nercar.ingredient.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.dto.StandardTemplatePageDTO;
import com.nercar.ingredient.domain.dto.StandardTemplateQueryDTO;
import com.nercar.ingredient.domain.po.ProductionEquipments;
import com.nercar.ingredient.domain.po.StandardRawMaterials;
import com.nercar.ingredient.domain.po.StandardTemplate;
import com.nercar.ingredient.domain.po.TemplateMaterialsMapping;
import com.nercar.ingredient.domain.vo.StandardTemplateVO;
import com.nercar.ingredient.mapper.ProductionEquipmentsMapper;
import com.nercar.ingredient.mapper.StandardTemplateMapper;
import com.nercar.ingredient.service.StandardRawMaterialsService;
import com.nercar.ingredient.service.StandardTemplateService;
import com.nercar.ingredient.service.TemplateMaterialsMappingService;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【standard_template(标准配料模版表)】的数据库操作Service实现
 * @createDate 2025-04-01 13:55:28
 */
@Service
public class StandardTemplateServiceImpl extends ServiceImpl<StandardTemplateMapper, StandardTemplate> implements StandardTemplateService {
    @Resource
    private TemplateMaterialsMappingService templateMaterialsMappingService;

    @Resource
    private StandardRawMaterialsService standardRawMaterialsService;
    @Override
    public IPage<StandardRawMaterials> getStandardRawMaterialsInfo(StandardTemplatePageDTO standardTemplatePageDTO) {
        // 1. 获取已映射的原材料 ID 列表
        List<TemplateMaterialsMapping> mappings = templateMaterialsMappingService.lambdaQuery()
                .eq(TemplateMaterialsMapping::getStandardTemplateId, Long.parseLong(standardTemplatePageDTO.getStandardTemplateId()))
                .list();

        if (mappings.isEmpty()) {
            // 返回空分页
            return new Page<>(standardTemplatePageDTO.getPageNo(), standardTemplatePageDTO.getPageSize(), 0);
        }

        List<Long> rawMaterialIds = mappings.stream()
                .map(TemplateMaterialsMapping::getRawMaterialId)
                .collect(Collectors.toList());

        // 2. 构建分页参数
        Page<StandardRawMaterials> page = new Page<>(standardTemplatePageDTO.getPageNo(), standardTemplatePageDTO.getPageSize());

        // 3. 构建查询条件
        LambdaQueryWrapper<StandardRawMaterials> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StandardRawMaterials::getId, rawMaterialIds);

        if (StringUtils.isNotEmpty(standardTemplatePageDTO.getName())) {
            queryWrapper.like(StandardRawMaterials::getName, standardTemplatePageDTO.getName());
        }

        // 4. 执行分页查询
        return standardRawMaterialsService.page(page, queryWrapper);
    }


    @Autowired
    private ProductionEquipmentsMapper productionEquipmentsMapper;
    @Override
    public IPage<StandardRawMaterials> getStandardRawMaterialsSaveInfo(StandardTemplatePageDTO standardTemplatePageDTO) {
        // 查询已关联的原材料 ID
        List<TemplateMaterialsMapping> templateMaterialsMappings = templateMaterialsMappingService.lambdaQuery()
                .eq(TemplateMaterialsMapping::getStandardTemplateId, Long.parseLong(standardTemplatePageDTO.getStandardTemplateId()))
                .list();

        List<Long> excludeIds = templateMaterialsMappings.stream()
                .map(TemplateMaterialsMapping::getRawMaterialId)
                .collect(Collectors.toList());

        // 构建分页参数
        Page<StandardRawMaterials> page = new Page<>(standardTemplatePageDTO.getPageNo(), standardTemplatePageDTO.getPageSize());

        // 构建查询条件
        LambdaQueryWrapper<StandardRawMaterials> queryWrapper = new LambdaQueryWrapper<>();

        if (!excludeIds.isEmpty()) {
            queryWrapper.notIn(StandardRawMaterials::getId, excludeIds);
        }

        if (StringUtils.isNotEmpty(standardTemplatePageDTO.getName())) {
            queryWrapper.like(StandardRawMaterials::getName, standardTemplatePageDTO.getName());
        }

        // 执行分页查询
        Page<StandardRawMaterials> page1 = standardRawMaterialsService.page(page, queryWrapper);
        //遍历所有的原料，根据原料中的设备id去设备表中找到设备名称，并赋值给原料对象
        for (StandardRawMaterials record : page1.getRecords()) {
            Long productionEquipmentId = record.getProductionEquipmentId();
            if (productionEquipmentId != null) {
                LambdaQueryWrapper<ProductionEquipments> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ProductionEquipments::getId, productionEquipmentId);
                ProductionEquipments productionEquipment = productionEquipmentsMapper.selectOne(wrapper);
                if (productionEquipment != null) {
                    record.setProductionEquipmentName(productionEquipment.getEquipmentName());
                }
//                record.setProductionEquipmentId(productionEquipmentId);
            }
        }
        //重新封装数据返回
        return page1;
//        return null;
    }

    @Override
    public IPage<StandardTemplateVO> getStandardTemplateWithPaging(StandardTemplateQueryDTO queryDTO) {
        // 构建分页参数
        Page<StandardTemplate> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());

        // 构建查询条件
        LambdaQueryWrapper<StandardTemplate> wrapper = new LambdaQueryWrapper<>();

        // 路径名称模糊查询
        if (StringUtils.isNotEmpty(queryDTO.getPathName())) {
            wrapper.like(StandardTemplate::getPathName, queryDTO.getPathName());
        }

        // 部门ID精确查询
        if (queryDTO.getDepartmentId() != null) {
            wrapper.eq(StandardTemplate::getDepartmentId, queryDTO.getDepartmentId());
        }

        // 部门名称模糊查询
        if (StringUtils.isNotEmpty(queryDTO.getDepartmentName())) {
            wrapper.like(StandardTemplate::getDepartmentName, queryDTO.getDepartmentName());
        }

        // 评级精确查询
        if (queryDTO.getRating() != null) {
            wrapper.eq(StandardTemplate::getRating, queryDTO.getRating());
        }

        // 评级范围查询
        if (queryDTO.getMinRating() != null) {
            wrapper.ge(StandardTemplate::getRating, queryDTO.getMinRating());
        }
        if (queryDTO.getMaxRating() != null) {
            wrapper.le(StandardTemplate::getRating, queryDTO.getMaxRating());
        }

        // 创建人查询
        if (StringUtils.isNotEmpty(queryDTO.getCreateuser())) {
            wrapper.like(StandardTemplate::getCreateuser, queryDTO.getCreateuser());
        }

        // 排序处理
        String sortField = queryDTO.getSortField();
        String sortOrder = queryDTO.getSortOrder();

        if ("rating".equals(sortField)) {
            if ("asc".equals(sortOrder)) {
                wrapper.orderByAsc(StandardTemplate::getRating);
            } else {
                wrapper.orderByDesc(StandardTemplate::getRating);
            }
        } else if ("createtime".equals(sortField)) {
            if ("asc".equals(sortOrder)) {
                wrapper.orderByAsc(StandardTemplate::getCreatetime);
            } else {
                wrapper.orderByDesc(StandardTemplate::getCreatetime);
            }
        } else if ("updatetime".equals(sortField)) {
            if ("asc".equals(sortOrder)) {
                wrapper.orderByAsc(StandardTemplate::getUpdatetime);
            } else {
                wrapper.orderByDesc(StandardTemplate::getUpdatetime);
            }
        } else {
            // 默认按评级降序排序
            wrapper.orderByDesc(StandardTemplate::getRating);
        }

        // 执行分页查询
        IPage<StandardTemplate> result = this.page(page, wrapper);

        // 转换为VO对象
        List<StandardTemplateVO> voList = BeanUtil.copyToList(result.getRecords(), StandardTemplateVO.class);

        // 构建返回结果
        Page<StandardTemplateVO> voPage = new Page<>(result.getCurrent(), result.getSize(), result.getTotal());
        voPage.setRecords(voList);

        return voPage;
    }

}




