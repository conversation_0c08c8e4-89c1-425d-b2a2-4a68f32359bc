package com.nercar.ingredient.domain.dto;


import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PathMaterialsQueryDTO extends BaseDTO{


    /**
     * 路径ID
     */
    private Long pathId;

    /**
     * 原料ID集合
     */
    private List<Long> rawMaterialIds;
}
