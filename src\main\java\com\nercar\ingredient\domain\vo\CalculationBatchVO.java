package com.nercar.ingredient.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 计算批次VO
 * 用于表示一次计算的所有结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "计算批次VO")
public class CalculationBatchVO {

    /**
     * 计算序号
     */
    @Schema(description = "计算序号")
    private String calculationSequence;

    /**
     * 计算时间
     */
    @Schema(description = "计算时间")
    private LocalDateTime calculationTime;

    /**
     * 批次内的计算结果列表
     */
    @Schema(description = "批次内的计算结果列表")
    private List<CalculationBatchResultVO> results;

    /**
     * 批次备注（可选）
     */
    @Schema(description = "批次备注")
    private String batchNotes;
}
