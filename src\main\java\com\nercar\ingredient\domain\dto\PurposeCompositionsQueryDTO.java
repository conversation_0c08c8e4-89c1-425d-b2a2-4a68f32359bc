package com.nercar.ingredient.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PurposeCompositionsQueryDTO {

    @Schema(description = "钢种")
    private String steelGrade;

    @Schema(description = "标准名称")
    private String standardName;

    @Schema(description = "标准配料ID")
    private Long standardIngredientRecordId;
    /**
     * 元素名称
     */
    @Schema(description = "元素名称")
    private String elementName;

    /**
     * 最小值
     */
    @Schema(description = "最小值")
    private BigDecimal minValue;

    /**
     * 最大值
     */
    @Schema(description = "最大值")
    private BigDecimal maxValue;

    /**
     * 平均值（目标值）
     */
    @Schema(description = "平均值（目标值）")
    private BigDecimal averageValue;

    /**
     * 数学符号
     */
    @Schema(description = "数学符号")
    private String code;

    @Schema(description = "元素列表")
    private List<StandardCompositionsQueryDTO> elementList;

}
