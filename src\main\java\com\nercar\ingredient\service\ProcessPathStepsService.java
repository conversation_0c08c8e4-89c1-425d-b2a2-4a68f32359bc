package com.nercar.ingredient.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.ingredient.domain.dto.PathSaveDTO;
import com.nercar.ingredient.domain.po.ProcessPathSteps;
import com.nercar.ingredient.domain.vo.ProcessPathVO;
import com.nercar.ingredient.mapper.ProcessPathStepsMapper;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【process_path_steps(工序表)】的数据库操作Service
* @createDate 2025-04-01 13:55:28
*/

public interface ProcessPathStepsService extends IService<ProcessPathSteps> {


    List<PathSaveDTO> selectProcessPathSteps(ProcessPathVO processPathVO);
}
