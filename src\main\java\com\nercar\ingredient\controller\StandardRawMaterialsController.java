package com.nercar.ingredient.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.nercar.ingredient.domain.dto.*;
import com.nercar.ingredient.domain.po.StandardRawMaterials;
import com.nercar.ingredient.domain.po.StandardRawMaterialsPlus;
import com.nercar.ingredient.domain.vo.StandardRawMaterialsVO;
import com.nercar.ingredient.mapper.ChemicalElementsMapper;
import com.nercar.ingredient.response.PageDataResult;
import com.nercar.ingredient.response.Result;
import com.nercar.ingredient.service.StandardRawMaterialsPlusService;
import com.nercar.ingredient.service.StandardRawMaterialsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Tag(name = "标准原料接口")
@RestController
@RequestMapping("/standardRawMaterials")
public class StandardRawMaterialsController {

    @Autowired
    private StandardRawMaterialsService standardRawMaterialsService;
    @Autowired
    private StandardRawMaterialsPlusService standardRawMaterialsPlusService;


    @Operation(summary = "分页查询标准原料-Plus")
    @PostMapping("/getStandardRawMaterialsPlus")
    public PageDataResult<StandardRawMaterialsPlus> getStandardRawMaterialsPlus(@RequestBody StandardRawMaterialsQueryDTO standardRawMaterialsQueryDTO) {
        log.info("分页查询标准原料");
        IPage< StandardRawMaterialsPlus> result =standardRawMaterialsPlusService.selectStandardRawMaterials(standardRawMaterialsQueryDTO);
        List<StandardRawMaterialsPlus> records = result.getRecords();
        long total = result.getTotal();
        if (ObjectUtils.isEmpty(records)) {
            log.warn("未找到符合给定条件的标准原料"); // 添加日志记录
            return PageDataResult.success(null, 0);
        }
        return PageDataResult.success(records, total);
    }
    @Operation(summary = "分页查询标准原料")
    @PostMapping("/getStandardRawMaterials")
    public PageDataResult<StandardRawMaterialsVO> getStandardRawMaterials(@RequestBody StandardRawMaterialsQueryDTO standardRawMaterialsQueryDTO) {
        log.info("分页查询标准原料");
        IPage< StandardRawMaterialsVO> result =standardRawMaterialsService.selectStandardRawMaterials(standardRawMaterialsQueryDTO);
        List<StandardRawMaterialsVO> records = result.getRecords();
        long total = result.getTotal();
        if (ObjectUtils.isEmpty(records)) {
            log.warn("未找到符合给定条件的标准原料"); // 添加日志记录
            return PageDataResult.success(null, 0);
        }
        return PageDataResult.success(records, total);
    }

    @Operation(summary = "新增原料-Plus")
    @PostMapping("/saveStandardRawMaterialsPlus")
    public Result<String> saveStandardRawMaterialsPlus(@RequestBody StandardRawMaterialsPlusDTO standardRawMaterialsPlusDTO) {
        log.info("新增原料");
        boolean result = standardRawMaterialsPlusService.insertOrUpdate(standardRawMaterialsPlusDTO);
        if (!result) {
            log.warn("新增原料失败");
            return Result.failed("新增原料失败");
        }
        return Result.success("新增原料成功");
    }

    @Operation(summary = "新增原料")
    @PostMapping("/saveStandardRawMaterials")
    public Result<String> saveStandardRawMaterials(@RequestBody StandardRawMaterialsDTO standardRawMaterialsDTO) {
        log.info("新增原料");
        boolean result = standardRawMaterialsService.insertOrUpdate(standardRawMaterialsDTO);
        if (!result) {
            log.warn("新增原料失败");
            return Result.failed("新增原料失败");
        }
        return Result.success("新增原料成功");
    }

    @Operation(summary = "查看原料详情-Plus")
    @GetMapping("/getStandardRawMaterialsPlus/{id}")
    public Result<StandardRawMaterialsPlusDTO> getStandardRawMaterialsPlus(@PathVariable String id) {
        log.info("查看原料详情");
        StandardRawMaterialsPlusDTO standardRawMaterialsPlusDTO = standardRawMaterialsPlusService.selectById(id);
        if (ObjectUtils.isEmpty(standardRawMaterialsPlusDTO)) {
            log.warn("未找到符合给定条件的标准原料");
            return Result.failed("未找到符合给定条件的标准原料");
        }
        return Result.success(standardRawMaterialsPlusDTO);
    }

    @Operation(summary = "查看原料详情")
    @GetMapping("/getStandardRawMaterials/{id}")
    public Result<StandardRawMaterialsVO> getStandardRawMaterials(@PathVariable Long id) {
        log.info("查看原料详情");
        StandardRawMaterialsVO standardRawMaterialsVO = standardRawMaterialsService.selectById(id);
        if (ObjectUtils.isEmpty(standardRawMaterialsVO)) {
            log.warn("未找到符合给定条件的标准原料");
            return Result.failed("未找到符合给定条件的标准原料");
        }
        return Result.success(standardRawMaterialsVO);
    }


    @Operation(summary = "编辑修改原料-Plus")
    @PostMapping("/updateStandardRawMaterialsPlus")
    public Result<String> updateStandardRawMaterialsPlus(@RequestBody StandardRawMaterialsPlusDTO standardRawMaterialsPlusDTO) {
        log.info("编辑修改原料");
        boolean result = standardRawMaterialsPlusService.insertOrUpdate(standardRawMaterialsPlusDTO);
        if (!result) {
            log.warn("编辑修改原料失败");
            return Result.failed("编辑修改原料失败");
        }
        return Result.success("编辑修改原料成功");
    }

    @Operation(summary = "编辑修改原料")
    @PostMapping("/updateStandardRawMaterials")
    public Result<String> updateStandardRawMaterials(@RequestBody StandardRawMaterialsDTO standardRawMaterialsDTO) {
        log.info("编辑修改原料");
        boolean result = standardRawMaterialsService.insertOrUpdate(standardRawMaterialsDTO);
        if (!result) {
            log.warn("编辑修改原料失败");
            return Result.failed("编辑修改原料失败");
        }
        return Result.success("编辑修改原料成功");
    }

    @Autowired
    private ChemicalElementsMapper chemicalElementsMapper;

    @Operation(summary = "选择元素-Plus")
    @GetMapping( "/selectElementNames")
    public Result<List<String>> selectElementNames() {
        List<String> list=chemicalElementsMapper.selectList(null).stream().map(chemicalElements -> chemicalElements.getElementSymbol()).toList();
        if (ObjectUtils.isEmpty(list)) {
            log.warn("未找到符合给定条件的元素"); // 添加日志记录
            return Result.ok();
        }
        return Result.ok(list);
    }


    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "删除-Plus")
    @GetMapping("/deleteStandardRawMaterialsPlus/{id}")
    public Result<Boolean> deleteStandardRawMaterialsPlus(@PathVariable String id) {
        if (StringUtils.isEmpty(id)) {
            return Result.failed("id不能为空");
        }
        return Result.success(standardRawMaterialsPlusService.deleteById(id));
    }

    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "删除")
    @GetMapping("/deleteStandardRawMaterials/{id}")
    public Result<Boolean> deleteStandardRawMaterials(@PathVariable String id) {
        if (StringUtils.isEmpty(id)) {
            return Result.failed("id不能为空");
        }
        return Result.success(standardRawMaterialsService.removeById(Long.parseLong(id)));
    }

    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "批量导入")
    @PostMapping("/importStandardRawMaterials")
    public Result<Boolean> saveBatch(@RequestBody StandardRawMaterialsSaveBatchDTO standardRawMaterialsSaveBatchDTO) {
         boolean res=standardRawMaterialsService.saveBatch(standardRawMaterialsSaveBatchDTO.getStandardRawMaterialsList());
        return Result.success(res);
    }


    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "批量导入-Plus")
    @PostMapping("/importStandardRawMaterialsPlus")
    public Result<Boolean> saveBatchPlus(@RequestBody StandardRawMaterialsSaveBatchPlusDTO standardRawMaterialsSaveBatchPlusDTO) {
        boolean res=standardRawMaterialsPlusService.saveBatch(standardRawMaterialsSaveBatchPlusDTO.getStandardRawMaterialsPlusList());
        return Result.success(res);
    }
}
