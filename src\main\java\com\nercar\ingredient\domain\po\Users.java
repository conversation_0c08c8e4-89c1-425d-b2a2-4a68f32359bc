package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 用户表
 * @TableName users
 */
@TableName(value ="users")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Users extends BaseEntity {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 工号
     */
    private String account;

    /**
     * 用户名
     */
    private String userName;


    private String nickName;
    /**
     * 密码
     */
//    @TableField("password")
    private String passWord;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 配料人
     */
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    private String createuser;

    /**
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "createtime", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime createtime;

    /**
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatetime;



}