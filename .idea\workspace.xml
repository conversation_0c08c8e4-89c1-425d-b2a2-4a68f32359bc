<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="4c084168-b3d2-4f45-965e-6703e7baba52" name="Changes" comment="4.27">
      <change afterPath="$PROJECT_DIR$/public714.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public728.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public729.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/config/ContractReviewConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/config/ImageServerConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/config/RedisConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/config/SaTokenConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/controller/DataFixController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/StandardTemplateQueryDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/ContractReviewOptionVO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/excel/listener/MaterialYieldRatesImportListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/com/nercar/ingredient/domain/vo/ContractReviewInfoVOTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/start.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/历史配料查询优化索引.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/合同评审分页查询功能增强测试脚本.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/权限控制功能说明.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/设备分类功能实现总结.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/设备分类功能验证脚本.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/配料模板评级功能实现总结.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/配料模板评级功能验证脚本.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/配料模板评级字段DDL.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/AugmentWebviewStateStore.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/MarsCodeWorkspaceAppSettings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/MarsCodeWorkspaceAppSettings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/dataSources.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/dataSources.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/config/JacksonConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/config/JacksonConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/config/MybatisPlusConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/config/MybatisPlusConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/config/WebConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/config/WebConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/constant/SecurityConstants.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/constant/SecurityConstants.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/controller/BasicInfoController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/controller/BasicInfoController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/controller/IngredientCalculationController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/controller/IngredientCalculationController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/controller/MaterialYieldRatesController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/controller/MaterialYieldRatesController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/controller/StandardTemplateController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/controller/StandardTemplateController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/controller/UserController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/controller/UserController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/CalculationIdAndTokenDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/CalculationIdAndTokenDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/CalculationQueryDataDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/CalculationQueryDataDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/CalculationResultDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/CalculationResultDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/IngotYieldRatesDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/IngotYieldRatesDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/MaterialYieldRatesDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/MaterialYieldRatesDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/PathSaveDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/PathSaveDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/ProductionEquipmentsDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/ProductionEquipmentsDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/PurposeCompositionsQueryDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/PurposeCompositionsQueryDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/StandardCompositionsQueryDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/StandardCompositionsQueryDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/StandardRawMaterialsDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/StandardRawMaterialsDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/StandardRawMaterialsPlusDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/StandardRawMaterialsPlusDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/StandardTemplateDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/StandardTemplateDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/SteelGradesDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/SteelGradesDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/TemplateMaterialsMappingSaveBatchDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/dto/TemplateMaterialsMappingSaveBatchDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/CalculationResult.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/CalculationResult.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/CostEstimation.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/CostEstimation.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/IngotYieldRates.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/IngotYieldRates.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/MaterialYieldRates.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/MaterialYieldRates.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/ProductionEquipments.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/ProductionEquipments.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/PurposeCompositions.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/PurposeCompositions.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/StandardIngredientRecords.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/StandardIngredientRecords.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/StandardRawMaterials.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/StandardRawMaterials.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/StandardRawMaterialsPlus.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/StandardRawMaterialsPlus.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/StandardTemplate.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/po/StandardTemplate.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/AllDataVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/AllDataVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/CalculationResultDataVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/CalculationResultDataVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/CalculationResultVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/CalculationResultVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/IngotYieldRatesVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/IngotYieldRatesVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/MaterialYieldRatesVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/MaterialYieldRatesVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/ProductionEquipmentsVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/ProductionEquipmentsVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/PurposeCompositionsVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/PurposeCompositionsVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/StandardCompositionsVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/StandardCompositionsVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/StandardRawMaterialsVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/StandardRawMaterialsVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/StandardTemplateVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/domain/vo/StandardTemplateVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/excel/MaterialYieldRatesExcel.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/excel/MaterialYieldRatesExcel.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/excel/StandardRawMaterialsExcel.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/excel/StandardRawMaterialsExcel.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/interceptor/TokenInterceptor.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/interceptor/TokenInterceptor.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/mapper/CalculationResultMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/mapper/CalculationResultMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/MaterialYieldRatesService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/MaterialYieldRatesService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/StandardIngredientRecordsService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/StandardIngredientRecordsService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/StandardTemplateService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/StandardTemplateService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/UsersService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/UsersService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/CalculationResultServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/CalculationResultServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/MaterialYieldRatesServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/MaterialYieldRatesServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/ProcessPathServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/ProcessPathServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/ProcessPathStepsServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/ProcessPathStepsServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/PurposeCompositionsServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/PurposeCompositionsServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/StandardCompositionsServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/StandardCompositionsServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/StandardIngredientRecordsServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/StandardIngredientRecordsServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/StandardRawMaterialsPlusServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/StandardRawMaterialsPlusServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/StandardRawMaterialsServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/StandardRawMaterialsServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/StandardTemplateServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/StandardTemplateServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/UsersServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/nercar/ingredient/service/impl/UsersServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-prod.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-prod.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/logback.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/logback.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/CalculationResultMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/CalculationResultMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/CostEstimationMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/CostEstimationMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/ProductionEquipmentsMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/ProductionEquipmentsMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/StandardRawMaterialsMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/StandardRawMaterialsMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/StandardRawMaterialsPlusMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/StandardRawMaterialsPlusMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/StandardTemplateMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/StandardTemplateMapper.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="dev2" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="G:\developSoftvare\apache-maven-3.9.4" />
        <option name="localRepository" value="G:\developSoftvare\apache-maven-3.9.4\mvn_repo" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="G:\developSoftvare\apache-maven-3.9.4\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="explicitlyEnabledProfiles" value="prod" />
    <option name="explicitlyDisabledProfiles" value="dev" />
  </component>
  <component name="MavenRunner">
    <option name="jreName" value="17" />
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2v76HF23FKS5vVVDLY0dg0t9dan" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "ApiPost:METDOD_SEND_RECORD:fushun-ingredient": "{\"/dataFix/syncTargetValuesToOldTableOptimized\":[{\"url\":\"/dataFix/syncTargetValuesToOldTableOptimized\",\"header\":[{\"value\":\"\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"value\":\"\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"rest\":[{\"value\":\"\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"requestBody\":\"\",\"responseBody\":\"There was an error accessing to URL: http:///dataFix/syncTargetValuesToOldTableOptimized\\n\\nConnectException: Connection refused: connect\",\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":5.0,\"day\":30.0},\"time\":{\"hour\":8.0,\"minute\":28.0,\"second\":3.0,\"nano\":1.0023E7}}},{\"url\":\"/dataFix/syncTargetValuesToOldTableOptimized\",\"header\":[{\"value\":\"\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"value\":\"\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"rest\":[{\"value\":\"\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"requestBody\":\"\",\"responseBody\":\"There was an error accessing to URL: http:///dataFix/syncTargetValuesToOldTableOptimized\\n\\nConnectException: Connection refused: connect\",\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":5.0,\"day\":30.0},\"time\":{\"hour\":8.0,\"minute\":28.0,\"second\":43.0,\"nano\":8.497268E8}}},{\"url\":\"/dataFix/syncTargetValuesToOldTableOptimized\",\"header\":[{\"value\":\"\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"value\":\"\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"rest\":[{\"value\":\"\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"requestBody\":\"\",\"responseBody\":\"There was an error accessing to URL: http:///dataFix/syncTargetValuesToOldTableOptimized\\n\\nConnectException: Connection refused: connect\",\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":5.0,\"day\":30.0},\"time\":{\"hour\":8.0,\"minute\":28.0,\"second\":51.0,\"nano\":1.436049E8}}},{\"url\":\"/dataFix/syncTargetValuesToOldTableOptimized\",\"header\":[{\"value\":\"\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"value\":\"\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"rest\":[{\"value\":\"\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"requestBody\":\"\",\"responseBody\":\"There was an error accessing to URL: http:///dataFix/syncTargetValuesToOldTableOptimized\\n\\nConnectException: Connection refused: connect\",\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":5.0,\"day\":30.0},\"time\":{\"hour\":8.0,\"minute\":28.0,\"second\":51.0,\"nano\":3.725084E8}}}],\"/dataFix/checkDataConsistency\":[{\"url\":\"http://localhost:8080/dataFix/checkDataConsistency\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8080/dataFix/checkDataConsistency\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":5.0,\"day\":30.0},\"time\":{\"hour\":8.0,\"minute\":30.0,\"second\":30.0,\"nano\":2.899266E8}}},{\"url\":\"http://localhost:8080/dataFix/checkDataConsistency\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8080/dataFix/checkDataConsistency\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":5.0,\"day\":30.0},\"time\":{\"hour\":8.0,\"minute\":30.0,\"second\":46.0,\"nano\":1.332334E8}}},{\"url\":\"http://localhost:9021/dataFix/checkDataConsistency\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:9021/dataFix/checkDataConsistency\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":5.0,\"day\":30.0},\"time\":{\"hour\":8.0,\"minute\":31.0,\"second\":16.0,\"nano\":3.059869E8}}},{\"url\":\"http://localhost:9021/dataFix/checkDataConsistency\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:9021/dataFix/checkDataConsistency\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":5.0,\"day\":30.0},\"time\":{\"hour\":8.0,\"minute\":31.0,\"second\":17.0,\"nano\":1.610933E8}}},{\"url\":\"http://localhost:9021/dataFix/checkDataConsistency\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:9021/dataFix/checkDataConsistency\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":5.0,\"day\":30.0},\"time\":{\"hour\":8.0,\"minute\":31.0,\"second\":17.0,\"nano\":5.855156E8}}},{\"url\":\"http://localhost:9021/dataFix/checkDataConsistency\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:9021/dataFix/checkDataConsistency\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":5.0,\"day\":30.0},\"time\":{\"hour\":8.0,\"minute\":31.0,\"second\":48.0,\"nano\":7.846781E8}}},{\"url\":\"http://localhost:8080/dataFix/checkDataConsistency\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8080/dataFix/checkDataConsistency\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":5.0,\"day\":30.0},\"time\":{\"hour\":8.0,\"minute\":32.0,\"second\":10.0,\"nano\":5.837636E8}}},{\"url\":\"http://localhost:9021/dataFix/checkDataConsistency\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:9021/dataFix/checkDataConsistency\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":5.0,\"day\":30.0},\"time\":{\"hour\":8.0,\"minute\":32.0,\"second\":41.0,\"nano\":8.731846E8}}},{\"url\":\"http://localhost:7104/dataFix/checkDataConsistency\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:7104/dataFix/checkDataConsistency\\n\\n{\\\"code\\\":401,\\\"msg\\\":\\\"未提供认证token\\\"}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 401\"],\"Keep-Alive\":[\"timeout\\u003d60\"],\"Connection\":[\"keep-alive\"],\"Content-Length\":[\"41\"],\"Date\":[\"Fri, 30 May 2025 00:32:47 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":5.0,\"day\":30.0},\"time\":{\"hour\":8.0,\"minute\":32.0,\"second\":47.0,\"nano\":9.563398E8}}},{\"url\":\"http://localhost:9021/dataFix/checkDataConsistency\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:9021/dataFix/checkDataConsistency\\n\\n{\\\"code\\\":401,\\\"msg\\\":\\\"未提供认证token\\\"}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 401\"],\"Keep-Alive\":[\"timeout\\u003d60\"],\"Connection\":[\"keep-alive\"],\"Content-Length\":[\"41\"],\"Date\":[\"Fri, 30 May 2025 00:40:36 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025,\"month\":5,\"day\":30},\"time\":{\"hour\":8,\"minute\":40,\"second\":36,\"nano\":904537300}}}]}",
    "Maven.fushun-ingredient [clean].executor": "Run",
    "Maven.fushun-ingredient [compile].executor": "Run",
    "Maven.fushun-ingredient [install].executor": "Run",
    "Maven.fushun-ingredient [package].executor": "Run",
    "Maven.fushun-ingredient [validate].executor": "Run",
    "Maven.fushun-ingredient [verify].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Spring Boot.Application (1).executor": "Debug",
    "Spring Boot.Application.executor": "Debug",
    "database.data.extractors.current.export.id": "Comma-separated (CSV)_id",
    "git-widget-placeholder": "6da0d0a0",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "G:/fushun/permissionCode/pom.xml",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.15429688",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "MavenSettings",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "postgresql",
      "mysql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\fushun\fushun-ingredient\template" />
      <recent name="E:\fushun\fushun-ingredient\src\main\resources\template" />
      <recent name="E:\fushun\fushun-ingredient\src\main\java\com\nercar\ingredient\domain\bo" />
      <recent name="E:\fushun\fushun-ingredient\src\main\java\com\nercar\ingredient" />
      <recent name="E:\fushun\fushun-ingredient\src\main\java\com\nercar\ingredient\domain" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\fushun\fushun-ingredient\images" />
      <recent name="E:\fushun\fushun-ingredient\src\main\resources\template" />
      <recent name="E:\fushun\fushun-ingredient\src\main\resources" />
      <recent name="E:\fushun\fushun-ingredient" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.nercar.ingredient.config" />
      <recent name="com.nercar.ingredient.controller" />
      <recent name="com.nercar.ingredient.domain.vo" />
      <recent name="com.nercar.ingredient.domain.dto" />
      <recent name="com.nercar.techprocess" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.Application (1)">
    <configuration name="Application (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="fushun-ingredient" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.nercar.ingredient.Application" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.nercar.ingredient.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Application" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="fushun-ingredient" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.nercar.ingredient.Application" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.Application (1)" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="Logical" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="4c084168-b3d2-4f45-965e-6703e7baba52" name="Changes" comment="" />
      <created>1743483677949</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1743483677949</updated>
      <workItem from="1743483679316" duration="439000" />
      <workItem from="1743484216826" duration="1527000" />
      <workItem from="1743486082265" duration="599000" />
      <workItem from="1743611698566" duration="78860000" />
      <workItem from="1744122821161" duration="866000" />
      <workItem from="1744123791841" duration="108154000" />
      <workItem from="1744442356153" duration="76633000" />
      <workItem from="1744713708902" duration="406000" />
      <workItem from="1744940090705" duration="22676000" />
      <workItem from="1744974277882" duration="594000" />
      <workItem from="1744974940861" duration="673000" />
      <workItem from="1744989027725" duration="208000" />
      <workItem from="1744989303476" duration="8637000" />
      <workItem from="1745206571569" duration="1111000" />
      <workItem from="1745207786698" duration="2402000" />
      <workItem from="1745210327992" duration="641000" />
      <workItem from="1745211090992" duration="2315000" />
      <workItem from="1745220059652" duration="18044000" />
      <workItem from="1745293348575" duration="207000" />
      <workItem from="1745293629094" duration="1043000" />
      <workItem from="1745294730038" duration="5746000" />
      <workItem from="1745657587908" duration="746000" />
      <workItem from="1745659302228" duration="2541000" />
      <workItem from="1745715463691" duration="2557000" />
      <workItem from="1745718132245" duration="43239000" />
      <workItem from="1745851948164" duration="3055000" />
      <workItem from="1745890129151" duration="12257000" />
      <workItem from="1745907949513" duration="937000" />
      <workItem from="1745909291998" duration="82000" />
      <workItem from="1745909674397" duration="2277000" />
      <workItem from="1745912900672" duration="2138000" />
      <workItem from="1745933511839" duration="16108000" />
      <workItem from="1746496780426" duration="24541000" />
      <workItem from="1746761563281" duration="2948000" />
      <workItem from="1746953671006" duration="227000" />
      <workItem from="1746953927323" duration="4930000" />
      <workItem from="1746972419574" duration="80000" />
      <workItem from="1746972515416" duration="351000" />
      <workItem from="1746972927912" duration="158000" />
      <workItem from="1746973152595" duration="210000" />
      <workItem from="1746973581887" duration="135000" />
      <workItem from="1746973773279" duration="44000" />
      <workItem from="1746973861290" duration="172000" />
      <workItem from="1746974060615" duration="173000" />
      <workItem from="1746974255392" duration="176000" />
      <workItem from="1746974490565" duration="52000" />
      <workItem from="1746974558450" duration="12453000" />
      <workItem from="1747031276534" duration="5221000" />
      <workItem from="1747126458813" duration="5657000" />
      <workItem from="1747194108293" duration="664000" />
      <workItem from="1747203246626" duration="8239000" />
      <workItem from="1747364614343" duration="13762000" />
      <workItem from="1747548848451" duration="96000" />
      <workItem from="1747615680080" duration="3917000" />
      <workItem from="1747635107690" duration="4314000" />
      <workItem from="1747726597311" duration="620000" />
      <workItem from="1747850698053" duration="727000" />
      <workItem from="1747913338308" duration="705000" />
      <workItem from="1748191968515" duration="13000" />
      <workItem from="1748227312894" duration="6945000" />
      <workItem from="1748418170109" duration="5486000" />
      <workItem from="1748425971615" duration="1318000" />
      <workItem from="1748479987296" duration="151000" />
      <workItem from="1748487667154" duration="16392000" />
      <workItem from="1748564590499" duration="371000" />
      <workItem from="1748564974073" duration="2226000" />
      <workItem from="1749179789972" duration="637000" />
      <workItem from="1751505901492" duration="2093000" />
      <workItem from="1752137141600" duration="710000" />
      <workItem from="1752459087289" duration="28757000" />
      <workItem from="1752652726059" duration="1393000" />
      <workItem from="1752713518626" duration="8472000" />
      <workItem from="1752744987312" duration="9883000" />
      <workItem from="1752801130334" duration="12723000" />
      <workItem from="1753145394745" duration="3568000" />
      <workItem from="1753241670127" duration="697000" />
      <workItem from="1753288546047" duration="962000" />
      <workItem from="1753404137396" duration="3437000" />
      <workItem from="1753664323987" duration="14778000" />
      <workItem from="1753692741057" duration="6848000" />
      <workItem from="1753749554376" duration="6998000" />
      <workItem from="1753768977848" duration="20692000" />
      <workItem from="1753945089230" duration="6640000" />
    </task>
    <task id="LOCAL-00001" summary="初始化">
      <option name="closed" value="true" />
      <created>1743585314073</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1743585314073</updated>
    </task>
    <task id="LOCAL-00002" summary="4.2">
      <option name="closed" value="true" />
      <created>1743608281762</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1743608281762</updated>
    </task>
    <task id="LOCAL-00003" summary="4.8">
      <option name="closed" value="true" />
      <created>1744099910294</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1744099910294</updated>
    </task>
    <task id="LOCAL-00004" summary="4.8.2">
      <option name="closed" value="true" />
      <created>1744102469228</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1744102469228</updated>
    </task>
    <task id="LOCAL-00005" summary="4.9">
      <option name="closed" value="true" />
      <created>1744166068383</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1744166068383</updated>
    </task>
    <task id="LOCAL-00006" summary="4.9">
      <option name="closed" value="true" />
      <created>1744166088246</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1744166088246</updated>
    </task>
    <task id="LOCAL-00007" summary="4.9.2">
      <option name="closed" value="true" />
      <created>1744182255442</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1744182255442</updated>
    </task>
    <task id="LOCAL-00008" summary="4.9.3">
      <option name="closed" value="true" />
      <created>1744189372272</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1744189372272</updated>
    </task>
    <task id="LOCAL-00009" summary="4.9.4">
      <option name="closed" value="true" />
      <created>1744210804988</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1744210804988</updated>
    </task>
    <task id="LOCAL-00010" summary="4.10">
      <option name="closed" value="true" />
      <created>1744248694704</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1744248694704</updated>
    </task>
    <task id="LOCAL-00011" summary="4.10.2">
      <option name="closed" value="true" />
      <created>1744270840348</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1744270840348</updated>
    </task>
    <task id="LOCAL-00012" summary="4.10.3">
      <option name="closed" value="true" />
      <created>1744276717153</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1744276717153</updated>
    </task>
    <task id="LOCAL-00013" summary="4.11">
      <option name="closed" value="true" />
      <created>1744317318865</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1744317318865</updated>
    </task>
    <task id="LOCAL-00014" summary="4.14">
      <option name="closed" value="true" />
      <created>1744622062316</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1744622062316</updated>
    </task>
    <task id="LOCAL-00015" summary="4.14">
      <option name="closed" value="true" />
      <created>1744622575975</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1744622575975</updated>
    </task>
    <task id="LOCAL-00016" summary="4.15">
      <option name="closed" value="true" />
      <created>1744710371994</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1744710371994</updated>
    </task>
    <task id="LOCAL-00017" summary="4.16">
      <option name="closed" value="true" />
      <created>1744851108171</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1744851108171</updated>
    </task>
    <task id="LOCAL-00018" summary="4.17.1">
      <option name="closed" value="true" />
      <created>1744863848280</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1744863848280</updated>
    </task>
    <task id="LOCAL-00019" summary="4.18">
      <option name="closed" value="true" />
      <created>1744940576925</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1744940576925</updated>
    </task>
    <task id="LOCAL-00020" summary="4.19">
      <option name="closed" value="true" />
      <created>1744965666130</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1744965666130</updated>
    </task>
    <task id="LOCAL-00021" summary="4.18.2">
      <option name="closed" value="true" />
      <created>1744989486066</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1744989486066</updated>
    </task>
    <task id="LOCAL-00022" summary="4.23">
      <option name="closed" value="true" />
      <created>1745339556889</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1745339556889</updated>
    </task>
    <option name="localTasksCounter" value="23" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="初始化" />
    <MESSAGE value="4.2" />
    <MESSAGE value="4.8" />
    <MESSAGE value="4.8.2" />
    <MESSAGE value="4.9" />
    <MESSAGE value="Merge remote-tracking branch 'origin/dev' into dev" />
    <MESSAGE value="4.9.2" />
    <MESSAGE value="4.9.3" />
    <MESSAGE value="4.9.4" />
    <MESSAGE value="4.10" />
    <MESSAGE value="4.10.2" />
    <MESSAGE value="4.10.3" />
    <MESSAGE value="4.11" />
    <MESSAGE value="4.14" />
    <MESSAGE value="4.15" />
    <MESSAGE value="4.16" />
    <MESSAGE value="4.17.1" />
    <MESSAGE value="4.18" />
    <MESSAGE value="4.19" />
    <MESSAGE value="4.18.2" />
    <MESSAGE value="4.23" />
    <MESSAGE value="4.27" />
    <option name="LAST_COMMIT_MESSAGE" value="4.27" />
  </component>
  <component name="XDebuggerManager">
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="com.nercar.ingredient.domain.dto.ElementDTO" memberName="C" />
      </pinned-members>
    </pin-to-top-manager>
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="excelItem" />
        <watch expression="responseString" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>