package com.nercar.ingredient.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.dto.PathSaveDTO;
import com.nercar.ingredient.domain.dto.ProcessPathDTO;
import com.nercar.ingredient.domain.po.*;
import com.nercar.ingredient.domain.vo.DepartmentsVO;
import com.nercar.ingredient.domain.vo.ProcessPathVO;
import com.nercar.ingredient.mapper.*;
import com.nercar.ingredient.response.PageDataResult;
import com.nercar.ingredient.security.UserContext;
import com.nercar.ingredient.domain.bo.CurrentUser;
import com.nercar.ingredient.service.ProcessPathService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【process_path(工艺路径表)】的数据库操作Service实现
* @createDate 2025-04-01 13:55:28
*/
@Slf4j
@Service
public class ProcessPathServiceImpl extends ServiceImpl<ProcessPathMapper, ProcessPath>
    implements ProcessPathService{

    @Autowired
    private ProcessPathStepsMapper processPathStepsMapper;
    @Autowired
    private DepartmentsMapper departmentsMapper;
    @Autowired
    private UsersMapper usersMapper;
    @Autowired
    private ProductionEquipmentsMapper productionEquipmentsMapper;
    @Autowired
    private SteelGradesMapper steelGradesMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveProcessPath(ProcessPathDTO processPathDTO) {
        // 基础验证
        if(StringUtils.isEmpty(processPathDTO.getSteelGrade())){
            throw new RuntimeException("钢种不能为空");
        }
        if (StringUtils.isEmpty(processPathDTO.getPathName())) {
            throw new RuntimeException("工艺路径名称不能为空");
        }
        List<PathSaveDTO> steps = processPathDTO.getSteps();
        if (CollectionUtil.isEmpty(steps)) {
            throw new RuntimeException("工艺路径步骤不能为空");
        }

        // 根据钢种查询钢种ID
        LambdaQueryWrapper<SteelGrades> steelGradesWrapper = new LambdaQueryWrapper<>();
        steelGradesWrapper.eq(SteelGrades::getSteelGrade, processPathDTO.getSteelGrade());
        SteelGrades steelGrades = steelGradesMapper.selectOne(steelGradesWrapper);

        // 检查是否存在相同的工艺路径
        if (isDuplicatePath(steelGrades.getId(), steps)) {
            throw new RuntimeException("已存在完全相同的工艺路径");
        }

        // 保存工艺路径
        ProcessPath processPath = new ProcessPath();
        processPath.setPathName(processPathDTO.getPathName());
        processPath.setPurpose(processPathDTO.getPurpose());
        processPath.setNote(processPathDTO.getNote());
        processPath.setSteelGradesId(steelGrades.getId());
        baseMapper.insert(processPath);

        // 保存工艺路径步骤
        List<ProcessPathSteps> processPathSteps = new ArrayList<>();
        for (PathSaveDTO x : steps) {
            ProcessPathSteps processPathStep = new ProcessPathSteps();
            processPathStep.setStepNumber(x.getStepNumber());
            processPathStep.setEquipmentId(Long.valueOf(x.getEquipmentId()));
            processPathStep.setPathId(processPath.getId());
            processPathStep.setType(x.getType());
            processPathSteps.add(processPathStep);
        }
        processPathStepsMapper.insert(processPathSteps);
        
        return processPath.getId();
    }

    /**
     * 检查是否存在相同的工艺路径
     * @param steelGradeId 钢种ID
     * @param newSteps 新的工艺步骤
     * @return true if duplicate exists
     */
    private boolean isDuplicatePath(Long steelGradeId, List<PathSaveDTO> newSteps) {
        // 查找相同钢种的所有工艺路径
        LambdaQueryWrapper<ProcessPath> pathWrapper = new LambdaQueryWrapper<>();
        pathWrapper.eq(ProcessPath::getSteelGradesId, steelGradeId);
        List<ProcessPath> existingPaths = baseMapper.selectList(pathWrapper);

        // 对每个现有路径进行比较
        for (ProcessPath existingPath : existingPaths) {
            // 获取现有路径的步骤
            LambdaQueryWrapper<ProcessPathSteps> stepsWrapper = new LambdaQueryWrapper<>();
            stepsWrapper.eq(ProcessPathSteps::getPathId, existingPath.getId())
                       .orderByAsc(ProcessPathSteps::getStepNumber);
            List<ProcessPathSteps> existingSteps = processPathStepsMapper.selectList(stepsWrapper);

            // 如果步骤数量不同，继续下一个比较
            if (existingSteps.size() != newSteps.size()) {
                continue;
            }

            // 比较每个步骤
            boolean isIdentical = true;
            for (int i = 0; i < existingSteps.size(); i++) {
                ProcessPathSteps existingStep = existingSteps.get(i);
                PathSaveDTO newStep = newSteps.get(i);
                
                if (!existingStep.getEquipmentId().equals(Long.valueOf(newStep.getEquipmentId())) ||
                    !existingStep.getType().equals(newStep.getType()) ||
                    !existingStep.getStepNumber().equals(newStep.getStepNumber())) {
                    isIdentical = false;
                    break;
                }
            }

            if (isIdentical) {
                return true;
            }
        }

        return false;
    }


    @Autowired
    private StandardTemplateMapper standardTemplateMapper;
    @Override
    public List<DepartmentsVO> getTree() {
        // 获取当前用户信息
        CurrentUser currentUser = UserContext.getCurrentUser();
        String userName = currentUser != null ? currentUser.getUsername() : "";
        log.info("用户部门权限控制 - 当前用户: {}", userName);

        // 根据userName查询用户信息，获取部门ID
        String userDepartmentName = getUserDepartmentName(userName);
        log.info("用户部门权限控制 - 用户: {}, 部门: {}", userName, userDepartmentName);

        // 1.1、查询所有部门
        List<Departments> departments = departmentsMapper.selectList(null);
        List<DepartmentsVO> departmentsVO = departments.stream()
                .map(dept -> {
                    DepartmentsVO vo = new DepartmentsVO();
                    BeanUtils.copyProperties(dept, vo);
                    return vo;
                })
                .collect(Collectors.toList());

        // 1.2、根据用户部门权限过滤部门列表
        if (!"技术中心".equals(userDepartmentName) && !userDepartmentName.isEmpty()) {
            // 非技术中心用户只能看到自己部门的数据
            log.info("应用部门权限过滤 - 用户部门: {}, 过滤前部门数量: {}", userDepartmentName, departmentsVO.size());
            departmentsVO = departmentsVO.stream()
                    .filter(dept -> userDepartmentName.equals(dept.getDepartmentName()))
                    .collect(Collectors.toList());
            log.info("应用部门权限过滤 - 过滤后部门数量: {}", departmentsVO.size());
        } else {
            log.info("技术中心用户或未设置部门 - 返回所有部门数据，部门数量: {}", departmentsVO.size());
        }
        // 2、遍历部门集合，根据部门ID查询所有设备
        for (DepartmentsVO departmentVO : departmentsVO) {
            // 2.1、根据部门ID查询对应的设备
            LambdaQueryWrapper<ProductionEquipments> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ProductionEquipments::getDepartmentId, departmentVO.getId());
            List<ProductionEquipments> productionEquipments = productionEquipmentsMapper.selectList(wrapper);
            // 2.2、将部门对应的设备集合赋值给部门对象
            departmentVO.setProductionEquipments(productionEquipments);
            // 2.3、不再返回模板数据，设置为空列表
            departmentVO.setStandardTemplate(new ArrayList<>());
        }
        return departmentsVO;
    }

    @Override
    public List<ProcessPathVO> selectRecommend() {
        // 1、查询工艺路径表中使用频次最高的两条路径
        LambdaQueryWrapper<ProcessPath> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNotNull(ProcessPath::getFrequence)  // 添加非空条件
              .orderByDesc(ProcessPath::getFrequence)
              .last("limit 2");
        List<ProcessPath> processPaths = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(processPaths)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(processPaths, ProcessPathVO.class);
    }

    /**
     * 根据用户名获取用户部门名称
     * @param userName 用户名
     * @return 部门名称，如果未找到则返回空字符串
     */
    private String getUserDepartmentName(String userName) {
        if (userName == null || userName.isEmpty()) {
            return "";
        }

        try {
            LambdaQueryWrapper<Users> userWrapper = new LambdaQueryWrapper<>();
            userWrapper.eq(Users::getUserName, userName);
            Users user = usersMapper.selectOne(userWrapper);

            if (user != null && user.getDepartmentId() != null) {
                Departments department = departmentsMapper.selectById(user.getDepartmentId());
                if (department != null) {
                    return department.getDepartmentName();
                }
            }
        } catch (Exception e) {
            log.error("查询用户部门信息失败 - 用户: {}, 错误: {}", userName, e.getMessage());
        }

        return "";
    }
}




