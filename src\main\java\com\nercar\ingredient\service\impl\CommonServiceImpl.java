package com.nercar.ingredient.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.dto.PurposeCompositionsQueryDTO;
import com.nercar.ingredient.domain.po.ExecutionStandard;
import com.nercar.ingredient.domain.po.StandardIngredientRecords;
import com.nercar.ingredient.domain.po.SteelGrades;
import com.nercar.ingredient.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

@Service
public class CommonServiceImpl implements CommonService  {

    @Autowired
    SteelGradesService steelGradesService;
    @Autowired
    ExecutionStandardService executionStandardService;
    @Autowired
    StandardIngredientRecordsService standardIngredientRecordsService;

    //提取通用方法，获取标准配料ID
    @Override
    public Long getStandardIngredientRecordId(PurposeCompositionsQueryDTO purposeCompositionsQueryDTO) {
        //1、先通过steelGrade和standardName拿到standardIngredientRecordId
        //通过钢种表拿到钢种的ID
        LambdaQueryWrapper<SteelGrades> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq( StringUtils.hasLength(purposeCompositionsQueryDTO.getSteelGrade()), SteelGrades::getSteelGrade, purposeCompositionsQueryDTO.getSteelGrade());
        SteelGrades one = steelGradesService.getOne(wrapper);
        Long steelGradeId = one.getId();
        //通过标准表拿到标准的ID
        LambdaQueryWrapper<ExecutionStandard> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(StringUtils.hasLength(purposeCompositionsQueryDTO.getStandardName()), ExecutionStandard::getStandardName, purposeCompositionsQueryDTO.getStandardName());
        ExecutionStandard one1 = executionStandardService.getOne(wrapper1);
        Long standardId = one1.getId();
        //通过钢种ID和标准ID确定标准配料记录表
        LambdaQueryWrapper<StandardIngredientRecords> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(StandardIngredientRecords::getSteelGradeId, steelGradeId)
                .eq(StandardIngredientRecords::getExecutionStandardId, standardId);
        StandardIngredientRecords one2 = standardIngredientRecordsService.getOne(wrapper2);
        //拿到标准配料记录表的ID
        Long standardIngredientRecordId = one2.getId();
        return standardIngredientRecordId;
    }
}
