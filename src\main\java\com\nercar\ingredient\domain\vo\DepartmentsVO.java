package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nercar.ingredient.domain.po.ProductionEquipments;
import com.nercar.ingredient.domain.po.StandardTemplate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 部门表
 * @TableName departments
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DepartmentsVO extends BaseVO {
    /**
     * 主键
     */
    @TableId
    @Schema(description = "主键")
    private Long id;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String departmentName;

    /**
     * 标准配料模板表
     */
    @Schema(description = "标准配料模板表")
    private List<StandardTemplate> standardTemplate;

    /**
     * 生产设备表
     */
    @Schema(description = "生产设备表")
    private List<ProductionEquipments> productionEquipments;


    /**
     * 
     */
    private String createuser;

    /**
     * 
     */
    private Date createtime;

    /**
     * 
     */
    private Date updatetime;




}