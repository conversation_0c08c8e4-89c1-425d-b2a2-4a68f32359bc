package com.nercar.ingredient.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ColumnWidth(20)
@ContentRowHeight(20)
@HeadRowHeight(30)
@ExcelIgnoreUnannotated
public class MaterialYieldRatesExcel implements Serializable {

    @ExcelProperty(value = "生产车间", index = 0)
    private String productionDept;

    @ExcelProperty(value = "作业线", index = 1)
    private String lineName;

    @ExcelProperty(value = "供料类别", index = 2)
    private String materialCategoryName;

    @ExcelProperty(value = "产品类别", index = 3)
    private String productCategory;

    @ExcelProperty(value = "钢类", index = 4)
    private String steelClass;

    @ExcelProperty(value = "钢号", index = 5)
    private String steelGrade;

    @ExcelProperty(value = "规格", index = 6)
    private String specifications;

    @ExcelProperty(value = "成材率")
    private String materialYield;

    @ExcelProperty(value = "不定尺（黑皮）", index = 7)
    private String unfixedLengthBlackSkin;

    @ExcelProperty(value = "不定尺成材率（磨光）", index = 9)
    private String unfixedYieldPolished;

    @ExcelProperty(value = "不定尺成材率（削皮）", index = 11)
    private String unfixedYieldPeeling;

    @ExcelProperty(value = "成材率（定尺+磨光）", index = 12)
    private String fixedYieldPolished;

    @ExcelProperty(value = "成材率（定尺+车光）", index = 13)
    private String fixedYieldLathe;

    @ExcelProperty(value = "成材率（定尺+削皮）", index = 14)
    private String fixedYieldPeeling;

    @ExcelProperty(value = "不定尺磨光车光", index = 15)
    private String unfixedPolishedLathe;

    @ExcelProperty(value = "不定尺磨光削皮", index = 16)
    private String unfixedPolishedPeeling;

    @ExcelProperty(value = "定尺磨光车光", index = 17)
    private String fixedPolishedLathe;

    @ExcelProperty(value = "定尺磨光削皮", index = 18)
    private String fixedPolishedPeeling;

    @ExcelProperty(value = "定尺率", index = 19)
    private String fixedLengthRate;

    @ExcelProperty(value = "磨光率", index = 20)
    private String polishedRate;

    @ExcelProperty(value = "车光率", index = 21)
    private String latheRate;

    @ExcelProperty(value = "削皮率", index = 22)
    private String peelingRate;

    @ExcelProperty(value = "烧损", index = 23)
    private String burningLoss;

    @ExcelProperty(value = "定尺（黑皮）", index = 8)
    private String fixedLengthBlackSkin;

    @ExcelProperty(value = "不定尺成材率（车光）", index = 10)
    private String unfixedYieldLathe;

    @ExcelProperty(value = "成材率（定尺+铣光）", index = 24)
    private String fixedYieldMilling;

    @ExcelProperty(value = "定尺+铣光率", index = 25)
    private String fixedMillingRate;

    @ExcelProperty(value = "成材率（调质）", index = 26)
    private String quenchingYield;

    @ExcelProperty(value = "调质率", index = 27)
    private String quenchingRate;

    @ExcelProperty(value = "成材率（调质+定尺）", index = 28)
    private String fixedQuenchingYield;

    @ExcelProperty(value = "定制单位", index = 29)
    private String specifiedUnit;

    @ExcelProperty(value = "修改内容及原因", index = 30)
    private String modificationReason;
}
