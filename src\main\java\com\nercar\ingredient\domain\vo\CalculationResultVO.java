package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 计算结果表
 * @TableName calculation_result
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CalculationResultVO extends BaseVO {
    /**
     * 主键
     */
    @TableId
    @Schema(description = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 主表ID
     */
    @Schema(description = "主表ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long standardIngredientRecordId;

    /**
     * 使用的原料ID
     */
    @Schema(description = "使用的原料ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long rawMaterialId;

    /**
     * 原料名称
     */
    @Schema(description = "原料名称")
    private String rawMaterialName;

    /**
     * 目标成分ID
     */
    @Schema(description = "目标成分ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long purposeCompositionId;

    /**
     * 品位%
     */
    @Schema(description = "品位%")
    private BigDecimal composition;

    /**
     * 收得率%
     */
    @Schema(description = "收得率%")
    private BigDecimal recoveryRate;

    /**
     * 重量%
     */
    @Schema(description = "重量")
    private BigDecimal wieght;
    /**
     * 配入量
     */
    @Schema(description = "配入量")
    private BigDecimal averageValue;

    /**
     * 价格
     */
    @Schema(description = "价格")
    private BigDecimal price;
    /**
     * 单耗
     */
    @Schema(description = "单耗")
    private String singleConsume;

    /**
     * 计算序号
     */
    @Schema(description = "计算序号")
    private String calculationSequence;

    @Schema(description = "最小值")
    private BigDecimal minValue;

    @Schema(description = "最大값")
    private BigDecimal maxValue;

    @Schema(description = "创建人")
    private String createuser;

    @Schema(description = "创建时间")
    private Date createtime;

    @Schema(description = "更新时间")
    private Date updatetime;
    /**
     * 成本
     */
    @Schema(description = "成本")
    private BigDecimal cost;

}