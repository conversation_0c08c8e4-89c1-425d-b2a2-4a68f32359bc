package com.nercar.ingredient.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nercar.ingredient.domain.po.MaterialYieldRates;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nercar.ingredient.domain.vo.MaterialYieldRatesVO;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【material_yield_rates(成材率)】的数据库操作Mapper
* @createDate 2025-04-07 15:15:46
* @Entity com.nercar.ingredient.domain.po.MaterialYieldRates
*/
public interface MaterialYieldRatesMapper extends BaseMapper<MaterialYieldRates> {

    IPage<MaterialYieldRates> selectMaterialYieldRates(Page<MaterialYieldRates> page, @Param(Constants.WRAPPER) LambdaQueryWrapper<MaterialYieldRates> wrapper);
}




