package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 工艺路径材料关联表
 * @TableName path_materials
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PathMaterialsVO extends BaseVO {
    /**
     * 路径ID
     */
    @Schema(description = "路径ID")
    private Long pathId;

    /**
     * 材料ID
     */
    @Schema(description = "材料ID")
    private Long rawMaterialId;


}