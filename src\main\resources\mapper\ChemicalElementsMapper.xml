<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.ChemicalElementsMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.ChemicalElements">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="elementName" column="element_name" jdbcType="VARCHAR"/>
            <result property="elementSymbol" column="element_symbol" jdbcType="VARCHAR"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,element_name,element_symbol,
        createtime,updatetime
    </sql>
</mapper>
