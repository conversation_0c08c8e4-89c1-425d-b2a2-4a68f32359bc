package com.nercar.ingredient.domain.bo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CalculationQueryData extends BaseModel{


    /**
     * 原料属性
     */
    private List<Material_dict> lis_material_dict;

    /**
     * 钢铁收得率对应设备属性
     */
    private List<String> lis_device;

    /**
     *不确定，先传默认值
     */
    private List<String> lis_scenario;

    /**
     * 脱氧剂类对应设备
     */
    private List<String> lis_deoxygen;


    /**
     *元素目标成分
     */

    private List<Element_composition_dict> lis_element_composition_dict;

}
