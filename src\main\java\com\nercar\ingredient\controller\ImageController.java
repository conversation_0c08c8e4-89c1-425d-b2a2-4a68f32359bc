package com.nercar.ingredient.controller;

import com.nercar.ingredient.response.Result;
import com.nercar.ingredient.utils.ImageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Tag(name = "图片上传接口")
@RestController
@RequestMapping("/image")
public class ImageController {

    @Autowired
    private ImageUtils imageUtils;

    @Operation(summary = "上传图片")
    @PostMapping("/upload")
    public Result<String> uploadImage(@RequestParam("file") MultipartFile file) {
        try {
            log.info("开始上传文件: {}", file.getOriginalFilename());
            String imageUrl = imageUtils.saveImage(file);
            log.info("文件上传成功: {}", imageUrl);
            return Result.ok(imageUrl);
        } catch (Exception e) {
            log.error("图片上传失败", e);
            return Result.failed("图片上传失败：" + e.getMessage());
        }
    }
}