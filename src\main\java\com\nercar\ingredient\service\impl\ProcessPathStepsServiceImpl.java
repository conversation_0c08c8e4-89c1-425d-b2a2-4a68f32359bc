package com.nercar.ingredient.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.config.ImageServerConfig;
import com.nercar.ingredient.domain.dto.PathSaveDTO;
import com.nercar.ingredient.domain.po.Departments;
import com.nercar.ingredient.domain.po.ProcessPathSteps;
import com.nercar.ingredient.domain.po.ProductionEquipments;
import com.nercar.ingredient.domain.po.StandardTemplate;
import com.nercar.ingredient.domain.vo.ProcessPathVO;
import com.nercar.ingredient.mapper.DepartmentsMapper;
import com.nercar.ingredient.mapper.ProcessPathStepsMapper;
import com.nercar.ingredient.mapper.StandardTemplateMapper;
import com.nercar.ingredient.service.ProcessPathStepsService;
import com.nercar.ingredient.service.ProductionEquipmentsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【process_path_steps(工序表)】的数据库操作Service实现
 * @createDate 2025-04-01 13:55:28
 */
@Service
public class ProcessPathStepsServiceImpl extends ServiceImpl<ProcessPathStepsMapper, ProcessPathSteps>
        implements ProcessPathStepsService{
    @Autowired
    private ProcessPathStepsMapper processPathStepsMapper;
    @Autowired
    private ProductionEquipmentsService productionEquipmentsService;
    @Autowired
    private StandardTemplateMapper standardTemplateMapper;
    @Autowired
    private DepartmentsMapper departmentsMapper;

    // 添加ImageServerConfig配置类注入
    @Autowired
    private ImageServerConfig imageServerConfig;

    @Override
    public List<PathSaveDTO> selectProcessPathSteps(ProcessPathVO processPathVO) {
        List<PathSaveDTO> pathSaveDTOS = new ArrayList<>();
        //根据工艺路径id查询所有工序
        LambdaQueryWrapper<ProcessPathSteps> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProcessPathSteps::getPathId, processPathVO.getId());
        List<ProcessPathSteps> processPathSteps =processPathStepsMapper.selectList(wrapper);
        //遍历工序表查找对应的设备
        for (ProcessPathSteps processPathStep : processPathSteps) {
            if(processPathStep.getType() == 1){
                //根据工序ID查找设备
                LambdaQueryWrapper<ProductionEquipments> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(ProductionEquipments::getId, processPathStep.getEquipmentId());
                ProductionEquipments productionEquipment = productionEquipmentsService.getOne(wrapper1);
                if (productionEquipment != null){
                    PathSaveDTO pathSaveDTO = new PathSaveDTO();
                    pathSaveDTO.setStepNumber(processPathStep.getStepNumber());
                    pathSaveDTO.setEquipmentId(processPathStep.getEquipmentId());
                    pathSaveDTO.setEquipmentName(productionEquipment.getEquipmentName());
                    pathSaveDTO.setImagesUrl(productionEquipment.getImagesUrl());
                    pathSaveDTO.setType(processPathStep.getType());
                    pathSaveDTO.setEquipmentType(productionEquipment.getEquipmentType());

                    // 根据设备的部门ID查询部门名称
                    Long departmentId = productionEquipment.getDepartmentId();
                    if (departmentId != null) {
                        LambdaQueryWrapper<Departments> departmentWrapper = new LambdaQueryWrapper<>();
                        departmentWrapper.eq(Departments::getId, departmentId);
                        Departments department = departmentsMapper.selectOne(departmentWrapper);
                        if (department != null) {
                            pathSaveDTO.setDepartmentName(department.getDepartmentName());
                        }
                    }

                    pathSaveDTOS.add(pathSaveDTO);
                }
            }else if (processPathStep.getType() == 2){
                //模板类型的工序不再返回，跳过处理
                // 注意：现有数据库中可能存在type=2的历史数据，这里选择跳过而不是报错
                continue;
            }
        }
        return pathSaveDTOS;
    }

}




