package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 金属料吨水单耗配置VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "金属料吨水单耗配置响应数据")
public class MetalMaterialWaterConsumptionConfigVO {
    
    /**
     * 主键
     */
    @TableId
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 金属料吨水单耗
     */
    @Schema(description = "金属料吨水单耗")
    private Integer metalMaterialWaterConsumption;

    /**
     * 冶炼方法
     */
    @Schema(description = "冶炼方法")
    private String method;

    /**
     * 冶炼设备
     */
    @Schema(description = "冶炼设备")
    private String device;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createuser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;
}
