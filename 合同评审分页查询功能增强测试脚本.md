# 合同评审分页查询功能增强测试脚本

## 📋 **功能测试清单**

### **1. 下拉框数据接口测试**

#### **1.1 获取顾客名称选项**
```bash
curl -X GET "http://localhost:8080/contractReview/getCustomerNameOptions" \
  -H "Content-Type: application/json"
```

**预期响应格式：**
```json
{
    "code": 200,
    "success": true,
    "message": "成功",
    "data": [
        {
            "value": "小米科技有限公司",
            "label": "小米科技有限公司",
            "count": 1
        },
        {
            "value": "华为技术有限公司",
            "label": "华为技术有限公司",
            "count": 1
        },
        {
            "value": "比亚迪股份有限公司",
            "label": "比亚迪股份有限公司",
            "count": 1
        }
    ]
}
```

#### **1.2 获取钢种选项**
```bash
curl -X GET "http://localhost:8080/contractReview/getSteelGradeNameOptions" \
  -H "Content-Type: application/json"
```

**预期响应格式：**
```json
{
    "code": 200,
    "success": true,
    "message": "成功",
    "data": [
        {
            "value": "304",
            "label": "304",
            "count": 1
        },
        {
            "value": "20CrMnTi",
            "label": "20CrMnTi",
            "count": 1
        },
        {
            "value": "N07718",
            "label": "N07718",
            "count": 1
        }
    ]
}
```

#### **1.3 获取钢类选项**
```bash
curl -X GET "http://localhost:8080/contractReview/getSteelTypeNameOptions" \
  -H "Content-Type: application/json"
```

**预期响应格式：**
```json
{
    "code": 200,
    "success": true,
    "message": "成功",
    "data": [
        {
            "value": "不锈钢",
            "label": "不锈钢",
            "count": 1
        },
        {
            "value": "工具钢",
            "label": "工具钢",
            "count": 1
        },
        {
            "value": "汽车钢",
            "label": "汽车钢",
            "count": 1
        }
    ]
}
```

### **2. 增强分页查询接口测试**

#### **2.1 基础分页查询（无筛选条件）**
```bash
curl -X POST "http://localhost:8080/contractReview/getContractReviewPage" \
  -H "Content-Type: application/json" \
  -d '{
    "pageNo": 1,
    "pageSize": 10
  }'
```

#### **2.2 按顾客名称精确查询**
```bash
curl -X POST "http://localhost:8080/contractReview/getContractReviewPage" \
  -H "Content-Type: application/json" \
  -d '{
    "pageNo": 1,
    "pageSize": 10,
    "customerNameExact": "小米科技有限公司"
  }'
```

#### **2.3 按钢种精确查询**
```bash
curl -X POST "http://localhost:8080/contractReview/getContractReviewPage" \
  -H "Content-Type: application/json" \
  -d '{
    "pageNo": 1,
    "pageSize": 10,
    "steelGradeNameExact": "304"
  }'
```

#### **2.4 按钢类精确查询**
```bash
curl -X POST "http://localhost:8080/contractReview/getContractReviewPage" \
  -H "Content-Type: application/json" \
  -d '{
    "pageNo": 1,
    "pageSize": 10,
    "steelTypeNameExact": "不锈钢"
  }'
```

#### **2.5 按合同编号精确查询**
```bash
curl -X POST "http://localhost:8080/contractReview/getContractReviewPage" \
  -H "Content-Type: application/json" \
  -d '{
    "pageNo": 1,
    "pageSize": 10,
    "codeExact": "F0001-2025"
  }'
```

#### **2.6 按时间范围查询**
```bash
curl -X POST "http://localhost:8080/contractReview/getContractReviewPage" \
  -H "Content-Type: application/json" \
  -d '{
    "pageNo": 1,
    "pageSize": 10,
    "startTime": "2025-01-01 00:00:00",
    "endTime": "2025-12-31 23:59:59"
  }'
```

#### **2.7 组合查询（多条件）**
```bash
curl -X POST "http://localhost:8080/contractReview/getContractReviewPage" \
  -H "Content-Type: application/json" \
  -d '{
    "pageNo": 1,
    "pageSize": 10,
    "customerNameExact": "小米科技有限公司",
    "steelTypeNameExact": "不锈钢",
    "startTime": "2025-01-01 00:00:00",
    "endTime": "2025-12-31 23:59:59"
  }'
```

### **3. 冶炼工艺字段格式测试**

#### **3.1 验证冶炼工艺返回数组格式**
```bash
curl -X GET "http://localhost:8080/contractReview/getContractById/1001" \
  -H "Content-Type: application/json"
```

**预期响应中的冶炼工艺字段：**
```json
{
    "smeltingProcess": ["电炉", "LF", "真空感应炉"]  // 应该是数组格式，不是字符串
}
```

## 🎯 **验证要点**

### **功能验证**
- ✅ 下拉框接口返回正确的选项数据
- ✅ 分页查询支持新的筛选条件
- ✅ 精确匹配和模糊匹配都能正常工作
- ✅ 时间范围查询功能正常
- ✅ 多条件组合查询功能正常
- ✅ 冶炼工艺字段返回数组格式

### **性能验证**
- ✅ 下拉框查询响应时间 < 1秒
- ✅ 分页查询响应时间 < 2秒
- ✅ 大数据量查询不会超时

### **数据验证**
- ✅ 下拉框选项按数量倒序排列
- ✅ 只返回有效状态的数据（status=1）
- ✅ 分页数据准确，总数正确
- ✅ 查询结果符合筛选条件

## 📊 **测试数据验证SQL**

```sql
-- 验证测试数据
SELECT 
    id,
    code,
    customer_name,
    steel_type_name,
    steel_grade_name,
    smelting_process,
    create_time,
    status
FROM contract_review_info_merged 
WHERE status = 1
ORDER BY create_time DESC;

-- 验证顾客名称统计
SELECT 
    customer_name,
    COUNT(*) as count
FROM contract_review_info_merged 
WHERE status = 1 
  AND customer_name IS NOT NULL 
  AND customer_name != ''
GROUP BY customer_name
ORDER BY count DESC;

-- 验证钢种统计
SELECT 
    steel_grade_name,
    COUNT(*) as count
FROM contract_review_info_merged 
WHERE status = 1 
  AND steel_grade_name IS NOT NULL 
  AND steel_grade_name != ''
GROUP BY steel_grade_name
ORDER BY count DESC;

-- 验证钢类统计
SELECT 
    steel_type_name,
    COUNT(*) as count
FROM contract_review_info_merged 
WHERE status = 1 
  AND steel_type_name IS NOT NULL 
  AND steel_type_name != ''
GROUP BY steel_type_name
ORDER BY count DESC;
```

## 🚀 **前端集成建议**

### **下拉框组件示例**
```javascript
// 获取下拉框数据
const loadCustomerOptions = async () => {
    const response = await fetch('/contractReview/getCustomerNameOptions');
    const result = await response.json();
    return result.data;
};

// 查询表单提交
const searchContracts = async (params) => {
    const response = await fetch('/contractReview/getContractReviewPage', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params)
    });
    const result = await response.json();
    return result.data;
};
```

这个测试脚本涵盖了所有新增功能的验证，确保功能完整性和数据准确性。
