package com.nercar.ingredient.domain.dto;


import com.nercar.ingredient.domain.vo.CalculationResultVO;
import com.nercar.ingredient.domain.vo.ProcessPathVO;
import com.nercar.ingredient.domain.vo.PurposeCompositionsVO;
import com.nercar.ingredient.domain.vo.StandardRawMaterialsVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AllDataDTO extends BaseDTO{

    /**
     * 钢种
     */
    private String steelGrade;
    /**
     * 标准名称
     */
    private String standardName;
    /**
     * 特殊说明
     */
    private String specialNotes;

    /**
     * 工艺路径
     */
    private ProcessPathVO processPath;

    /**
     * 目标成分
     */
    private List<PurposeCompositionsVO> purposeCompositions;

    /**
     * 原料清单
     */
    private List<StandardRawMaterialsVO> standardRawMaterials;

    /**
     * 计算结果
     */
    private List<CalculationResultVO> calculationResult;


}
