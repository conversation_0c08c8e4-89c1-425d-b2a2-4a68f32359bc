package com.nercar.ingredient.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.dto.PurposeCompositionsQueryDTO;
import com.nercar.ingredient.domain.dto.StandardCompositionsQueryDTO;
import com.nercar.ingredient.domain.po.PurposeCompositions;
import com.nercar.ingredient.domain.vo.PurposeCompositionsVO;
import com.nercar.ingredient.mapper.PurposeCompositionsMapper;
import com.nercar.ingredient.service.CommonService;
import com.nercar.ingredient.service.PurposeCompositionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【purpose_compositions(目标成分)】的数据库操作Service实现
* @createDate 2025-04-01 13:55:28
*/
@Service
public class PurposeCompositionsServiceImpl extends ServiceImpl<PurposeCompositionsMapper, PurposeCompositions>
    implements PurposeCompositionsService{

    @Autowired
    private CommonService commonService;

    @Override
    public List<PurposeCompositionsVO> getPurposeCompositions(PurposeCompositionsQueryDTO purposeCompositionsQueryDTO) {
        if (StringUtils.isEmpty(purposeCompositionsQueryDTO.getSteelGrade()) || StringUtils.isEmpty(purposeCompositionsQueryDTO.getStandardName())) {
            throw new RuntimeException("steelGrade和standardName不能为空");
        }
        // 通过steelGrade和standardName拿到standardIngredientRecordId
        Long standardIngredientRecordId = commonService.getStandardIngredientRecordId(purposeCompositionsQueryDTO);
        //通过标准配料记录表ID拿到一系列目标成分
        LambdaQueryWrapper<PurposeCompositions> wrapper3 = new LambdaQueryWrapper<>();
        wrapper3.eq(PurposeCompositions::getStandardIngredientRecordId, standardIngredientRecordId)
                .orderByAsc(PurposeCompositions::getSortOrder);
        List<PurposeCompositions> list = list(wrapper3);
        List<PurposeCompositionsVO> purposeCompositionsVOList = BeanUtil.copyToList(list, PurposeCompositionsVO.class);
        return purposeCompositionsVOList;
    }

    @Override
    public void savePurposeCompositions(PurposeCompositionsQueryDTO purposeCompositionsQueryDTO) {
        if (StringUtils.isEmpty(purposeCompositionsQueryDTO.getSteelGrade()) || StringUtils.isEmpty(purposeCompositionsQueryDTO.getStandardName())) {
            throw new RuntimeException("steelGrade和standardName不能为空");
        }
        Long standardIngredientRecordId = commonService.getStandardIngredientRecordId( purposeCompositionsQueryDTO);
        PurposeCompositions purposeCompositions = new PurposeCompositions();
        purposeCompositions.setStandardIngredientRecordId(standardIngredientRecordId);
        //计算或设置平均值
        BigDecimal averageValue;
        if (purposeCompositionsQueryDTO.getMinValue() != null && purposeCompositionsQueryDTO.getMaxValue() != null) {
            // 如果min和max都不为空，计算平均值
            MathContext mathContext = new MathContext(2);
            averageValue = purposeCompositionsQueryDTO.getMinValue().add(purposeCompositionsQueryDTO.getMaxValue())
                    .divide(new BigDecimal(2), mathContext);
            // 计算平均值成功
        } else {
            // 如果min或max为空，直接使用传入的averageValue
            averageValue = purposeCompositionsQueryDTO.getAverageValue();
            if (averageValue == null) {
                throw new RuntimeException("目标成分的平均值不能为空，元素名称：" + purposeCompositionsQueryDTO.getElementName());
            }
        }
        BeanUtil.copyProperties(purposeCompositionsQueryDTO, purposeCompositions);
        purposeCompositions.setAverageValue(averageValue);
        save(purposeCompositions);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> updatePurposeCompositions(PurposeCompositionsQueryDTO purposeCompositionsQueryDTO) {
        List<String> ids = new ArrayList<>();
        Long standardIngredientRecordId = purposeCompositionsQueryDTO.getStandardIngredientRecordId();
        List<StandardCompositionsQueryDTO> elementList = purposeCompositionsQueryDTO.getElementList();

        if (elementList == null || elementList.isEmpty()) {
            throw new RuntimeException("目标成分列表不能为空");
        }

        // 如果没有standardIngredientRecordId，说明是第一次保存
        if (standardIngredientRecordId == null) {
            for (StandardCompositionsQueryDTO element : elementList) {
                // 查找是否存在相同元素名称且没有关联主表ID的记录
                LambdaQueryWrapper<PurposeCompositions> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(PurposeCompositions::getElementName, element.getElementName())
                        .eq(PurposeCompositions::getCode, element.getCode())
                        .eq(PurposeCompositions::getMaxValue, element.getMaxValue())
                        .eq(PurposeCompositions::getMinValue, element.getMinValue())
                        .eq(PurposeCompositions::getType, element.getType())
                        .isNull(PurposeCompositions::getStandardIngredientRecordId);
                PurposeCompositions existingElement = getOne(wrapper);

                if (existingElement != null) {
                    // 更新已存在的记录
                    existingElement.setMinValue(element.getMinValue());
                    existingElement.setMaxValue(element.getMaxValue());
                    existingElement.setCode(element.getCode());
//                    // 计算平均值
//                    MathContext mathContext = new MathContext(2);
//                    BigDecimal avg = element.getMinValue().add(element.getMaxValue())
//                            .divide(new BigDecimal(2), mathContext);
                    existingElement.setAverageValue(element.getAverageValue());
                    existingElement.setType(element.getType());
                    updateById(existingElement);
                    ids.add(existingElement.getId().toString());
                } else {
                    // 创建新记录
                    PurposeCompositions newElement = new PurposeCompositions();
                    newElement.setElementName(element.getElementName());
                    newElement.setMinValue(element.getMinValue());
                    newElement.setMaxValue(element.getMaxValue());
                    newElement.setCode(element.getCode());
//                    // 计算平均值
//                    MathContext mathContext = new MathContext(2);
//                    BigDecimal avg = element.getMinValue().add(element.getMaxValue())
//                            .divide(new BigDecimal(2), mathContext);
                    newElement.setAverageValue(element.getAverageValue());
                    newElement.setType(element.getType());
                    save(newElement);
                    ids.add(newElement.getId().toString());
                }
            }
        } else {
            // 如果有standardIngredientRecordId，删除所有现有记录，然后按原始顺序重新保存
            LambdaQueryWrapper<PurposeCompositions> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(PurposeCompositions::getStandardIngredientRecordId, standardIngredientRecordId);
            remove(deleteWrapper);

            // 按 elementList 的原始顺序保存，设置 sortOrder
            for (int i = 0; i < elementList.size(); i++) {
                StandardCompositionsQueryDTO element = elementList.get(i);

                PurposeCompositions newElement = new PurposeCompositions();
                newElement.setStandardIngredientRecordId(standardIngredientRecordId);
                newElement.setElementName(element.getElementName());
                newElement.setMinValue(element.getMinValue());
                newElement.setMaxValue(element.getMaxValue());
                newElement.setCode(element.getCode());
                newElement.setType(element.getType());
                newElement.setSortOrder(i);  // 设置排序序号

                // 计算或设置平均值
                BigDecimal averageValue;
                if (element.getMinValue() != null && element.getMaxValue() != null) {
                    // 如果min和max都不为空，计算平均值
                    MathContext mathContext = new MathContext(2);
                    averageValue = element.getMinValue().add(element.getMaxValue())
                            .divide(new BigDecimal(2), mathContext);
                    // 计算平均值成功
                } else {
                    // 如果min或max为空（如"配料内控"类型），直接使用传入的averageValue
                    averageValue = element.getAverageValue();
                    if (averageValue == null) {
                        throw new RuntimeException("目标成分的平均值不能为空，元素名称：" + element.getElementName() +
                                "，类型：" + element.getType());
                    }
                }
                newElement.setAverageValue(averageValue);

                save(newElement);
                ids.add(newElement.getId().toString());
            }
        }

        return ids;
    }
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public List<String> updatePurposeCompositions(PurposeCompositionsQueryDTO purposeCompositionsQueryDTO) {
//        List<String> ids = new ArrayList<>();
//        // 1、通过standardIngredientRecordId判断是更新还是新增
//        Long standardIngredientRecordId = purposeCompositionsQueryDTO.getStandardIngredientRecordId();
//        // 2、通过elementName找到目标成分,若是存在，则更新，不存在则新增,并求平均值
//        List<StandardCompositionsQueryDTO> elementList = purposeCompositionsQueryDTO.getElementList();
//        for (StandardCompositionsQueryDTO element : elementList) {
//            if (standardIngredientRecordId != null) {
//                // 更新逻辑
//                LambdaQueryWrapper<PurposeCompositions> elementWrapper = new LambdaQueryWrapper<>();
//                elementWrapper.eq(PurposeCompositions::getStandardIngredientRecordId, standardIngredientRecordId)
//                        .eq(PurposeCompositions::getElementName, element.getElementName());
//                PurposeCompositions existingElement = getOne(elementWrapper);
//                if (existingElement != null) {
//                    // 更新现有元素
//                    existingElement.setMinValue(element.getMinValue());
//                    existingElement.setMaxValue(element.getMaxValue());
//                    existingElement.setCode(element.getCode());
//                    // 求平均值
//                    MathContext mathContext = new MathContext(2);
//                    BigDecimal avg = element.getMinValue().add(element.getMaxValue()).divide(new BigDecimal(2), mathContext);
//                    existingElement.setAverageValue(avg);
//                    updateById(existingElement);
//                    ids.add(existingElement.getId().toString());
//                } else {
//                    // 新增元素
//                    PurposeCompositions newElement = new PurposeCompositions();
//                    newElement.setStandardIngredientRecordId(standardIngredientRecordId);
//                    newElement.setElementName(element.getElementName());
//                    newElement.setMinValue(element.getMinValue());
//                    newElement.setMaxValue(element.getMaxValue());
//                    newElement.setCode(element.getCode());
//                    // 求平均值
//                    MathContext mathContext = new MathContext(2);
//                    BigDecimal avg = element.getMinValue().add(element.getMaxValue()).divide(new BigDecimal(2), mathContext);
//                    newElement.setAverageValue(avg);
//                    save(newElement);
//                    // 直接使用 newElement 的 ID
//                    ids.add(newElement.getId().toString());
//                }
//            } else {
//                // 新增逻辑
//                PurposeCompositions newElement = new PurposeCompositions();
//                newElement.setElementName(element.getElementName());
//                newElement.setMinValue(element.getMinValue());
//                newElement.setMaxValue(element.getMaxValue());
//                newElement.setCode(element.getCode());
//                // 求平均值
//                MathContext mathContext = new MathContext(2);
//                BigDecimal avg = element.getMinValue().add(element.getMaxValue()).divide(new BigDecimal(2), mathContext);
//                newElement.setAverageValue(avg);
//                save(newElement);
//                // 直接使用 newElement 的 ID
//                ids.add(newElement.getId().toString());
//            }
//        }
//        return ids;
//    }



}




