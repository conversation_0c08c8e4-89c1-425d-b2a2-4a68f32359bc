package com.nercar.ingredient.domain.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StandardRawMaterialsPlusDTO {
    /**
     * 主键
     */
    @TableId
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @Schema(description = "原料id")
    @JsonSerialize(using = ToStringSerializer.class)
    private String materialId;

    @Schema(description = "计算结果ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @TableField(exist = false)
    private Long calculationResultId;

    @Schema(description = "重量%")
    @TableField(exist = false)
    private BigDecimal wieght;



    @Schema(description = "设备名称")
    @TableField(exist = false)
    private String equipmentName;
    /**
     * 成本
     */
    @Schema(description = "成本")
    @TableField(exist = false)
    private BigDecimal cost;

    /**
     *
     */
    @Schema(description = "原料名称")
    private String name;

    /**
     *
     */
    @Schema(description = "部门ID")
    private Long departmentId;

    /**
     *
     */
    @Schema(description = "生产设备ID")
    private Long productionEquipmentId;

    /**
     *
     */
    @Schema(description = "成材率")
    private BigDecimal yieldRate;

    /**
     *
     */
    @Schema(description = "价格")
    private BigDecimal price;

    /**
     *
     */
    @Schema(description = "是否自定义")
    private String isCustom;

    /**
     *
     */
    @Schema(description = "类别")
    private String category;

    /**
     *
     */
    @Schema(description = "优先级")
    private Integer priority;

    /**
     *
     */
    @Schema(description = "碳含量")
    private Integer cContent;

    /**
     *
     */
    @Schema(description = "单次消耗")
    private String singleConsume;

    /**
     *
     */
    @Schema(description = "部门名称")
    @TableField(exist = false)
    private String departmentName;

    /**
     *
     */
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createuser;

    /**
     *
     */
    @TableField(value = "createtime",fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     *
     */
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;

    /**
     * 钢种
     */
    @Schema(description = "钢种")
    private String steelGrade;

    @Schema(description = "元素列表")
    private List<ElementDTO> elementList;

    /**
     * 物料代码
     */
    @Schema(description = "物料代码")
    private String materialCode;

    /**
     * 标准号
     */
    @Schema(description = "标准号")
    private String standardNo;

}
