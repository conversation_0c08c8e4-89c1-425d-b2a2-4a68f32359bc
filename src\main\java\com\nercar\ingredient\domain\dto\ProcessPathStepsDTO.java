package com.nercar.ingredient.domain.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 工序表
 * @TableName process_path_steps
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProcessPathStepsDTO extends BaseDTO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 步骤编号
     */
    private String stepNumber;

    /**
     * 设备ID
     */
    private Long equipmentId;
    /**
     * 工序类别，1代表设备，2代表模板
     */
    private Integer type;

    /**
     * 描述
     */
    private String description;

    /**
     * 关联的工艺路径ID
     */
    private Long pathId;

    /**
     *
     */
    @TableField(value = "createtime",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     *
     */
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;




}