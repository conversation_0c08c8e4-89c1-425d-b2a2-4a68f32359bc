package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 模板与设备关联表
 * @TableName template_equipment_mapping
 */
@TableName(value ="template_equipment_mapping")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TemplateEquipmentMapping extends BaseEntity {
    /**
     * 模板ID
     */

    private Long templateId;

    /**
     * 设备ID
     */

    private Long equipmentId;




}