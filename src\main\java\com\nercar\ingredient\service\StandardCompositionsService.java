package com.nercar.ingredient.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.ingredient.domain.dto.StandardCompositionsQueryDTO;
import com.nercar.ingredient.domain.dto.StandardRawMaterialsIdListDTO;
import com.nercar.ingredient.domain.dto.SteelGradesDTO;
import com.nercar.ingredient.domain.po.StandardCompositions;
import com.nercar.ingredient.domain.vo.CalculationResultVO;
import com.nercar.ingredient.domain.vo.StandardCompositionsVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【standard_compositions(标准成分)】的数据库操作Service
* @createDate 2025-04-01 13:55:28
*/
public interface StandardCompositionsService extends IService<StandardCompositions> {

    List<StandardCompositionsVO> getStandardCompositions(StandardCompositionsQueryDTO standardCompositionsQueryDTO);

    Long saveStandardCompositions(StandardCompositionsQueryDTO standardCompositionsQueryDTO);

    List<StandardCompositionsVO> getSteelGradesCompositions(SteelGradesDTO steelGradesDTO);

    List<StandardCompositionsVO> getStandardCompositionsById(Long id);

    List<CalculationResultVO> getCalculationResult(StandardRawMaterialsIdListDTO standardRawMaterialsIdList);
}
