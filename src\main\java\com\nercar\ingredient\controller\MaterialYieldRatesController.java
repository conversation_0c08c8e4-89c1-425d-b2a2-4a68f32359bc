package com.nercar.ingredient.controller;


import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.nercar.ingredient.domain.dto.MaterialYieldRatesDTO;
import com.nercar.ingredient.domain.dto.MaterialYieldRatesIdsDTO;
import com.nercar.ingredient.domain.dto.MaterialYieldRatesSaveBatchDTO;
import com.nercar.ingredient.domain.po.MaterialYieldRates;
import com.nercar.ingredient.domain.vo.MaterialYieldRatesVO;
import com.nercar.ingredient.excel.MaterialYieldRatesExcel;
import com.nercar.ingredient.response.PageDataResult;
import com.nercar.ingredient.response.Result;
import com.nercar.ingredient.service.MaterialYieldRatesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Tag(name = "工序成材率接口")
@RestController
@RequestMapping("/materialYieldRates")
public class MaterialYieldRatesController {
    @Autowired
    private MaterialYieldRatesService materialYieldRatesService;

    @Operation(summary = "分页查询成材率")
    @PostMapping("/getMaterialYieldRates")
    public PageDataResult<MaterialYieldRates> getMaterialYieldRates(@RequestBody MaterialYieldRatesDTO materialYieldRatesDTO) {
        log.info("分页查询成材率");
        if(materialYieldRatesDTO.getPageNo()==null || materialYieldRatesDTO.getPageSize()==null){
            materialYieldRatesDTO.setPageNo(1);
            materialYieldRatesDTO.setPageSize(10);
        }
        IPage<MaterialYieldRates> result = materialYieldRatesService.selectMaterialYieldRates(materialYieldRatesDTO);
        List<MaterialYieldRates> records = result.getRecords();
        long total = result.getTotal();
        if (ObjectUtils.isEmpty(records)) {
            log.warn("未找到符合给定条件的成材率"); // 添加日志记录
            return PageDataResult.success(null, 0);
        }
        return PageDataResult.success(records, total);
    }

    @Operation(summary = "新增成材率")
    @PostMapping("/saveMaterialYieldRates")
    public Result<String> saveMaterialYieldRates(@RequestBody MaterialYieldRatesDTO materialYieldRatesDTO) {
        log.info("新增成材率");
        MaterialYieldRates materialYieldRates = new MaterialYieldRates();
        BeanUtils.copyProperties(materialYieldRatesDTO, materialYieldRates);
        boolean result = materialYieldRatesService.saveOrUpdate(materialYieldRates);
        if (result) {
            log.info("新增成功");
            return Result.success("新增成功");
        }
        log.warn("新增失败");
        return Result.failed("新增失败");
    }

    @Operation(summary = "编辑成材率")
    @PostMapping("/updateMaterialYieldRates")
    public Result<String> updateMaterialYieldRates(@RequestBody MaterialYieldRatesDTO materialYieldRatesDTO) {
        log.info("编辑成材率");
        MaterialYieldRates materialYieldRates = new MaterialYieldRates();
        BeanUtils.copyProperties(materialYieldRatesDTO, materialYieldRates);
        boolean result = materialYieldRatesService.saveOrUpdate(materialYieldRates);
        if (result) {
            log.info("编辑成功");
            return Result.success("编辑成功");
        }
        log.warn("编辑失败");
        return Result.failed("编辑失败");
    }

    @Operation(summary = "查看成材率详情")
    @GetMapping("/getMaterialYieldRates/{id}")
    public Result<MaterialYieldRatesVO> getMaterialYieldRates(@PathVariable Long id) {
        log.info("查看成材率详情");
        MaterialYieldRates materialYieldRates = materialYieldRatesService.getById(id);
        if (ObjectUtils.isEmpty(materialYieldRates)) {
            log.warn("未找到符合给定条件的成材率");
            return Result.failed("未找到符合给定条件的成材率");
        }
        MaterialYieldRatesVO materialYieldRatesVO = new MaterialYieldRatesVO();
        BeanUtils.copyProperties(materialYieldRates, materialYieldRatesVO);
        return Result.ok(materialYieldRatesVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "删除")
    @GetMapping("/deleteMaterialYieldRates/{id}")
    public Result<Boolean> updateMaterialYieldRates(@PathVariable String id) {
        if (StringUtils.isEmpty(id)) {
            return Result.failed("id不能为空");
        }
        return Result.success(materialYieldRatesService.removeById(Long.parseLong(id)));
    }


    @Operation(summary = "导出")
    @PostMapping("/excel/export")
    public ResponseEntity<byte[]> export(@RequestBody MaterialYieldRatesIdsDTO materialYieldRatesIdsDTO) throws IOException {
        if (CollectionUtils.isEmpty(materialYieldRatesIdsDTO.getIds())) {
            throw new RuntimeException("不能传空");
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
        String timeStamp = dateFormat.format(new Date());
        String filename = "excel_" + timeStamp;

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        List<MaterialYieldRates> resList = materialYieldRatesService.listByIds(materialYieldRatesIdsDTO.getIds());

        List<MaterialYieldRatesExcel> newList = new ArrayList<>();

        for (MaterialYieldRates item : resList) {
            MaterialYieldRatesExcel excelItem = new MaterialYieldRatesExcel();
            BeanUtils.copyProperties(item, excelItem);
//            if (item.getId() != null) {
//                excelItem.setId(String.valueOf(item.getId()));
//            }
            newList.add(excelItem);
        }
        EasyExcel.write(outputStream, MaterialYieldRatesExcel.class).sheet("工序成材率").doWrite(newList);


        byte[] excelByteArray = outputStream.toByteArray();

        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.CONTENT_ENCODING, "UTF-8");
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", filename + ".xlsx");

        return new ResponseEntity<>(excelByteArray, headers, HttpStatus.OK);
    }

    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "批量导入")
    @PostMapping("/importMaterialYieldRates")
    public Result<Boolean> saveBatch(@RequestBody MaterialYieldRatesSaveBatchDTO materialYieldRatesSaveBatchDTO) {
        boolean res=materialYieldRatesService.saveBatch(materialYieldRatesSaveBatchDTO.getMaterialYieldRatesList());
        return Result.success(res);
    }



    @Operation(summary = "Excel批量导入成材率")
    @PostMapping("/import-excel")
    public Result<String> importExcel(@RequestPart("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.failed("文件不能为空");
        }
        try {
            materialYieldRatesService.importExcel(file);
            return Result.success("导入成功");
        } catch (Exception e) {
            log.error("导入失败", e);
            return Result.failed("导入失败：" + e.getMessage());
        }
    }


}
