package com.nercar.ingredient.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.nercar.ingredient.domain.dto.StandardIngredientRecordsQueryDTO;
import com.nercar.ingredient.domain.dto.SteelGradesDTO;
import com.nercar.ingredient.domain.vo.StandardIngredientRecordsResultVO;
import com.nercar.ingredient.domain.vo.StandardIngredientRecordsVO;
import com.nercar.ingredient.response.PageDataResult;
import com.nercar.ingredient.response.Result;
import com.nercar.ingredient.service.StandardIngredientRecordsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Slf4j
@Tag(name = "历史配料接口")
@RestController
@RequestMapping("/standardIngredientRecords")
public class StandardIngredientRecordsController {


    @Autowired
    private StandardIngredientRecordsService standardIngredientRecordsService;

    @Operation(summary = "分页查询历史配料")
    @PostMapping("/getStandardIngredientRecords")
    public PageDataResult<StandardIngredientRecordsVO> getStandardIngredientRecords(@RequestBody StandardIngredientRecordsQueryDTO standardIngredientRecordsQueryDTO) {
        log.info("分页查询标准配料记录");
        IPage< StandardIngredientRecordsVO> result =standardIngredientRecordsService.selectRecords(standardIngredientRecordsQueryDTO);
        List<StandardIngredientRecordsVO> records = result.getRecords();
        long total = result.getTotal();
        if (ObjectUtils.isEmpty(records)) {
            log.warn("未找到符合给定条件的标准配料记录"); // 添加日志记录
            return PageDataResult.success(null, 0);
        }
        return PageDataResult.success(records, total);
    }


    @Operation(summary = "预览配料信息")
    @GetMapping("/previewIngredient/{id}")
    public Result<StandardIngredientRecordsResultVO> previewIngredient(@PathVariable String id) {
        log.info("预览配料信息");
        StandardIngredientRecordsResultVO result = standardIngredientRecordsService.previewIngredient(Long.parseLong(id));
        if (ObjectUtils.isEmpty(result)) {
            log.warn("未找到符合给定条件的标准配料记录"); // 添加日志记录
            return Result.ok();
        }
        return Result.success(result);
    }

    @Operation(summary="删除历史配料")
    @GetMapping("/deleteIngredient/{id}")
    public Result<String> deleteIngredient(@PathVariable Long id) {
        log.info("删除历史配料");
        boolean result = standardIngredientRecordsService.removeById(id);
        if (!result) {
            log.warn("删除历史配料失败"); // 添加日志记录
            return Result.failed("删除历史配料失败");
        }
        return Result.success("删除历史配料成功");
    }

    @Operation(summary="按照最新价格计算")//TODO 目前就是先修改一下最新价格，后面需要调用其他系统获取最新你价格，再去计算，返回主表id以及计算结果id
    @GetMapping("/getRecords/{id}")
    @Transactional(rollbackFor = Exception.class)
    public Result<String> updateByNewPrice(@PathVariable Long id) {
        log.info("按照最新价格计算");
        boolean result = standardIngredientRecordsService.updateByNewPrice(id);
        if (!result) {
            log.warn("按照最新价格计算失败"); // 添加日志记录
            return Result.failed("按照最新价格计算失败");
        }
        return Result.success("按照最新价格计算成功");

    }







}
