package com.nercar.ingredient.response;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 分页查询返回数据
 */
@Data
public class PageDataResult<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 状态码
     */
    private int code;

    /**
     * 成功状态
     */
    private boolean success;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 总数
     */
    private long total;

    /**
     * 数据封装
     */
    private List<T> rows;

    /**
     * 表格数据对象
     */
    public PageDataResult() {
    }

    private PageDataResult(int code, boolean success, String message, long total, List<T> rows) {
        this.code = code;
        this.success = success;
        this.message = message;
        this.total = total;
        this.rows = rows;
    }

    /**
     * 分页
     *
     * @param list  列表数据
     */
    public static <T> PageDataResult<T> success(List<T> list,long total) {
        PageDataResult<T> tPageDataResult = new PageDataResult<>();
        tPageDataResult.setCode(ResultCode.SUCCESS.getCode());
        tPageDataResult.setSuccess(Boolean.TRUE);
        tPageDataResult.setMessage((ResultCode.SUCCESS.getMessage()));
        tPageDataResult.setRows(list);
        tPageDataResult.setTotal(total);
        return tPageDataResult;
    }


    /**
     * 分页
     *
     */
    public static <T> PageDataResult<T> success(String message) {
        PageDataResult<T> tPageDataResult = new PageDataResult<>();
        tPageDataResult.setCode(ResultCode.SUCCESS.getCode());
        tPageDataResult.setSuccess(Boolean.TRUE);
        tPageDataResult.setMessage(message);
        tPageDataResult.setRows(List.of());
        tPageDataResult.setTotal(0);
        return tPageDataResult;
    }
}
