package com.nercar.ingredient.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 标准配料模板查询DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StandardTemplateQueryDTO extends BaseDTO {

    /**
     * 分页页码
     */
    @Schema(description = "分页页码，从1开始")
    private Integer pageNo = 1;

    /**
     * 分页大小
     */
    @Schema(description = "分页大小，默认10")
    private Integer pageSize = 10;

    /**
     * 路径名称（模糊查询）
     */
    @Schema(description = "路径名称，支持模糊查询")
    private String pathName;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private Long departmentId;

    /**
     * 部门名称（模糊查询）
     */
    @Schema(description = "部门名称，支持模糊查询")
    private String departmentName;

    /**
     * 评级筛选
     */
    @Schema(description = "评级筛选：1-5星级")
    private Integer rating;

    /**
     * 最小评级
     */
    @Schema(description = "最小评级，用于范围查询")
    private Integer minRating;

    /**
     * 最大评级
     */
    @Schema(description = "最大评级，用于范围查询")
    private Integer maxRating;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createuser;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段：rating, createtime, updatetime")
    private String sortField = "rating";

    /**
     * 排序方向
     */
    @Schema(description = "排序方向：asc, desc")
    private String sortOrder = "desc";
}
