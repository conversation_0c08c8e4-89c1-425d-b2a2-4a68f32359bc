00:02:29.145 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
00:02:29.152 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
00:02:34.061 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 40476 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:02:34.063 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:02:34.064 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
00:02:35.071 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:02:35.968 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
00:02:35.970 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:02:35.970 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:02:36.031 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:02:36.235 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:02:36.318 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@9d99851, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:02:36.440 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:02:36.471 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:02:36.489 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:02:36.508 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:02:36.525 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:02:36.552 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:02:36.569 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:02:36.595 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
00:02:36.601 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:02:36.602 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:02:36.612 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:02:36.688 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:02:36.715 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:02:36.745 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:02:36.769 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:02:36.790 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:02:36.804 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:02:36.823 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:02:36.838 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:02:36.851 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:02:36.862 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:02:36.865 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:02:36.865 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:02:36.870 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:02:36.874 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:02:36.874 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:02:36.879 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:02:36.890 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:02:36.902 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:02:37.054 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:02:37.062 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:02:37.068 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:6
00:02:38.507 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
00:02:38.529 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.878 seconds (process running for 7.191)
00:02:38.536 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
00:03:09.958 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:03:10.092 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:03:10.672 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@db316bf
00:03:10.674 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:03:10.684 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
00:03:10.738 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
00:03:10.862 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
00:03:10.872 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
00:03:10.873 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
00:03:10.937 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
00:03:10.940 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
00:03:10.941 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
00:03:11.015 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
00:03:11.018 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
00:03:11.018 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
00:03:11.079 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
00:03:11.185 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:03:11.186 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
00:03:11.254 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:03:11.255 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:03:11.256 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
00:03:11.324 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:03:11.324 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:03:11.324 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
00:03:11.443 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:03:11.443 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:03:11.444 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
00:03:11.506 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:03:11.506 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:03:11.506 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
00:03:11.565 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:03:11.565 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:03:11.566 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
00:03:11.626 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:03:11.627 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:03:11.628 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
00:03:11.687 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:03:11.688 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:03:11.688 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
00:03:11.755 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:03:11.755 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:03:11.756 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
00:03:11.815 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:03:11.816 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:03:11.816 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
00:03:11.877 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:03:11.878 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:03:11.878 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
00:03:11.938 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:03:11.939 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:03:11.940 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
00:03:11.998 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:03:12.002 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
00:03:12.003 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:03:12.060 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:03:12.061 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:03:12.062 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
00:03:12.128 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:03:12.128 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:03:12.129 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
00:03:12.186 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:03:12.187 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
00:03:12.188 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:03:12.247 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:03:12.247 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:03:12.248 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
00:03:12.312 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:03:12.313 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:03:12.314 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
00:03:12.375 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:12:13.202 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
00:12:13.206 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
00:13:45.435 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 41080 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:13:45.437 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:13:45.438 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
00:13:46.732 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:13:47.524 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
00:13:47.527 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:13:47.527 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:13:47.620 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:13:48.101 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:13:48.182 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@5da57a64, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:13:48.385 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:13:48.434 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:13:48.459 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:13:48.479 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:13:48.500 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:13:48.532 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:13:48.587 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:13:48.625 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
00:13:48.637 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:13:48.637 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:13:48.645 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:13:48.691 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:13:48.706 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:13:48.724 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:13:48.738 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:13:48.756 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:13:48.769 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:13:48.788 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:13:48.803 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:13:48.817 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:13:48.839 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:13:48.846 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:13:48.847 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:13:48.860 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:13:48.866 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:13:48.867 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:13:48.879 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:13:48.900 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:13:48.919 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:13:49.076 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:13:49.089 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:13:49.097 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:26
00:13:50.700 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
00:13:50.726 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 5.744 seconds (process running for 6.787)
00:13:50.736 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
00:13:50.813 [http-nio-9021-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:13:50.942 [http-nio-9021-exec-2] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:13:51.531 [http-nio-9021-exec-2] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@150dc353
00:13:51.533 [http-nio-9021-exec-2] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:13:51.541 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
00:13:51.590 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
00:13:51.839 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
00:13:51.848 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
00:13:51.848 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
00:13:51.920 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
00:13:51.921 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
00:13:51.922 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
00:13:51.985 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
00:13:51.987 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
00:13:51.987 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
00:13:52.046 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
00:13:52.112 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:13:52.112 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
00:13:52.169 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:13:52.170 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:13:52.170 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
00:13:52.235 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:13:52.235 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:13:52.236 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
00:13:52.296 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:13:52.296 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:13:52.297 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
00:13:52.356 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:13:52.356 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:13:52.357 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
00:13:52.414 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:13:52.415 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:13:52.415 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
00:13:52.507 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:13:52.507 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:13:52.507 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
00:13:52.566 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:13:52.567 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:13:52.567 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
00:13:52.624 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:13:52.625 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:13:52.626 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
00:13:52.690 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:13:52.691 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:13:52.692 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
00:13:52.750 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:13:52.751 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:13:52.751 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
00:13:52.807 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:13:52.809 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:13:52.810 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
00:13:52.867 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:13:52.870 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
00:13:52.871 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:13:52.935 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:13:52.936 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:13:52.936 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
00:13:52.995 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:13:52.995 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:13:52.995 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
00:13:53.057 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:13:53.059 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
00:13:53.059 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:13:53.123 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:13:53.124 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:13:53.125 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
00:13:53.197 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:13:53.199 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:13:53.199 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
00:13:53.261 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:14:59.103 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
00:14:59.105 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
00:16:07.891 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 16960 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:16:07.893 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:16:07.893 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
00:16:08.862 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:16:09.321 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
00:16:09.323 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:16:09.323 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:16:09.379 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:16:09.559 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:16:09.598 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@4438b862, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:16:09.705 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:16:09.735 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:16:09.750 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:16:09.764 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:16:09.778 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:16:09.793 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:16:09.807 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:16:09.838 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
00:16:09.845 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:16:09.845 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:16:09.852 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:16:09.895 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:16:09.908 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:16:09.922 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:16:09.933 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:16:09.943 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:16:09.956 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:16:09.974 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:16:09.990 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:16:10.005 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:16:10.018 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:16:10.021 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:16:10.022 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:16:10.028 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:16:10.032 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:16:10.033 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:16:10.039 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:16:10.053 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:16:10.067 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:16:10.194 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:16:10.203 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:16:10.209 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:29
00:16:11.618 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
00:16:11.644 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.143 seconds (process running for 5.26)
00:16:11.652 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
00:16:18.307 [http-nio-9021-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:16:18.408 [http-nio-9021-exec-2] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:16:18.969 [http-nio-9021-exec-2] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@73f78def
00:16:18.971 [http-nio-9021-exec-2] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:16:18.979 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
00:16:19.025 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
00:16:19.147 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
00:16:19.154 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
00:16:19.155 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
00:16:19.223 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
00:16:19.225 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
00:16:19.225 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
00:16:19.297 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
00:16:19.310 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
00:16:19.311 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
00:16:19.370 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
00:16:19.418 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:16:19.418 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
00:16:19.486 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:16:19.486 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:16:19.488 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
00:16:19.546 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:16:19.547 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:16:19.547 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
00:16:19.632 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:16:19.632 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:16:19.633 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
00:16:19.691 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:16:19.692 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:16:19.693 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
00:16:19.773 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:16:19.773 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:16:19.773 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
00:16:19.844 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:16:19.844 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:16:19.845 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
00:16:19.904 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:16:19.905 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:16:19.905 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
00:16:19.962 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:16:19.963 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:16:19.963 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
00:16:20.024 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:16:20.025 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:16:20.025 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
00:16:20.082 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:16:20.083 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:16:20.083 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
00:16:20.140 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:16:20.142 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:16:20.143 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
00:16:20.202 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:16:20.204 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
00:16:20.205 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:16:20.263 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:16:20.264 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:16:20.264 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
00:16:20.322 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:16:20.323 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:16:20.323 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
00:16:20.386 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:16:20.388 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
00:16:20.388 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:16:20.445 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:16:20.445 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:16:20.446 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
00:16:20.516 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:16:20.517 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:16:20.518 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
00:16:20.594 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:17:09.093 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
00:17:09.096 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
00:17:39.882 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 36020 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:17:39.885 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:17:39.886 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
00:17:40.809 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:17:41.293 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
00:17:41.295 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:17:41.295 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:17:41.350 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:17:41.530 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:17:41.570 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@37c2f0b4, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:17:41.693 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:17:41.729 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:17:41.754 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:17:41.774 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:17:41.791 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:17:41.807 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:17:41.830 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:17:41.851 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
00:17:41.854 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:17:41.855 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:17:41.860 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:17:41.892 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:17:41.904 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:17:41.918 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:17:41.930 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:17:41.941 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:17:41.954 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:17:41.970 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:17:41.987 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:17:42.002 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:17:42.021 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:17:42.028 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:17:42.029 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:17:42.039 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:17:42.044 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:17:42.044 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:17:42.052 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:17:42.067 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:17:42.083 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:17:42.223 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:17:42.233 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:17:42.238 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:26
00:17:43.912 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
00:17:43.954 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.483 seconds (process running for 5.435)
00:17:43.967 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
00:17:45.512 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:17:45.616 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:17:46.227 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1c8a91e7
00:17:46.229 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:17:46.236 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
00:17:46.283 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
00:17:46.385 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
00:17:46.403 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
00:17:46.404 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
00:17:46.467 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
00:17:46.469 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
00:17:46.469 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
00:17:46.538 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
00:17:46.539 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
00:17:46.539 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
00:17:46.608 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
00:17:46.657 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:17:46.657 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
00:17:46.726 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:17:46.726 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:17:46.727 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
00:17:46.798 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:17:46.798 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:17:46.799 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
00:17:46.872 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:17:46.872 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:17:46.872 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
00:17:46.932 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:17:46.932 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:17:46.933 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
00:17:46.997 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:17:46.998 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:17:46.998 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
00:17:47.062 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:17:47.062 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:17:47.063 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
00:17:47.130 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:17:47.131 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:17:47.131 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
00:17:47.193 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:17:47.194 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:17:47.194 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
00:17:47.256 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:17:47.257 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:17:47.257 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
00:17:47.318 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:17:47.319 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:17:47.319 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
00:17:47.379 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:17:47.381 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:17:47.382 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
00:17:47.444 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:17:47.447 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
00:17:47.448 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:17:47.520 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:17:47.520 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:17:47.521 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
00:17:47.584 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:17:47.585 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:17:47.585 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
00:17:47.645 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:17:47.647 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
00:17:47.647 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:17:47.723 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:17:47.724 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:17:47.724 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
00:17:47.788 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:17:47.789 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:17:47.789 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
00:17:47.851 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:18:25.283 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
00:18:25.286 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
00:18:45.489 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 27512 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:18:45.492 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:18:45.494 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
00:18:46.524 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:18:47.008 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
00:18:47.011 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:18:47.011 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:18:47.077 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:18:47.268 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:18:47.309 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@35dcd032, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:18:47.418 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:18:47.452 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:18:47.468 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:18:47.484 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:18:47.499 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:18:47.515 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:18:47.533 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:18:47.559 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
00:18:47.563 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:18:47.564 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:18:47.569 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:18:47.604 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:18:47.618 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:18:47.631 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:18:47.641 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:18:47.651 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:18:47.666 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:18:47.682 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:18:47.696 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:18:47.711 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:18:47.728 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:18:47.733 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:18:47.733 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:18:47.740 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:18:47.745 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:18:47.746 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:18:47.753 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:18:47.768 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:18:47.784 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:18:47.910 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:18:47.919 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:18:47.924 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:0
00:18:49.293 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
00:18:49.315 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.307 seconds (process running for 5.509)
00:18:49.324 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
00:19:39.266 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 42320 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:19:39.269 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:19:39.269 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
00:19:40.253 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:19:40.713 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
00:19:40.715 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:19:40.715 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:19:40.771 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:19:40.960 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:19:41.000 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@4536a715, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:19:41.114 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:19:41.149 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:19:41.171 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:19:41.193 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:19:41.217 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:19:41.237 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:19:41.264 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:19:41.293 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
00:19:41.297 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:19:41.298 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:19:41.306 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:19:41.357 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:19:41.377 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:19:41.393 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:19:41.405 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:19:41.424 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:19:41.439 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:19:41.463 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:19:41.477 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:19:41.491 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:19:41.506 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:19:41.511 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:19:41.511 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:19:41.517 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:19:41.521 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:19:41.521 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:19:41.527 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:19:41.540 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:19:41.555 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:19:41.685 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:19:41.693 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:19:41.698 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:2
00:19:43.047 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
00:19:43.067 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.236 seconds (process running for 5.423)
00:19:43.075 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
00:19:45.473 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:19:45.600 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:19:46.204 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@30605cdb
00:19:46.207 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:19:46.216 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
00:19:46.263 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
00:19:46.376 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
00:19:46.383 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
00:19:46.384 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
00:19:46.449 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
00:19:46.451 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
00:19:46.452 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
00:19:46.535 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
00:19:46.536 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
00:19:46.537 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
00:19:46.601 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
00:19:46.649 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:19:46.649 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
00:19:46.725 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:19:46.726 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:19:46.726 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
00:19:46.791 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:19:46.791 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:19:46.792 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
00:19:46.859 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:19:46.860 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:19:46.860 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
00:19:46.923 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:19:46.923 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:19:46.924 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
00:19:46.989 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:19:46.991 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:19:46.991 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
00:19:47.051 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:19:47.051 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:19:47.051 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
00:19:47.113 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:19:47.114 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:19:47.114 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
00:19:47.171 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:19:47.172 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:19:47.173 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
00:19:47.233 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:19:47.234 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:19:47.234 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
00:19:47.293 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:19:47.294 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:19:47.294 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
00:19:47.354 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:19:47.355 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:19:47.356 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
00:19:47.416 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:19:47.419 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
00:19:47.419 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:19:47.478 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:19:47.479 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:19:47.479 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
00:19:47.546 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:19:47.546 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:19:47.547 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
00:19:47.616 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:19:47.618 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
00:19:47.618 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:19:47.680 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:19:47.681 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:19:47.682 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
00:19:47.750 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:19:47.751 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:19:47.752 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
00:19:47.811 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:28:48.177 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 36968 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:28:48.180 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:28:48.181 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
00:28:49.263 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:28:49.760 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
00:28:49.762 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:28:49.762 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:28:49.819 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:28:50.003 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:28:50.041 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@1736c1e4, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:28:50.152 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:28:50.183 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:28:50.197 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:28:50.212 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:28:50.230 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:28:50.245 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:28:50.260 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:28:50.292 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
00:28:50.296 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:28:50.296 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:28:50.302 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:28:50.336 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:28:50.349 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:28:50.363 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:28:50.374 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:28:50.385 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:28:50.397 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:28:50.415 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:28:50.440 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:28:50.458 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:28:50.470 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:28:50.474 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:28:50.474 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:28:50.482 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:28:50.486 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:28:50.486 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:28:50.494 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:28:50.509 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:28:50.522 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:28:50.652 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:28:50.661 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:28:50.666 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:7
00:28:52.255 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
00:28:52.285 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.631 seconds (process running for 6.174)
00:28:52.294 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
00:28:57.129 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:28:57.306 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:28:57.931 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5b0e46b4
00:28:57.933 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:28:57.940 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
00:28:57.986 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
00:28:58.100 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
00:28:58.121 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
00:28:58.122 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
00:28:58.194 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
00:28:58.196 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
00:28:58.197 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
00:28:58.269 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
00:28:58.271 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
00:28:58.271 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
00:28:58.341 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
00:28:58.392 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:28:58.393 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
00:28:58.480 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:28:58.481 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:28:58.482 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
00:28:58.549 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:28:58.549 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:28:58.550 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
00:28:58.630 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:28:58.631 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:28:58.631 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
00:28:58.704 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:28:58.705 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:28:58.705 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
00:28:58.774 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:28:58.775 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:28:58.775 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
00:28:58.844 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:28:58.845 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:28:58.845 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
00:28:58.913 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:28:58.913 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:28:58.914 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
00:28:58.986 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:28:58.987 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:28:58.988 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
00:28:59.054 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:28:59.055 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:28:59.055 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
00:28:59.124 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:28:59.124 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:28:59.125 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
00:28:59.189 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:28:59.191 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:28:59.192 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
00:28:59.273 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:28:59.276 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
00:28:59.277 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:28:59.344 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:28:59.345 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:28:59.345 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
00:28:59.413 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:28:59.414 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:28:59.414 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
00:28:59.483 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:28:59.484 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
00:28:59.484 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:28:59.556 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:28:59.557 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:28:59.558 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
00:28:59.628 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:28:59.629 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:28:59.629 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
00:28:59.706 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:29:00.168 [http-nio-9021-exec-1] ERROR c.n.i.s.i.StandardIngredientRecordsServiceImpl - [download,674] - 导出成本测算流程表失败
java.lang.NullPointerException: Cannot invoke "org.apache.poi.xssf.usermodel.XSSFCell.setCellValue(String)" because the return value of "org.apache.poi.xssf.usermodel.XSSFRow.getCell(int)" is null
	at com.nercar.ingredient.service.impl.StandardIngredientRecordsServiceImpl.download(StandardIngredientRecordsServiceImpl.java:651)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:716)
	at com.nercar.ingredient.service.impl.StandardIngredientRecordsServiceImpl$$SpringCGLIB$$0.download(<generated>)
	at com.nercar.ingredient.controller.IngredientCalculationController.download(IngredientCalculationController.java:292)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:833)
00:29:00.173 [http-nio-9021-exec-1] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 导出成本测算流程表失败: Cannot invoke "org.apache.poi.xssf.usermodel.XSSFCell.setCellValue(String)" because the return value of "org.apache.poi.xssf.usermodel.XSSFRow.getCell(int)" is null
00:30:09.061 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
00:30:09.064 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
00:30:15.514 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 42528 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:30:15.517 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:30:15.518 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
00:30:16.609 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:30:17.067 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
00:30:17.070 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:30:17.070 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:30:17.125 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:30:17.327 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:30:17.378 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@4438b862, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:30:17.487 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:30:17.523 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:30:17.538 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:30:17.555 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:30:17.572 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:30:17.596 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:30:17.613 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:30:17.634 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
00:30:17.638 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:30:17.638 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:30:17.644 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:30:17.678 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:30:17.692 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:30:17.705 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:30:17.715 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:30:17.728 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:30:17.739 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:30:17.755 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:30:17.774 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:30:17.790 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:30:17.803 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:30:17.806 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:30:17.807 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:30:17.813 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:30:17.817 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:30:17.817 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:30:17.822 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:30:17.835 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:30:17.850 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:30:17.978 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:30:17.987 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:30:17.993 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:12
00:30:19.417 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
00:30:19.435 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.433 seconds (process running for 5.384)
00:30:19.443 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
00:30:21.981 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:30:22.102 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:30:22.690 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1c8a91e7
00:30:22.692 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:30:22.699 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
00:30:22.745 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
00:30:22.865 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
00:30:22.875 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
00:30:22.875 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
00:30:22.945 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
00:30:22.947 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
00:30:22.948 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
00:30:23.014 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
00:30:23.015 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
00:30:23.016 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
00:30:23.085 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
00:30:23.132 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:30:23.132 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
00:30:23.192 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:30:23.193 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:30:23.193 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
00:30:23.251 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:30:23.251 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:30:23.251 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
00:30:23.316 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:30:23.317 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:30:23.317 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
00:30:23.376 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:30:23.377 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:30:23.377 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
00:30:23.436 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:30:23.437 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:30:23.437 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
00:30:23.496 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:30:23.496 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:30:23.496 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
00:30:23.554 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:30:23.554 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:30:23.555 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
00:30:23.619 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:30:23.620 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:30:23.620 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
00:30:23.677 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:30:23.678 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:30:23.678 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
00:30:23.745 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:30:23.745 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:30:23.745 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
00:30:23.808 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:30:23.810 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:30:23.811 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
00:30:23.877 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:30:23.880 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
00:30:23.880 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:30:23.940 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:30:23.941 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:30:23.942 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
00:30:24.002 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:30:24.003 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:30:24.003 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
00:30:24.066 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:30:24.067 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
00:30:24.068 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:30:24.126 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:30:24.127 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:30:24.127 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
00:30:24.187 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:30:24.188 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:30:24.188 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
00:30:24.252 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:31:48.757 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
00:31:48.760 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
00:37:09.862 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 36532 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:37:09.864 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:37:09.864 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
00:37:11.115 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:37:11.587 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
00:37:11.589 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:37:11.590 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:37:11.661 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:37:11.880 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:37:11.925 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@4536a715, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:37:12.075 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:37:12.140 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:37:12.166 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:37:12.190 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:37:12.213 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:37:12.231 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:37:12.256 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:37:12.289 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
00:37:12.295 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:37:12.295 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:37:12.303 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:37:12.363 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:37:12.399 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:37:12.447 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:37:12.469 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:37:12.497 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:37:12.527 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:37:12.571 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:37:12.597 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:37:12.615 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:37:12.629 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:37:12.634 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:37:12.634 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:37:12.642 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:37:12.646 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:37:12.646 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:37:12.659 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:37:12.683 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:37:12.705 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:37:12.841 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:37:12.849 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:37:12.854 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:0
00:37:14.319 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
00:37:14.340 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.859 seconds (process running for 5.807)
00:37:14.348 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
00:37:28.705 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:37:28.808 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:37:29.424 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5d2039f4
00:37:29.425 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:37:29.444 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
00:37:29.490 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
00:37:29.597 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
00:37:29.615 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
00:37:29.615 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
00:37:29.687 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
00:37:29.689 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
00:37:29.689 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
00:37:29.759 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
00:37:29.761 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
00:37:29.761 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
00:37:29.828 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
00:37:29.879 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:37:29.880 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
00:37:29.944 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:37:29.945 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:37:29.945 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
00:37:30.010 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:37:30.011 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:37:30.011 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
00:37:30.075 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:37:30.076 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:37:30.076 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
00:37:30.151 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:37:30.152 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:37:30.152 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
00:37:30.217 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:37:30.218 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:37:30.218 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
00:37:30.280 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:37:30.281 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:37:30.281 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
00:37:30.358 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:37:30.359 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:37:30.359 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
00:37:30.425 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:37:30.426 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:37:30.426 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
00:37:30.490 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:37:30.491 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:37:30.491 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
00:37:30.566 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:37:30.567 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:37:30.567 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
00:37:30.624 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:37:30.625 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:37:30.626 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
00:37:30.690 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:37:30.693 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
00:37:30.694 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:37:30.761 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:37:30.761 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:37:30.762 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
00:37:30.822 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:37:30.823 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:37:30.824 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
00:37:30.887 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:37:30.889 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
00:37:30.889 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:37:30.969 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:37:30.970 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:37:30.971 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
00:37:31.038 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:37:31.039 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:37:31.040 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
00:37:31.106 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:40:06.850 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
00:40:06.859 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
00:45:31.219 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 42844 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:45:31.222 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:45:31.224 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
00:45:32.529 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:45:33.017 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
00:45:33.019 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:45:33.020 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:45:33.080 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:45:33.272 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:45:33.311 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@4438b862, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:45:33.435 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:45:33.469 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:45:33.505 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:45:33.529 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:45:33.553 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:45:33.572 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:45:33.594 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:45:33.614 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
00:45:33.618 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:45:33.618 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:45:33.624 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:45:33.658 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:45:33.673 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:45:33.690 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:45:33.701 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:45:33.717 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:45:33.731 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:45:33.750 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:45:33.764 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:45:33.779 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:45:33.793 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:45:33.796 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:45:33.797 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:45:33.804 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:45:33.807 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:45:33.808 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:45:33.814 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:45:33.828 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:45:33.841 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:45:33.975 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:45:33.985 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:45:33.991 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:9
00:45:35.424 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
00:45:35.445 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.763 seconds (process running for 5.882)
00:45:35.454 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
00:45:40.138 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:45:40.269 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:45:40.873 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@f4244e3
00:45:40.874 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:45:40.882 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
00:45:40.928 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
00:45:41.074 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
00:45:41.094 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
00:45:41.095 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
00:45:41.182 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
00:45:41.184 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
00:45:41.185 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
00:45:41.252 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
00:45:41.253 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
00:45:41.254 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
00:45:41.318 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
00:45:41.375 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:45:41.375 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
00:45:41.435 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:45:41.435 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:45:41.436 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
00:45:41.500 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:45:41.501 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:45:41.501 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
00:45:41.566 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:45:41.567 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:45:41.567 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
00:45:41.643 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:45:41.643 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:45:41.644 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
00:45:41.715 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:45:41.716 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:45:41.716 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
00:45:41.788 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:45:41.789 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:45:41.789 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
00:45:41.865 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:45:41.866 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:45:41.866 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
00:45:41.934 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:45:41.935 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:45:41.935 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
00:45:42.002 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:45:42.002 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:45:42.003 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
00:45:42.064 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:45:42.065 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:45:42.065 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
00:45:42.133 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:45:42.135 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:45:42.136 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
00:45:42.200 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:45:42.203 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
00:45:42.203 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:45:42.265 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:45:42.265 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:45:42.266 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
00:45:42.329 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:45:42.329 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:45:42.330 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
00:45:42.393 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:45:42.394 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
00:45:42.395 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:45:42.460 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:45:42.461 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:45:42.461 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
00:45:42.528 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:45:42.529 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:45:42.530 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
00:45:42.598 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:45:43.017 [http-nio-9021-exec-1] ERROR c.n.i.s.i.StandardIngredientRecordsServiceImpl - [download,701] - 导出成本测算流程表失败
java.lang.NullPointerException: Cannot invoke "String.trim()" because "in" is null
	at java.base/jdk.internal.math.FloatingDecimal.readJavaFormatString(FloatingDecimal.java:1838)
	at java.base/jdk.internal.math.FloatingDecimal.parseDouble(FloatingDecimal.java:110)
	at java.base/java.lang.Double.parseDouble(Double.java:651)
	at com.nercar.ingredient.service.impl.StandardIngredientRecordsServiceImpl.download(StandardIngredientRecordsServiceImpl.java:666)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:716)
	at com.nercar.ingredient.service.impl.StandardIngredientRecordsServiceImpl$$SpringCGLIB$$0.download(<generated>)
	at com.nercar.ingredient.controller.IngredientCalculationController.download(IngredientCalculationController.java:292)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:833)
00:45:43.021 [http-nio-9021-exec-1] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 导出成本测算流程表失败: Cannot invoke "String.trim()" because "in" is null
00:46:26.646 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
00:46:26.649 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
00:46:35.132 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 36664 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:46:35.135 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:46:35.136 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
00:46:36.104 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:46:36.578 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
00:46:36.580 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:46:36.580 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:46:36.642 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:46:36.834 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:46:36.879 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@738a815c, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:46:37.001 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:46:37.034 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:46:37.055 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:46:37.075 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:46:37.094 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:46:37.114 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:46:37.137 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:46:37.160 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
00:46:37.165 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:46:37.165 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:46:37.173 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:46:37.218 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:46:37.236 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:46:37.256 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:46:37.274 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:46:37.296 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:46:37.317 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:46:37.339 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:46:37.356 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:46:37.369 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:46:37.381 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:46:37.384 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:46:37.384 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:46:37.389 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:46:37.392 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:46:37.392 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:46:37.397 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:46:37.407 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:46:37.420 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:46:37.556 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:46:37.567 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:46:37.572 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:0
00:46:38.931 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
00:46:38.955 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.26 seconds (process running for 5.604)
00:46:38.962 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
00:46:41.204 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:46:41.350 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:46:41.988 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@24964edc
00:46:41.990 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:46:41.998 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
00:46:42.043 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
00:46:42.145 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
00:46:42.152 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
00:46:42.153 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
00:46:42.215 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
00:46:42.217 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
00:46:42.217 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
00:46:42.294 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
00:46:42.296 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
00:46:42.297 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
00:46:42.367 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
00:46:42.416 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:46:42.416 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
00:46:42.475 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:46:42.476 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:46:42.477 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
00:46:42.542 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:46:42.543 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:46:42.544 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
00:46:42.602 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:46:42.603 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:46:42.603 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
00:46:42.662 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:46:42.662 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:46:42.662 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
00:46:42.754 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:46:42.755 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:46:42.755 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
00:46:42.814 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:46:42.815 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:46:42.815 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
00:46:42.872 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:46:42.872 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:46:42.872 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
00:46:42.962 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:46:42.963 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:46:42.964 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
00:46:43.037 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:46:43.038 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:46:43.038 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
00:46:43.101 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:46:43.102 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:46:43.102 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
00:46:43.158 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:46:43.160 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:46:43.161 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
00:46:43.227 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:46:43.230 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
00:46:43.231 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:46:43.292 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:46:43.293 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:46:43.293 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
00:46:43.355 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:46:43.356 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:46:43.356 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
00:46:43.422 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:46:43.423 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
00:46:43.423 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:46:43.491 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:46:43.493 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:46:43.493 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
00:46:43.555 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:46:43.556 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:46:43.556 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
00:46:43.618 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:46:44.115 [http-nio-9021-exec-1] ERROR c.n.i.s.i.StandardIngredientRecordsServiceImpl - [download,701] - 导出成本测算流程表失败
java.lang.NullPointerException: Cannot invoke "String.trim()" because "in" is null
	at java.base/jdk.internal.math.FloatingDecimal.readJavaFormatString(FloatingDecimal.java:1838)
	at java.base/jdk.internal.math.FloatingDecimal.parseDouble(FloatingDecimal.java:110)
	at java.base/java.lang.Double.parseDouble(Double.java:651)
	at com.nercar.ingredient.service.impl.StandardIngredientRecordsServiceImpl.download(StandardIngredientRecordsServiceImpl.java:666)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:716)
	at com.nercar.ingredient.service.impl.StandardIngredientRecordsServiceImpl$$SpringCGLIB$$0.download(<generated>)
	at com.nercar.ingredient.controller.IngredientCalculationController.download(IngredientCalculationController.java:292)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:833)
00:46:44.122 [http-nio-9021-exec-1] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 导出成本测算流程表失败: Cannot invoke "String.trim()" because "in" is null
00:47:39.916 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
00:47:39.919 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
00:47:47.389 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 15904 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:47:47.392 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:47:47.393 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
00:47:48.319 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:47:48.790 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
00:47:48.792 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:47:48.792 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:47:48.849 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:47:49.035 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:47:49.075 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@64dfb31d, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:47:49.187 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:47:49.218 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:47:49.235 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:47:49.256 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:47:49.279 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:47:49.300 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:47:49.325 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:47:49.355 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
00:47:49.359 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:47:49.359 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:47:49.366 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:47:49.409 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:47:49.429 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:47:49.452 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:47:49.466 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:47:49.484 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:47:49.501 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:47:49.525 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:47:49.545 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:47:49.561 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:47:49.575 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:47:49.579 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:47:49.579 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:47:49.585 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:47:49.588 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:47:49.589 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:47:49.595 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:47:49.604 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:47:49.616 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:47:49.746 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:47:49.755 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:47:49.761 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:8
00:47:51.161 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
00:47:51.184 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.256 seconds (process running for 5.403)
00:47:51.192 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
00:47:53.397 [http-nio-9021-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:47:53.534 [http-nio-9021-exec-2] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:47:54.086 [http-nio-9021-exec-2] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6aba2a39
00:47:54.088 [http-nio-9021-exec-2] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:47:54.096 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
00:47:54.164 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
00:47:54.279 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
00:47:54.288 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
00:47:54.289 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
00:47:54.352 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
00:47:54.355 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
00:47:54.356 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
00:47:54.437 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
00:47:54.438 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
00:47:54.439 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
00:47:54.521 [http-nio-9021-exec-2] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
00:47:54.588 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:47:54.588 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
00:47:54.646 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:47:54.647 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:47:54.647 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
00:47:54.714 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:47:54.714 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:47:54.714 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
00:47:54.773 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:47:54.774 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:47:54.774 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
00:47:54.847 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:47:54.848 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:47:54.848 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
00:47:54.912 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:47:54.913 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:47:54.913 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
00:47:54.978 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:47:54.978 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:47:54.979 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
00:47:55.038 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:47:55.038 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:47:55.038 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
00:47:55.098 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:47:55.098 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:47:55.099 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
00:47:55.157 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:47:55.157 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:47:55.157 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
00:47:55.215 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:47:55.215 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:47:55.216 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
00:47:55.272 [http-nio-9021-exec-2] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:47:55.274 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:47:55.275 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
00:47:55.331 [http-nio-9021-exec-2] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:47:55.334 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
00:47:55.335 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:47:55.392 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:47:55.393 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:47:55.393 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
00:47:55.452 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:47:55.453 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:47:55.453 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
00:47:55.514 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:47:55.516 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
00:47:55.516 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:47:55.573 [http-nio-9021-exec-2] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:47:55.574 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:47:55.574 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
00:47:55.635 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:47:55.636 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:47:55.636 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
00:47:55.695 [http-nio-9021-exec-2] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:54:01.402 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
00:54:01.403 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
00:54:01.464 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
00:54:01.465 [http-nio-9021-exec-3] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
00:54:01.465 [http-nio-9021-exec-3] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
00:54:01.529 [http-nio-9021-exec-3] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
00:54:01.530 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
00:54:01.531 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
00:54:01.597 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
00:54:01.600 [http-nio-9021-exec-3] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
00:54:01.601 [http-nio-9021-exec-3] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
00:54:01.663 [http-nio-9021-exec-3] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
00:54:01.666 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:54:01.667 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
00:54:01.725 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:54:01.726 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:54:01.726 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
00:54:01.781 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:54:01.782 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:54:01.782 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
00:54:01.836 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:54:01.837 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:54:01.837 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
00:54:01.891 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:54:01.892 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:54:01.892 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
00:54:01.946 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:54:01.947 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:54:01.948 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
00:54:02.006 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:54:02.007 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:54:02.007 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
00:54:02.062 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:54:02.062 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:54:02.063 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
00:54:02.115 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:54:02.116 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:54:02.117 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
00:54:02.171 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:54:02.172 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:54:02.172 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
00:54:02.243 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:54:02.243 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:54:02.244 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
00:54:02.297 [http-nio-9021-exec-3] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:54:02.298 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:54:02.298 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
00:54:02.351 [http-nio-9021-exec-3] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:54:02.353 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
00:54:02.353 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:54:02.421 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:54:02.422 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:54:02.422 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
00:54:02.477 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:54:02.478 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:54:02.478 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
00:54:02.532 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:54:02.533 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
00:54:02.534 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:54:02.590 [http-nio-9021-exec-3] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:54:02.590 [http-nio-9021-exec-3] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:54:02.591 [http-nio-9021-exec-3] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
00:54:02.651 [http-nio-9021-exec-3] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:54:02.652 [http-nio-9021-exec-3] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:54:02.653 [http-nio-9021-exec-3] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
00:54:02.718 [http-nio-9021-exec-3] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:54:30.650 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
00:54:30.653 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
00:54:34.825 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 15696 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:54:34.830 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:54:34.831 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
00:54:35.941 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:54:36.459 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
00:54:36.460 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:54:36.461 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:54:36.520 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:54:36.693 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:54:36.744 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@32dcfeea, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:54:36.862 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:54:36.900 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:54:36.921 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:54:36.939 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:54:36.957 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:54:36.973 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:54:36.987 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:54:37.009 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
00:54:37.014 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:54:37.014 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:54:37.026 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:54:37.070 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:54:37.086 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:54:37.102 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:54:37.120 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:54:37.136 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:54:37.154 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:54:37.177 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:54:37.190 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:54:37.208 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:54:37.225 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:54:37.229 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:54:37.229 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:54:37.235 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:54:37.238 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:54:37.238 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:54:37.243 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:54:37.254 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:54:37.265 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:54:37.412 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:54:37.420 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:54:37.426 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:30
00:54:38.886 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
00:54:38.914 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.77 seconds (process running for 6.949)
00:54:38.922 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
00:54:41.111 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:54:41.246 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:54:41.851 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@57405f4b
00:54:41.852 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:54:41.861 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
00:54:41.910 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
00:54:42.039 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
00:54:42.048 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
00:54:42.048 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
00:54:42.120 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
00:54:42.122 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
00:54:42.123 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
00:54:42.199 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
00:54:42.201 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
00:54:42.201 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
00:54:42.266 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
00:54:42.334 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:54:42.335 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
00:54:42.399 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:54:42.400 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:54:42.401 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
00:54:42.464 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:54:42.465 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:54:42.465 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
00:54:42.534 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:54:42.535 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:54:42.535 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
00:54:42.596 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:54:42.597 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:54:42.598 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
00:54:42.694 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:54:42.695 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:54:42.695 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
00:54:42.755 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:54:42.756 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:54:42.756 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
00:54:42.818 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:54:42.819 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:54:42.819 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
00:54:42.882 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:54:42.882 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:54:42.882 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
00:54:42.950 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:54:42.950 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:54:42.951 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
00:54:43.012 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:54:43.013 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:54:43.014 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
00:54:43.073 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:54:43.074 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:54:43.075 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
00:54:43.132 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:54:43.135 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,device,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
00:54:43.136 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:54:43.200 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:54:43.201 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:54:43.201 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
00:54:43.260 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:54:43.261 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:54:43.261 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
00:54:43.323 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:54:43.325 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
00:54:43.326 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:54:43.392 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:54:43.394 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:54:43.394 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
00:54:43.459 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:54:43.460 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:54:43.460 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
00:54:43.527 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:56:30.674 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
00:56:30.678 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
00:56:38.121 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 14740 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:56:38.124 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:56:38.124 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
00:56:39.085 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:56:39.549 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
00:56:39.551 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:56:39.551 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:56:39.609 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:56:39.796 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:56:39.834 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@37c2f0b4, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:56:39.946 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:56:39.976 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:56:39.993 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:56:40.015 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:56:40.035 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:56:40.055 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:56:40.077 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:56:40.109 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
00:56:40.114 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:56:40.115 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:56:40.121 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:56:40.161 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:56:40.180 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:56:40.197 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:56:40.208 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:56:40.218 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:56:40.235 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:56:40.254 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:56:40.268 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:56:40.282 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:56:40.295 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:56:40.299 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:56:40.299 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:56:40.304 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:56:40.307 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:56:40.308 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:56:40.313 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:56:40.326 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:56:40.337 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:56:40.465 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:56:40.473 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:56:40.479 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:31
00:56:41.837 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
00:56:41.872 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.102 seconds (process running for 5.184)
00:56:41.882 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
00:56:46.421 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:56:46.534 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:56:47.161 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@f4244e3
00:56:47.162 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:56:47.170 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
00:56:47.215 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
00:56:47.320 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
00:56:47.340 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
00:56:47.341 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
00:56:47.403 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
00:56:47.406 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
00:56:47.407 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
00:56:47.471 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
00:56:47.473 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
00:56:47.473 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
00:56:47.536 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
00:56:47.583 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:56:47.584 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
00:56:47.650 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:56:47.651 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:56:47.651 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
00:56:47.708 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:56:47.708 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:56:47.708 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
00:56:47.770 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:56:47.771 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:56:47.772 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
00:56:47.834 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:56:47.835 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:56:47.835 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
00:56:47.897 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:56:47.897 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:56:47.898 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
00:56:47.970 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:56:47.971 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:56:47.971 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
00:56:48.043 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:56:48.043 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:56:48.043 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
00:56:48.111 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:56:48.112 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:56:48.112 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
00:56:48.172 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:56:48.173 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:56:48.173 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
00:56:48.240 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:56:48.240 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:56:48.242 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
00:56:48.297 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:56:48.298 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:56:48.299 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
00:56:48.359 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:56:48.362 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,device,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
00:56:48.362 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:56:48.428 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:56:48.428 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:56:48.428 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
00:56:48.496 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:56:48.497 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:56:48.497 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
00:56:48.554 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:56:48.556 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
00:56:48.556 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:56:48.620 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:56:48.621 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:56:48.621 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
00:56:48.683 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:56:48.684 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:56:48.684 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
00:56:48.750 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:58:31.774 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
00:58:31.777 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
00:58:39.511 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 38212 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
00:58:39.513 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
00:58:39.514 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
00:58:40.780 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:58:41.340 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
00:58:41.342 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:58:41.343 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
00:58:41.403 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:58:41.597 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
00:58:41.652 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@1736c1e4, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
00:58:41.875 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
00:58:41.925 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
00:58:41.954 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
00:58:41.974 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
00:58:41.990 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
00:58:42.009 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
00:58:42.035 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
00:58:42.060 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
00:58:42.064 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
00:58:42.064 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:58:42.071 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
00:58:42.119 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
00:58:42.141 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
00:58:42.161 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
00:58:42.179 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
00:58:42.196 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
00:58:42.209 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
00:58:42.228 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
00:58:42.242 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
00:58:42.257 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
00:58:42.272 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
00:58:42.275 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
00:58:42.276 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:58:42.282 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
00:58:42.287 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
00:58:42.287 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
00:58:42.293 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
00:58:42.310 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
00:58:42.329 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
00:58:42.483 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
00:58:42.492 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
00:58:42.498 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:29
00:58:44.004 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
00:58:44.024 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.935 seconds (process running for 5.84)
00:58:44.034 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
00:58:56.200 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:58:56.405 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
00:58:57.106 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4f44a0f4
00:58:57.108 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
00:58:57.116 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
00:58:57.162 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
00:58:57.265 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
00:58:57.283 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
00:58:57.283 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
00:58:57.379 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
00:58:57.381 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
00:58:57.382 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
00:58:57.448 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
00:58:57.450 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
00:58:57.450 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
00:58:57.511 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
00:58:57.559 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:58:57.560 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
00:58:57.620 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:58:57.621 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:58:57.621 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
00:58:57.680 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:58:57.681 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:58:57.681 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
00:58:57.743 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:58:57.744 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:58:57.744 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
00:58:57.816 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:58:57.817 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:58:57.817 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
00:58:57.878 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:58:57.879 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:58:57.879 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
00:58:57.941 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:58:57.941 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:58:57.941 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
00:58:58.008 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:58:58.008 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:58:58.009 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
00:58:58.084 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:58:58.084 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:58:58.084 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
00:58:58.145 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:58:58.145 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:58:58.145 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
00:58:58.203 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:58:58.203 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
00:58:58.204 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
00:58:58.270 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
00:58:58.273 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
00:58:58.274 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
00:58:58.334 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
00:58:58.337 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,device,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
00:58:58.338 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:58:58.404 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:58:58.405 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:58:58.405 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
00:58:58.463 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:58:58.464 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
00:58:58.464 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
00:58:58.526 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
00:58:58.528 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
00:58:58.529 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
00:58:58.597 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
00:58:58.598 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:58:58.598 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
00:58:58.672 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
00:58:58.672 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
00:58:58.673 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
00:58:58.734 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
01:02:53.672 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
01:02:53.675 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
01:08:49.466 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 40800 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
01:08:49.469 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
01:08:49.469 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
01:08:50.712 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
01:08:51.200 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
01:08:51.202 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
01:08:51.202 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
01:08:51.261 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
01:08:51.477 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
01:08:51.522 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@7e27f603, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
01:08:51.661 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
01:08:51.713 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
01:08:51.745 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
01:08:51.780 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
01:08:51.812 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
01:08:51.841 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
01:08:51.875 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
01:08:51.910 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
01:08:51.915 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
01:08:51.915 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
01:08:51.928 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
01:08:51.980 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
01:08:51.997 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
01:08:52.012 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
01:08:52.024 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
01:08:52.037 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
01:08:52.048 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
01:08:52.063 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
01:08:52.074 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
01:08:52.084 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
01:08:52.095 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
01:08:52.099 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
01:08:52.099 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
01:08:52.104 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
01:08:52.107 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
01:08:52.108 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
01:08:52.113 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
01:08:52.126 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
01:08:52.140 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
01:08:52.280 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
01:08:52.288 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
01:08:52.292 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:11
01:08:53.664 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
01:08:53.682 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.711 seconds (process running for 5.717)
01:08:53.690 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
01:08:57.371 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
01:08:57.495 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
01:08:58.043 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6adb728
01:08:58.045 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
01:08:58.053 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
01:08:58.102 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
01:08:58.210 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
01:08:58.231 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
01:08:58.232 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
01:08:58.298 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
01:08:58.301 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
01:08:58.301 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
01:08:58.369 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
01:08:58.370 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
01:08:58.371 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
01:08:58.432 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
01:08:58.482 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:08:58.483 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
01:08:58.539 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:08:58.539 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:08:58.540 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
01:08:58.594 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:08:58.595 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:08:58.595 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
01:08:58.651 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:08:58.651 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:08:58.652 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
01:08:58.709 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:08:58.710 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:08:58.710 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
01:08:58.763 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:08:58.764 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:08:58.764 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
01:08:58.819 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:08:58.820 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:08:58.820 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
01:08:58.875 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:08:58.876 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:08:58.876 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
01:08:58.936 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:08:58.936 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:08:58.937 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
01:08:58.988 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:08:58.989 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:08:58.989 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
01:08:59.043 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:08:59.043 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:08:59.044 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
01:08:59.108 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:08:59.109 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:08:59.110 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
01:08:59.165 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:08:59.167 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,device,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
01:08:59.168 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
01:08:59.221 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
01:08:59.222 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
01:08:59.222 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
01:08:59.293 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
01:08:59.294 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
01:08:59.294 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
01:08:59.351 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
01:08:59.353 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
01:08:59.354 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
01:08:59.407 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
01:08:59.408 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
01:08:59.408 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
01:08:59.513 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
01:08:59.514 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
01:08:59.514 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
01:08:59.576 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
01:09:00.004 [http-nio-9021-exec-1] ERROR c.n.i.s.i.StandardIngredientRecordsServiceImpl - [download,717] - 导出成本测算流程表失败
java.lang.IndexOutOfBoundsException: Index 2 out of bounds for length 2
	at java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:64)
	at java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:70)
	at java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:266)
	at java.base/java.util.Objects.checkIndex(Objects.java:359)
	at java.base/java.util.ArrayList.get(ArrayList.java:427)
	at com.nercar.ingredient.service.impl.StandardIngredientRecordsServiceImpl.download(StandardIngredientRecordsServiceImpl.java:694)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:716)
	at com.nercar.ingredient.service.impl.StandardIngredientRecordsServiceImpl$$SpringCGLIB$$0.download(<generated>)
	at com.nercar.ingredient.controller.IngredientCalculationController.download(IngredientCalculationController.java:292)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:833)
01:09:00.018 [http-nio-9021-exec-1] ERROR c.n.i.e.GlobalExceptionHandler - [handleErrors,23] - 异常信息:- 导出成本测算流程表失败: Index 2 out of bounds for length 2
01:15:20.656 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
01:15:20.661 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
01:15:31.969 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 41260 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
01:15:31.972 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
01:15:31.973 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
01:15:33.546 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
01:15:34.012 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
01:15:34.014 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
01:15:34.014 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
01:15:34.069 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
01:15:34.244 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
01:15:34.300 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@738a815c, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
01:15:34.401 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
01:15:34.427 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
01:15:34.444 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
01:15:34.460 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
01:15:34.477 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
01:15:34.493 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
01:15:34.508 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
01:15:34.536 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
01:15:34.541 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
01:15:34.541 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
01:15:34.553 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
01:15:34.589 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
01:15:34.605 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
01:15:34.624 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
01:15:34.640 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
01:15:34.658 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
01:15:34.675 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
01:15:34.694 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
01:15:34.714 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
01:15:34.730 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
01:15:34.749 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
01:15:34.752 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
01:15:34.753 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
01:15:34.767 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
01:15:34.772 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
01:15:34.772 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
01:15:34.782 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
01:15:34.801 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
01:15:34.818 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
01:15:34.968 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
01:15:34.977 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
01:15:34.983 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:30
01:15:36.395 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
01:15:36.415 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 5.214 seconds (process running for 7.016)
01:15:36.423 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
01:15:38.742 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
01:15:38.910 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
01:15:39.588 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@777b5ee
01:15:39.591 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
01:15:39.601 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
01:15:39.648 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
01:15:39.786 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
01:15:39.794 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
01:15:39.794 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
01:15:39.869 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
01:15:39.872 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
01:15:39.872 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
01:15:39.966 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
01:15:39.968 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
01:15:39.968 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
01:15:40.040 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
01:15:40.114 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:15:40.114 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
01:15:40.188 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:15:40.189 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:15:40.190 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
01:15:40.265 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:15:40.266 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:15:40.266 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
01:15:40.339 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:15:40.340 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:15:40.340 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
01:15:40.414 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:15:40.415 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:15:40.415 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
01:15:40.488 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:15:40.489 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:15:40.490 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
01:15:40.562 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:15:40.562 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:15:40.562 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
01:15:40.627 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:15:40.628 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:15:40.628 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
01:15:40.691 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:15:40.692 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:15:40.693 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
01:15:40.766 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:15:40.767 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:15:40.767 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
01:15:40.831 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:15:40.832 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:15:40.832 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
01:15:40.896 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:15:40.897 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:15:40.898 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
01:15:40.986 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:15:40.991 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,device,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
01:15:40.992 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
01:15:41.072 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
01:15:41.073 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
01:15:41.073 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
01:15:41.142 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
01:15:41.142 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
01:15:41.142 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
01:15:41.208 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
01:15:41.210 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
01:15:41.211 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
01:15:41.278 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
01:15:41.278 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
01:15:41.278 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
01:15:41.346 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
01:15:41.347 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
01:15:41.347 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
01:15:41.414 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
01:18:48.984 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
01:18:48.987 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
01:18:51.723 [main] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 39712 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
01:18:51.725 [main] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
01:18:51.726 [main] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
01:18:52.665 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
01:18:53.159 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9021"]
01:18:53.161 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
01:18:53.161 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
01:18:53.217 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
01:18:53.436 [main] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
01:18:53.486 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@4438b862, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
01:18:53.601 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
01:18:53.631 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
01:18:53.646 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
01:18:53.664 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
01:18:53.682 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
01:18:53.708 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
01:18:53.725 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
01:18:53.744 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\MaterialYieldRatesMapper.xml]'
01:18:53.748 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
01:18:53.748 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
01:18:53.754 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
01:18:53.786 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
01:18:53.799 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
01:18:53.812 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
01:18:53.824 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
01:18:53.837 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
01:18:53.848 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
01:18:53.866 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
01:18:53.882 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
01:18:53.895 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
01:18:53.910 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
01:18:53.914 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
01:18:53.914 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
01:18:53.920 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
01:18:53.924 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
01:18:53.925 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
01:18:53.931 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
01:18:53.949 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
01:18:53.965 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
01:18:54.096 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
01:18:54.105 [main] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
01:18:54.111 [main] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:1
01:18:55.517 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9021"]
01:18:55.538 [main] INFO  c.n.i.Application - [logStarted,56] - Started Application in 4.244 seconds (process running for 5.499)
01:18:55.545 [main] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9021/doc.html
	局域网: 	http://*************:9021/
	Knife4j文档: 	http://*************:9021/doc.html
swagger-ui: 	http://*************:9021/swagger-ui.html
----------------------------------------------------------
01:18:57.653 [http-nio-9021-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
01:18:57.811 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,109] - HikariPool-1 - Starting...
01:18:58.401 [http-nio-9021-exec-1] INFO  c.z.h.p.HikariPool - [checkFailFast,554] - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@75f2a5b
01:18:58.404 [http-nio-9021-exec-1] INFO  c.z.h.HikariDataSource - [getConnection,122] - HikariPool-1 - Start completed.
01:18:58.416 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==>  Preparing: SELECT id,status,user_name,department_id,steel_grade_id,calculation_process_no,execution_standard_id,raw_material_total,cost_price,mixing_date,release_date,category,calculation_result_id,process_path_id,special_notes,cost_estimattion_id,createuser,createtime,updatetime FROM standard_ingredient_records WHERE (id = ?)
01:18:58.470 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - ==> Parameters: 1(Long)
01:18:58.598 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectList - [debug,135] - <==      Total: 1
01:18:58.607 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,task_id,estimation_no,user_id,username,company_name,steel_number,finished_product_specification,estimated_order_quantity,quotation_or_cost_estimation,forged_material,change_standard_cost,length_delivery_status,surface_delivery_status,heat_delivery_status,technical_standard,process_route,approve_status,sub_status,department_id,processinstanceid,created_by,update_by,update_time,created_time,num FROM cost_estimation limit 1
01:18:58.608 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 
01:18:58.675 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 1
01:18:58.677 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE (standard_ingredient_record_id = ?)
01:18:58.677 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - ==> Parameters: 1(Long)
01:18:58.745 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectList - [debug,135] - <==      Total: 17
01:18:58.747 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,raw_material_id,raw_material_name,purpose_composition_id,composition,recovery_rate,wieght,price,single_consume,createuser,createtime,updatetime FROM calculation_result WHERE (standard_ingredient_record_id = ?)
01:18:58.747 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - ==> Parameters: 1(Long)
01:18:58.814 [http-nio-9021-exec-1] DEBUG c.n.i.m.C.selectList - [debug,135] - <==      Total: 6
01:18:58.870 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:18:58.871 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 1(Long)
01:18:58.939 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:18:58.940 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:18:58.940 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 1(Long)
01:18:59.003 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:18:59.003 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:18:59.003 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 2(Long)
01:18:59.073 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:18:59.074 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:18:59.075 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 2(Long)
01:18:59.141 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:18:59.142 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:18:59.142 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 3(Long)
01:18:59.207 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:18:59.207 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:18:59.208 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 3(Long)
01:18:59.273 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:18:59.274 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:18:59.274 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 4(Long)
01:18:59.338 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:18:59.339 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:18:59.339 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 4(Long)
01:18:59.409 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:18:59.409 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:18:59.410 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 5(Long)
01:18:59.472 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:18:59.473 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:18:59.473 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 5(Long)
01:18:59.560 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:18:59.561 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==>  Preparing: SELECT id,name,element,secondary_element,carbon_element,department_id,production_equipment_id,composition,yield_rate,price,is_custom,category,single_consume,department_name,createuser,createtime,updatetime FROM standard_raw_materials WHERE id=?
01:18:59.561 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - ==> Parameters: 6(Long)
01:18:59.629 [http-nio-9021-exec-1] DEBUG c.n.i.m.S.selectById - [debug,135] - <==      Total: 1
01:18:59.631 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,element_name,min_value,max_value,average_value,code,createuser,createtime,updatetime FROM purpose_compositions WHERE id=?
01:18:59.632 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - ==> Parameters: 6(Long)
01:18:59.704 [http-nio-9021-exec-1] DEBUG c.n.i.m.P.selectById - [debug,135] - <==      Total: 1
01:18:59.707 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,igingot_id,process_path,department_name,ingot_yield,device,sort FROM ingredient_idingot_result WHERE (standard_ingredient_record_id = ?)
01:18:59.708 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
01:18:59.776 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
01:18:59.776 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
01:18:59.777 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 2(Long)
01:18:59.845 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
01:18:59.846 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==>  Preparing: SELECT id,process_path,device,updateuser,department_name,ingot_yield,createuser,createtime,updatetime FROM ingot_yield_rates WHERE id=?
01:18:59.847 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - ==> Parameters: 1(Long)
01:18:59.916 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectById - [debug,135] - <==      Total: 1
01:18:59.918 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==>  Preparing: SELECT id,standard_ingredient_record_id,yield_id,production_dept,line_name,material_yield,sort FROM ingredient_yield_result WHERE (standard_ingredient_record_id = ?)
01:18:59.918 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - ==> Parameters: 1(Long)
01:18:59.981 [http-nio-9021-exec-1] DEBUG c.n.i.m.I.selectList - [debug,135] - <==      Total: 2
01:18:59.982 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
01:18:59.983 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 1(Long)
01:19:00.051 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
01:19:00.052 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==>  Preparing: SELECT id,device,production_dept,line_name,material_category_name,product_category,steel_class,steel_grade,specifications,material_yield,unfixed_length_black_skin,unfixed_yield_polished,unfixed_yield_peeling,fixed_yield_polished,fixed_yield_lathe,fixed_yield_peeling,unfixed_polished_lathe,unfixed_polished_peeling,fixed_polished_lathe,fixed_polished_peeling,fixed_length_rate,polished_rate,lathe_rate,peeling_rate,burning_loss FROM material_yield_rates WHERE id=?
01:19:00.053 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - ==> Parameters: 2(Long)
01:19:00.120 [http-nio-9021-exec-1] DEBUG c.n.i.m.M.selectById - [debug,135] - <==      Total: 1
01:19:31.723 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,349] - HikariPool-1 - Shutdown initiated...
01:19:31.727 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,351] - HikariPool-1 - Shutdown completed.
