package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 设备表
 * @TableName production_equipments
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductionEquipmentsVO extends BaseVO {
    /**
     * 主键
     */
    @TableId
    @Schema(description = "主键")
    private Long id;

    /**
     * 设备名称
     */
    @Schema(description = "设备名称")
    private String equipmentName;

    /**
     * 设备类型
     */
    @Schema(description = "设备类型")
    private String type;

    /**
     * 设备类别：冶炼、加工
     */
    @Schema(description = "设备类别：冶炼、加工")
    private String equipmentType;

    /**
     * 图片链接
     */
    @Schema(description = "图片链接")
    private String imagesUrl;

    /**
     * 关联的厂ID
     */
    @Schema(description = "关联的厂ID")
    private Long departmentId;

    /**
     * 
     */
    private String createuser;

    /**
     * 
     */
    private Date createtime;

    /**
     * 
     */
    private Date updatetime;




}