package com.nercar.ingredient.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PathSaveDTO extends BaseDTO{
    /**
     * 步骤编号
     */
    @Schema(description = "步骤编号")
    private  String stepNumber;
    /**
     * 设备id
     */
    @Schema(description = "设备id")
    private Long equipmentId;
    /**
     * 设备名称
     */
    @Schema(description = "设备名称")
    private String equipmentName;
    /**
     * 设备类别
     */
    @Schema(description = "设备类别")
    private Integer type;
    /**
     * 设备类型：冶炼、加工
     */
    @Schema(description = "设备类型：冶炼、加工")
    private String equipmentType;
    /**
     * 设备图片
     */
    @Schema(description = "设备图片")
    private String imagesUrl;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String departmentName;
}