package com.nercar.ingredient.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 计算批次结果VO
 * 用于表示批次内的单个计算结果（不包含calculationSequence字段）
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "计算批次结果VO")
public class CalculationBatchResultVO extends BaseVO {

    /**
     * 配料记录ID
     */
    @Schema(description = "配料记录ID")
    private Long standardIngredientRecordId;

    /**
     * 原料ID
     */
    @Schema(description = "原料ID")
    private Long rawMaterialId;

    /**
     * 原料名称
     */
    @Schema(description = "原料名称")
    private String rawMaterialName;

    /**
     * 目标成分ID
     */
    @Schema(description = "目标成分ID")
    private Long purposeCompositionId;

    /**
     * 成分
     */
    @Schema(description = "成分")
    private String composition;

    /**
     * 回收率
     */
    @Schema(description = "回收率")
    private String recoveryRate;

    /**
     * 重量
     */
    @Schema(description = "重量")
    private String wieght;

    /**
     * 价格
     */
    @Schema(description = "价格")
    private BigDecimal price;

    /**
     * 单耗
     */
    @Schema(description = "单耗")
    private String singleConsume;

    /**
     * 最小值
     */
    @Schema(description = "最小值")
    private BigDecimal minValue;

    /**
     * 最大值
     */
    @Schema(description = "最大值")
    private BigDecimal maxValue;

    /**
     * 成本
     */
    @Schema(description = "成本")
    private BigDecimal cost;
}
