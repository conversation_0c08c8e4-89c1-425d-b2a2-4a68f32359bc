-- 金属料吨水单耗配置集成测试脚本
-- 执行时间：2025-01-14

-- 1. 查看主表结构，确认新增字段
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'standard_ingredient_records'
  AND column_name IN ('metal_material_water_consumption_config_id', 'metal_material_water_consumption')
ORDER BY ordinal_position;

-- 2. 查看金属料吨水单耗配置表数据
SELECT 
    id,
    metal_material_water_consumption,
    method,
    device,
    createuser,
    createtime,
    updatetime
FROM metal_material_water_consumption_config
ORDER BY createtime DESC;

-- 3. 查看标准配料记录表中的金属料吨水单耗字段数据
SELECT 
    id,
    steel_grade_id,
    execution_standard_id,
    process_path_id,
    metal_material_water_consumption_config_id,
    metal_material_water_consumption,
    status,
    createtime
FROM standard_ingredient_records
ORDER BY createtime DESC
LIMIT 10;

-- 4. 查看金属料吨水单耗配置与标准配料记录的关联情况
SELECT 
    sir.id AS record_id,
    sg.steel_grade AS steel_grade,
    es.standard_name AS standard_name,
    sir.metal_material_water_consumption_config_id AS config_id,
    sir.metal_material_water_consumption AS water_consumption,
    mmwcc.method AS smelting_method,
    mmwcc.device AS smelting_device
FROM standard_ingredient_records sir
LEFT JOIN steel_grades sg ON sir.steel_grade_id = sg.id
LEFT JOIN execution_standard es ON sir.execution_standard_id = es.id
LEFT JOIN metal_material_water_consumption_config mmwcc ON sir.metal_material_water_consumption_config_id = mmwcc.id
WHERE sir.metal_material_water_consumption_config_id IS NOT NULL
ORDER BY sir.createtime DESC
LIMIT 10;

-- 5. 统计金属料吨水单耗配置使用情况
SELECT 
    mmwcc.id AS config_id,
    mmwcc.method AS smelting_method,
    mmwcc.device AS smelting_device,
    mmwcc.metal_material_water_consumption AS water_consumption,
    COUNT(sir.id) AS usage_count
FROM metal_material_water_consumption_config mmwcc
LEFT JOIN standard_ingredient_records sir ON mmwcc.id = sir.metal_material_water_consumption_config_id
GROUP BY mmwcc.id, mmwcc.method, mmwcc.device, mmwcc.metal_material_water_consumption
ORDER BY usage_count DESC;

-- 6. 按冶炼方法统计平均金属料吨水单耗
SELECT 
    mmwcc.method AS smelting_method,
    COUNT(sir.id) AS record_count,
    AVG(sir.metal_material_water_consumption) AS avg_water_consumption,
    MIN(sir.metal_material_water_consumption) AS min_water_consumption,
    MAX(sir.metal_material_water_consumption) AS max_water_consumption
FROM standard_ingredient_records sir
JOIN metal_material_water_consumption_config mmwcc ON sir.metal_material_water_consumption_config_id = mmwcc.id
WHERE sir.metal_material_water_consumption IS NOT NULL
GROUP BY mmwcc.method
ORDER BY record_count DESC;

-- 7. 按钢种统计金属料吨水单耗使用情况
SELECT 
    sg.steel_grade AS steel_grade,
    COUNT(sir.id) AS record_count,
    AVG(sir.metal_material_water_consumption) AS avg_water_consumption
FROM standard_ingredient_records sir
JOIN steel_grades sg ON sir.steel_grade_id = sg.id
WHERE sir.metal_material_water_consumption IS NOT NULL
GROUP BY sg.steel_grade
ORDER BY record_count DESC
LIMIT 10;

-- 8. 检查金属料吨水单耗为NULL的记录
SELECT 
    COUNT(*) AS total_records,
    SUM(CASE WHEN metal_material_water_consumption_config_id IS NULL THEN 1 ELSE 0 END) AS null_config_id_count,
    SUM(CASE WHEN metal_material_water_consumption IS NULL THEN 1 ELSE 0 END) AS null_water_consumption_count
FROM standard_ingredient_records;

-- 9. 检查金属料吨水单耗配置ID与值的一致性
SELECT 
    sir.id AS record_id,
    sir.metal_material_water_consumption_config_id AS config_id,
    sir.metal_material_water_consumption AS record_water_consumption,
    mmwcc.metal_material_water_consumption AS config_water_consumption,
    CASE 
        WHEN sir.metal_material_water_consumption = mmwcc.metal_material_water_consumption THEN '一致'
        ELSE '不一致'
    END AS consistency_check
FROM standard_ingredient_records sir
JOIN metal_material_water_consumption_config mmwcc ON sir.metal_material_water_consumption_config_id = mmwcc.id
WHERE sir.metal_material_water_consumption_config_id IS NOT NULL
  AND sir.metal_material_water_consumption IS NOT NULL
ORDER BY sir.createtime DESC
LIMIT 10;

-- 10. 验证计算接口是否正确保存金属料吨水单耗信息
-- 注意：这部分需要通过API调用验证，以下SQL仅用于查看最新的计算结果
SELECT 
    sir.id AS record_id,
    sir.steel_grade_id,
    sg.steel_grade,
    sir.execution_standard_id,
    es.standard_name,
    sir.process_path_id,
    sir.metal_material_water_consumption_config_id,
    sir.metal_material_water_consumption,
    sir.createtime
FROM standard_ingredient_records sir
JOIN steel_grades sg ON sir.steel_grade_id = sg.id
JOIN execution_standard es ON sir.execution_standard_id = es.id
ORDER BY sir.createtime DESC
LIMIT 5;

-- 测试脚本执行完成
SELECT '金属料吨水单耗配置集成测试脚本执行完成' as completion_status;
