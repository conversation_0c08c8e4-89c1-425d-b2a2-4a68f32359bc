package com.nercar.ingredient.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.ingredient.domain.dto.StandardTemplatePageDTO;
import com.nercar.ingredient.domain.dto.StandardTemplateQueryDTO;
import com.nercar.ingredient.domain.po.StandardRawMaterials;
import com.nercar.ingredient.domain.po.StandardTemplate;
import com.nercar.ingredient.domain.vo.StandardTemplateVO;
import org.w3c.dom.stylesheets.LinkStyle;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【standard_template(标准配料模版表)】的数据库操作Service
* @createDate 2025-04-01 13:55:28
*/
public interface StandardTemplateService extends IService<StandardTemplate> {


    IPage<StandardRawMaterials> getStandardRawMaterialsInfo(StandardTemplatePageDTO standardTemplatePageDTO);

    IPage<StandardRawMaterials> getStandardRawMaterialsSaveInfo(StandardTemplatePageDTO standardTemplatePageDTO);

    /**
     * 分页查询标准配料模板
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<StandardTemplateVO> getStandardTemplateWithPaging(StandardTemplateQueryDTO queryDTO);
}
