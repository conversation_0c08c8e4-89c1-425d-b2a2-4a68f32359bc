package com.nercar.ingredient.service;

import com.nercar.ingredient.domain.dto.CalculationQueryDataDTO;
import com.nercar.ingredient.domain.dto.NewAlgorithmRequest;
import com.nercar.ingredient.domain.po.CalculationResult;
import com.nercar.ingredient.domain.vo.NewAlgorithmResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 新算法接口集成测试
 */
@SpringBootTest
@Slf4j
public class NewAlgorithmIntegrationTest {
    
    @Autowired
    private AlgorithmDataTransformService algorithmDataTransformService;
    
    @Test
    public void testNewAlgorithmDataTransform() {
        log.info("开始测试新算法数据转换");
        
        // 创建测试数据
        CalculationQueryDataDTO dto = createTestDTO();
        
        // 测试数据转换
        NewAlgorithmRequest request = algorithmDataTransformService.transformToNewAlgorithmRequest(dto);
        
        // 验证结果
        assertNotNull(request);
        assertEquals("18CrNiMo7-6+HH", request.getSteelGrade());
        assertEquals(1229.0, request.getTargetWeight());
        assertNotNull(request.getMaterials());
        assertNotNull(request.getTargetComp());
        
        log.info("新算法数据转换测试通过");
    }
    
    @Test
    public void testNewAlgorithmResponseProcessing() {
        log.info("开始测试新算法响应处理");
        
        // 创建模拟响应数据
        NewAlgorithmResponse response = createMockResponse();
        
        // 创建模拟计算结果
        List<CalculationResult> calculationResults = createMockCalculationResults();
        
        // 测试响应处理
        algorithmDataTransformService.processNewAlgorithmResponse(response, calculationResults);
        
        // 验证结果
        for (CalculationResult result : calculationResults) {
            assertNotNull(result.getSingleConsume());
            assertNotNull(result.getUnit_consumption_tons());
            assertTrue(result.getUnit_consumption_tons() >= 0);
        }
        
        log.info("新算法响应处理测试通过");
    }
    
    /**
     * 创建测试DTO
     */
    private CalculationQueryDataDTO createTestDTO() {
        CalculationQueryDataDTO dto = new CalculationQueryDataDTO();
        dto.setSteelGrade("18CrNiMo7-6+HH");
        dto.setStandardName("GB/T 3077-2015");
        dto.setProcessPathId(123L);
        dto.setMetalMaterialWaterConsumption(1229);
        
        // 设置原料ID列表
        List<String> materialIds = new ArrayList<>();
        materialIds.add("1");
        materialIds.add("2");
        dto.setStandardRawMaterialsIds(materialIds);
        
        // 设置目标成分ID列表
        List<String> purposeIds = new ArrayList<>();
        purposeIds.add("1");
        purposeIds.add("2");
        dto.setPurposeCompositionsIds(purposeIds);
        
        return dto;
    }
    
    /**
     * 创建模拟响应
     */
    private NewAlgorithmResponse createMockResponse() {
        // 这里创建一个模拟的响应对象用于测试
        // 实际测试时需要根据真实的响应格式调整
        return new NewAlgorithmResponse();
    }
    
    /**
     * 创建模拟计算结果
     */
    private List<CalculationResult> createMockCalculationResults() {
        List<CalculationResult> results = new ArrayList<>();
        
        CalculationResult result1 = new CalculationResult();
        result1.setRawMaterialName("高铬");
        results.add(result1);
        
        CalculationResult result2 = new CalculationResult();
        result2.setRawMaterialName("低碳铬铁");
        results.add(result2);
        
        return results;
    }
}
