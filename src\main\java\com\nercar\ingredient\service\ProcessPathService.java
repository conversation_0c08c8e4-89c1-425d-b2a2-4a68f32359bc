package com.nercar.ingredient.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.ingredient.domain.dto.ProcessPathDTO;
import com.nercar.ingredient.domain.po.ProcessPath;
import com.nercar.ingredient.domain.vo.DepartmentsVO;
import com.nercar.ingredient.domain.vo.ProcessPathVO;
import com.nercar.ingredient.response.PageDataResult;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【process_path(工艺路径表)】的数据库操作Service
* @createDate 2025-04-01 13:55:28
*/
public interface ProcessPathService extends IService<ProcessPath> {

    Long saveProcessPath(ProcessPathDTO processPathDTO);

    List<DepartmentsVO> getTree();

    List<ProcessPathVO> selectRecommend();

}
