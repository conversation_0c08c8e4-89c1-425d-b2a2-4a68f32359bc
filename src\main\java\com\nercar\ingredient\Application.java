
package com.nercar.ingredient;

import cn.dev33.satoken.SaManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Collections;

@SpringBootApplication
@Slf4j
public class Application {

    public static void main(String[] args) {
        ConfigurableApplicationContext applicationContext = SpringApplication.run(Application.class, args);
        Environment env = applicationContext.getEnvironment();
        String ip = getLocalIP(); // 使用改进的IP获取方法
        String port = env.getProperty("server.port");
        System.out.println("启动成功，Sa-Token 配置如下：" + SaManager.getConfig());
        String path = env.getProperty("server.servlet.context-path") == null ? "" : env.getProperty("server.servlet.context-path");
        log.info("\n----------------------------------------------------------\n\t" +
                "Application is running! Access URLs:\n\t" +
                "本地: \thttp://localhost:" + port + path + "/doc.html\n\t" +
                "局域网: \thttp://" + ip + ":" + port + path + "/\n\t" +
                "Knife4j文档: \thttp://" + ip + ":" + port + path + "/doc.html\n" +
                "swagger-ui: \thttp://" + ip + ":" + port + path + "/swagger-ui.html\n" +
                "----------------------------------------------------------");
    }

    private static String getLocalIP() {
        try {
            for (NetworkInterface ni : Collections.list(NetworkInterface.getNetworkInterfaces())) {
                if (ni.isLoopback() || !ni.isUp() || ni.getDisplayName().contains("Virtual"))
                    continue;

                for (InetAddress address : Collections.list(ni.getInetAddresses())) {
                    if (address instanceof Inet4Address) {
                        return address.getHostAddress();
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取IP失败", e);
        }
        return "127.0.0.1";
    }
}


//package com.nercar.ingredient;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.boot.SpringApplication;
//import org.springframework.boot.autoconfigure.SpringBootApplication;
//import org.springframework.context.ConfigurableApplicationContext;
//import org.springframework.core.env.Environment;
//
//import java.net.InetAddress;
//import java.net.UnknownHostException;
//
//@SpringBootApplication
//@Slf4j
//public class Application {
//    public static void main(String[] args) {
//        ConfigurableApplicationContext applicationContext = SpringApplication.run(Application.class, args);
//        Environment env = applicationContext.getEnvironment();
//        String ip = null;
//        try {
//            ip = InetAddress.getLocalHost().getHostAddress();
//        } catch (UnknownHostException e) {
//            throw new RuntimeException(e);
//        }
//        String port = env.getProperty("server.port");
//        String path = env.getProperty("server.servlet.context-path") == null ? "" : env.getProperty("server.servlet.context-path");
//        log.info("\n----------------------------------------------------------\n\t" +
//                "Application is running! Access URLs:\n\t" +
//                "本地: \thttp://localhost:" + port + path + "/doc.html\n\t" +
//                "局域网: \thttp://" + ip + ":" + port + path + "/\n\t" +
//                "Knife4j文档: \thttp://" + ip + ":" + port + path + "/doc.html\n" +
//                "swagger-ui: \thttp://" + ip + ":" + port + path + "/swagger-ui.html\n" +
//                "----------------------------------------------------------");
//    }
//}
