package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 配料单成锭率
 * @TableName ingredient_idingot_result
 */
@TableName(value ="ingredient_idingot_result")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class IngredientIdingotResult extends BaseEntity {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 配料单ID
     */
    private Long standardIngredientRecordId;

    /**
     * 成锭率表ID
     */
    private Long igingotId;

    /**
     * 工序
     */
    private String processPath;

    /**
     * 部门名称
     */
    private String departmentName;

    @TableField(exist = false)
    private String productionDept;

    /**
     * 成锭率
     */
    private BigDecimal ingotYield;


    private String device;

    private Integer sort;




}