package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 成锭率
 * @TableName ingot_yield_rates
 */
@TableName(value ="ingot_yield_rates")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class IngotYieldRates extends BaseEntity {
    /**
     * 
     */
    @TableId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 工序
     */
    private String processPath;


    private String device;


    @TableField(value = "updateuser", fill = FieldFill.INSERT_UPDATE)
    private String updateuser;
    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 成锭率
     */
    private BigDecimal ingotYield;

    /**
     *
     */
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    private String createuser;


    /**
     *
     */
    @TableField(value = "createtime",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     *
     */
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;

    /**
     * 是否自定义(0=系统默认,1=用户自定义)
     */
    private Integer isCustom;

}