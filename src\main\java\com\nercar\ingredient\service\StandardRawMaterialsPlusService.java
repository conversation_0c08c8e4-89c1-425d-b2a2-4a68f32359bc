package com.nercar.ingredient.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nercar.ingredient.domain.dto.StandardRawMaterialsPlusDTO;
import com.nercar.ingredient.domain.dto.StandardRawMaterialsQueryDTO;
import com.nercar.ingredient.domain.po.StandardRawMaterialsPlus;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【standard_raw_materials_plus(标准原料扩展表)】的数据库操作Service
* @createDate 2025-04-22 10:52:00
*/
public interface StandardRawMaterialsPlusService extends IService<StandardRawMaterialsPlus> {

    IPage<StandardRawMaterialsPlus> selectStandardRawMaterials(StandardRawMaterialsQueryDTO standardRawMaterialsQueryDTO);

    boolean insertOrUpdate(StandardRawMaterialsPlusDTO standardRawMaterialsPlusDTO);

    StandardRawMaterialsPlusDTO selectById(String id);

    Boolean deleteById(String l);
}
