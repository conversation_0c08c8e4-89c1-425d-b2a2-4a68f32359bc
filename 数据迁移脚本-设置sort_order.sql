-- 数据迁移脚本：为现有 purpose_compositions 数据设置 sort_order 值
-- 执行时间：2025-07-29
-- 目的：为现有数据按照当前排序逻辑设置排序序号，保持现有显示效果

-- 1. 查看当前数据情况
SELECT 
    '迁移前数据统计' as info,
    COUNT(*) as total_records,
    COUNT(CASE WHEN sort_order IS NULL THEN 1 END) as null_sort_order_count,
    COUNT(CASE WHEN sort_order IS NOT NULL THEN 1 END) as has_sort_order_count
FROM purpose_compositions;

-- 2. 查看需要迁移的数据示例
SELECT 
    id,
    standard_ingredient_record_id,
    element_name,
    type,
    sort_order,
    createtime
FROM purpose_compositions 
WHERE standard_ingredient_record_id IS NOT NULL
ORDER BY standard_ingredient_record_id, type ASC, element_name ASC
LIMIT 20;

-- 3. 执行数据迁移
-- 为现有数据按照当前排序逻辑（type ASC, element_name ASC）设置排序序号
WITH ranked_data AS (
    SELECT 
        id,
        standard_ingredient_record_id,
        element_name,
        type,
        ROW_NUMBER() OVER (
            PARTITION BY standard_ingredient_record_id 
            ORDER BY type ASC, element_name ASC
        ) - 1 AS new_sort_order
    FROM purpose_compositions 
    WHERE standard_ingredient_record_id IS NOT NULL
      AND sort_order IS NULL
)
UPDATE purpose_compositions 
SET sort_order = ranked_data.new_sort_order
FROM ranked_data 
WHERE purpose_compositions.id = ranked_data.id;

-- 4. 验证迁移结果
SELECT 
    '迁移后数据统计' as info,
    COUNT(*) as total_records,
    COUNT(CASE WHEN sort_order IS NULL THEN 1 END) as null_sort_order_count,
    COUNT(CASE WHEN sort_order IS NOT NULL THEN 1 END) as has_sort_order_count
FROM purpose_compositions;

-- 5. 查看迁移后的数据示例
SELECT 
    id,
    standard_ingredient_record_id,
    element_name,
    type,
    sort_order,
    createtime
FROM purpose_compositions 
WHERE standard_ingredient_record_id IS NOT NULL
ORDER BY standard_ingredient_record_id, sort_order ASC
LIMIT 20;

-- 6. 验证特定配料记录的排序
-- 以配料记录 1950093414410448897 为例
SELECT 
    id,
    element_name,
    type,
    sort_order,
    createtime
FROM purpose_compositions 
WHERE standard_ingredient_record_id = 1950093414410448897
ORDER BY sort_order ASC;

-- 迁移完成提示
SELECT '数据迁移脚本执行完成，请检查验证结果' as status;
