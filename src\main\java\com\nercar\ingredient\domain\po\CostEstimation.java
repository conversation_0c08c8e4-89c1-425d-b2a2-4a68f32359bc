package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.nercar.ingredient.domain.po.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 成本测算
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Data
@TableName("cost_estimation")
@Schema(description = "成本测算")
public class CostEstimation extends BaseEntity {


    @Schema(description = "token")
    @TableField(exist = false)
    private String token;

    @Schema(description = "用户名称")
    @TableField(exist = false)
    private String nickName;

    @Schema(description = "合同ID")
    @TableField("contract_id")
    private String contractId;

    @Schema(description = "测算那边的用户id")
    @TableField("user_ids")
    private Long userIds;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "id主键")
    private Long id;

    @Schema(description = "任务id")
    @TableField("task_id")
    private String taskId;

    @Schema(description = "评审编号")
    @TableField("estimation_no")
    private String estimationNo;

    @Schema(description = "用户id")
    @TableField("user_id")
    private String userId;

    @Schema(description = "账号名称")
    @TableField("username")
    private String username;

    @Schema(description = "所属分公司")
    @TableField("company_name")
    private String companyName;

    @Schema(description = "钢号")
    @TableField("steel_number")
    private String steelNumber;

    @Schema(description = "成品规格")
    @TableField("finished_product_specification")
    private String finishedProductSpecification;

    @Schema(description = "预计订货量")
    @TableField("estimated_order_quantity")
    private String estimatedOrderQuantity;

    @Schema(description = "报价或测算成本")
    @TableField("quotation_or_cost_estimation")
    private String quotationOrCostEstimation;

    @Schema(description = "是否锻材")
    @TableField("forged_material")
    private String forgedMaterial;

    @Schema(description = "是否修改标准成本")
    @TableField("change_standard_cost")
    private String changeStandardCost;

    @Schema(description = "长度交货状态")
    @TableField("length_delivery_status")
    private String lengthDeliveryStatus;

    @Schema(description = "表面交货状态")
    @TableField("surface_delivery_status")
    private String surfaceDeliveryStatus;

    @Schema(description = "热处理交货状态")
    @TableField("heat_delivery_status")
    private String heatDeliveryStatus;

    @Schema(description = "技术标准及特殊说明")
    @TableField("technical_standard")
    private String technicalStandard;

    @Schema(description = "工艺路线")
    @TableField("process_route")
    private String processRoute;

    @Schema(description = "当前节点")
    @TableField("approve_status")
    private String approveStatus;

    @Schema(description = "状态0保存1提交已发送2历史")
    @TableField("sub_status")
    private Integer subStatus;

    @Schema(description = "部门id 冶炼科传1 配料室传3 两个都有传2")
    @TableField("department_id")
    private String departmentId;

    @Schema(description = "流程图编号")
    @TableField("processinstanceid")
    private String processinstanceId;

    @Schema(description = "创建人")
    @TableField("created_by")
    private String createdBy;

    @Schema(description = "更新人")
    @TableField("update_by")
    private String updateBy;

    @Schema(description = "更新时间")
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "创建时间")
    @TableField("created_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    @Schema(description = "数量")
    @TableField("num")
    private Integer num;

}
