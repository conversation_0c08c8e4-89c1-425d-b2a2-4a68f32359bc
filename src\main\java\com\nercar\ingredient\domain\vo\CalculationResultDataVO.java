package com.nercar.ingredient.domain.vo;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CalculationResultDataVO extends BaseVO{
    //返回的参数是一个主表id，还有一个计算结果表id集合
    /**
     * 主表id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long standardIngredientRecordId;

    /**
     * 计算结果表id集合
     */
    private List<String> calculationResultIdList;

    /**
     * 金属料吨水单耗配置ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long metalMaterialWaterConsumptionConfigId;

    /**
     * 金属料吨水单耗值
     */
    private Integer metalMaterialWaterConsumption;
}
