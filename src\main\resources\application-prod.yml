server:
  port: 7104

spring:
  application:
    name: fushun-ingredient
  datasource:
    url: ***************************************************
    username: postgres
    password: Nercar01
    driver-class-name: org.postgresql.Driver
  devtools:
    restart:
      enabled: false
  data:
    # redis配置 - 仅生产环境特定的配置
    redis:
      # Redis服务器地址
      host: 127.0.0.1
      # Redis服务器连接端口
      port: 6379
      # Redis服务器连接密码（默认为空）
      password:
      #Redis数据库索引（默认为0）
      database: 2
      # 连接超时时间
      timeout: 10s
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 200
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
          # 连接池中的最大空闲连接
          max-idle: 10
          # 连接池中的最小空闲连接
          min-idle: 0

  web:
    resources:
      static-locations: classpath:/static/,file:images/

logging:
  level:
    com.baomidou.mybatis plus: INFO
    org.apache.ibatis: INFO

smart-desktop:
  token-prefix: SmartDesktop-
  auth-url: http://*************:8081/user/getCurrUser

# 远程服务配置
remote-service:
  approve-batching:
    base-url: http://*************:7103
    endpoints:
      batching: /approve/batching
      batching-again: /approve/batching/onceagain
  user-auth:
    base-url: http://*************:7103
    endpoints:
      token: /user/token
  calculation:
    base-url: http://172.18.11.138:7104
    endpoints:
      calculate: /caculate

# 合同评审系统配置
contract-review:
  base-url: http://*************:7102
  login:
    username: "1000"
    password: "123456"
  token:
    header-name: Authorization
    refresh-interval: 30  # Token刷新间隔（分钟）
    cache-duration: 60    # Token缓存时长（分钟）
  endpoints:
    login: /employee/login
    customer-list: /sale/getCustomerListByName
    historical-reviews: /sale/getHistoricalReviews
    steel-grade-list: /sale/getSteelGradeListByName
    steel-type-list: /sale/getSteelTypeListByName
    contract-detail: /techCenterStandardSection/getReviewedOrderInfo
    file-list: /file/select
    file-preview: /file/preview
