<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.UsersMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.Users">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="account" column="account" jdbcType="VARCHAR"/>
            <result property="userName" column="user_name" jdbcType="VARCHAR"/>
            <result property="departmentId" column="department_id" jdbcType="BIGINT"/>
            <result property="createuser" column="createuser" jdbcType="VARCHAR"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,account,user_name,pass_word,
        department_id,createuser,createtime,
        updatetime
    </sql>
</mapper>
