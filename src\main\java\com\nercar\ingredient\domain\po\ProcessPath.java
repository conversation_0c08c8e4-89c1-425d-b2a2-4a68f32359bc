package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 工艺路径表
 * @TableName process_path
 */
@TableName(value ="process_path")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProcessPath extends BaseEntity {
    /**
     * 主键
     */
    @TableId
    private Long id;

    private Long steelGradesId;

    /**
     * 路径名称
     */
    private String pathName;

    /**
     * 用途描述
     */
    private String purpose;

    /**
     * 备注
     */
    private String note;

    /**
     * 使用频次
     */
    private Integer frequence;

    /**
     * 成材率id
     */
    private Long materialYield;

    /**
     * 
     */
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    private String createuser;

    /**
     * 
     */
    @TableField(value = "createtime",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     * 
     */
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;




}