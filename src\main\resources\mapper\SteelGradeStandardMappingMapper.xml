<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.SteelGradeStandardMappingMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.SteelGradeStandardMapping">
            <result property="steelGradeId" column="steel_grade_id" jdbcType="BIGINT"/>
            <result property="standardId" column="standard_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        steel_grade_id,standard_id
    </sql>
</mapper>
