<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.IngredientYieldResultMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.IngredientYieldResult">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="standardIngredientRecordId" column="standard_ingredient_record_id" jdbcType="BIGINT"/>
            <result property="yieldId" column="yield_id" jdbcType="BIGINT"/>
            <result property="productionDept" column="production_dept" jdbcType="VARCHAR"/>
            <result property="lineName" column="line_name" jdbcType="VARCHAR"/>
            <result property="materialYield" column="material_yield" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,standard_ingredient_record_id,yield_id,
        production_dept,line_name,material_yield
    </sql>
</mapper>
