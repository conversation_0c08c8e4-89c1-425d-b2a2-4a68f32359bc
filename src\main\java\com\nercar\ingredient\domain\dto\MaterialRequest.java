package com.nercar.ingredient.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 原料请求类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MaterialRequest {
    /**
     * 原料名称
     */
    private String name;
    
    /**
     * 成分信息
     */
    private List<CompositionRequest> compositions;
    
    /**
     * 价格
     */
    private Double price;
    
    /**
     * 收得率
     */
    private Double yieldRate;
    
    /**
     * 固定重量（基准品味）
     */
    private Double fixedWeight;
}
