package com.nercar.ingredient.domain.vo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SubmitDataVO extends BaseVO{

    /**
     * 工艺路径
     */
    private ProcessPathVO processPath;

    /**
     * 目标成分
     */
    private List<PurposeCompositionsVO> purposeCompositions;
    /**
     * 计算结果
     */
    private List<CalculationResultVO> calculationResult;
}
