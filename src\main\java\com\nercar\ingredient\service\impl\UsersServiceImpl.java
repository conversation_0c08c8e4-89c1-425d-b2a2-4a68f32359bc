package com.nercar.ingredient.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.bo.CurrentUser;
import com.nercar.ingredient.domain.dto.UsersDTO;
import com.nercar.ingredient.domain.po.Users;
import com.nercar.ingredient.domain.vo.UsersVO;
import com.nercar.ingredient.mapper.UsersMapper;
import com.nercar.ingredient.response.Result;
import com.nercar.ingredient.security.UserContext;
import com.nercar.ingredient.service.UsersService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

/**
* <AUTHOR>
* @description 针对表【users(用户表)】的数据库操作Service实现
* @createDate 2025-04-01 13:55:28
*/
@Service
public class UsersServiceImpl extends ServiceImpl<UsersMapper, Users>
    implements UsersService{

    @Autowired
    private UsersMapper usersMapper;

    @Override
    public Users login(UsersDTO usersDTO) {
        String userName = usersDTO.getUserName();
        String password = usersDTO.getPassword();
        // 1、根据用户名查询数据库中的数据
        QueryWrapper<Users> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_name", userName);
        Users users = getOne(queryWrapper);
        // 2、处理各种异常情况（用户名不存在、密码不对、账号被锁定）
        if (users == null) {
            // 账号不存在
            throw new RuntimeException("账号不存在");
        }
        // 密码比对
        // 对前端传过来的明文密码进行md5加密处理
        password=DigestUtils.md5DigestAsHex(password.getBytes());
        if(!password.equals(users.getPassWord())){
            //密码错误
            throw new RuntimeException("密码错误");
        }
        // 3、返回实体对象
        return users;
    }

    @Override
    public Result<UsersVO> getCurrentUserInfo() {
        CurrentUser currentUser = UserContext.getCurrentUser();
        String username = currentUser.getUsername();
        return Result.ok(getUserVoByUsername(username));
    }

    private UsersVO getUserVoByUsername(String username) {
        LambdaQueryWrapper<Users> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Users::getUserName, username);
        Users users = usersMapper.selectOne(queryWrapper);
            UsersVO usersVO = new UsersVO();
            BeanUtils.copyProperties(users, usersVO);
            return usersVO;
    }
}




