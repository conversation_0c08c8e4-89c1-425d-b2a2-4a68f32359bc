package com.nercar.ingredient.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 当访问 /images/** 的URL时，会去查找项目根目录下的 images 文件夹
        registry.addResourceHandler("/images/**")
                .addResourceLocations("file:images/");
    }
}