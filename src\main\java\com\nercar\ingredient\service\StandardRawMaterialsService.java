package com.nercar.ingredient.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.ingredient.domain.dto.MaterialsCategoryDTO;
import com.nercar.ingredient.domain.dto.PathMaterialsQueryDTO;
import com.nercar.ingredient.domain.dto.StandardRawMaterialsDTO;
import com.nercar.ingredient.domain.dto.StandardRawMaterialsQueryDTO;
import com.nercar.ingredient.domain.po.StandardRawMaterials;
import com.nercar.ingredient.domain.vo.RawMaterialsCategoryVO;
import com.nercar.ingredient.domain.vo.StandardRawMaterialsVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【standard_rwa_materials(标准原料)】的数据库操作Service
* @createDate 2025-04-01 13:55:28
*/
public interface StandardRawMaterialsService extends IService<StandardRawMaterials> {

    List<StandardRawMaterialsVO> getStandardRawMaterials(List<StandardRawMaterialsDTO> standardRawMaterialsQueryDTOList);

    IPage<StandardRawMaterialsVO> selectStandardRawMaterials(StandardRawMaterialsQueryDTO standardRawMaterialsQueryDTO);

    boolean insertOrUpdate(StandardRawMaterialsDTO standardRawMaterialsDTO);

    StandardRawMaterialsVO selectById(Long id);

    List<StandardRawMaterialsVO> getStandardRawMaterialsById(Long id);

    List<RawMaterialsCategoryVO> getCustomizePathRawMaterials(MaterialsCategoryDTO materialsCategoryDTO);

    void saveOrUpdateStandardRawMaterials(PathMaterialsQueryDTO pathMaterialsQueryDTO);

    List<StandardRawMaterialsVO> getStandardRawMaterialsByTemplateId(Long id);
}
