<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <!-- 日志存放路径 -->
	<property name="log.path" value="logs" />
   <!-- 日志输出格式 -->
    <property name="log.pattern"
              value="%d{HH:mm:ss.SSS} %highlight(%-5level) [%thread] %blue(%logger{20}) - [%method,%line] - %msg%n" />
    <!-- 控制台输出 -->
	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>${log.pattern}</pattern>
		</encoder>
	</appender>

    <!-- 新增:file_server appender -->
    <appender name="file_server" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/server.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/server.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>60</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- 系统模块日志级别控制  -->
    <logger name="com.nercar" level="info" />
    <!-- Spring日志级别控制  -->
	<logger name="org.springframework" level="warn" />
    <!-- MyBatis-Plus日志级别控制 -->
    <logger name="com.baomidou.mybatisplus" level="info" />
    <!-- 确保拦截器日志能被记录 -->
    <logger name="com.nercar.ingredient.interceptor" level="debug" />

	<!-- 系统操作日志 -->
	<root level="info">
		<appender-ref ref="console" />
        <appender-ref ref="file_server" />
    </root>
</configuration>

