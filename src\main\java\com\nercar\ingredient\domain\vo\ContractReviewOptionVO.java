package com.nercar.ingredient.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 合同评审下拉框选项VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "合同评审下拉框选项VO")
public class ContractReviewOptionVO {
    
    /**
     * 选项值
     */
    @Schema(description = "选项值，用于提交表单")
    private String value;
    
    /**
     * 选项标签
     */
    @Schema(description = "选项标签，用于显示")
    private String label;
    
    /**
     * 选项数量（可选）
     */
    @Schema(description = "该选项对应的记录数量")
    private Long count;
}
