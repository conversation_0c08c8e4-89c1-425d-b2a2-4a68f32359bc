/*
 Navicat Premium Data Transfer

 Source Server         : ************_5432
 Source Server Type    : PostgreSQL
 Source Server Version : 90224
 Source Host           : ************:5432
 Source Catalog        : ingredient2
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 90224
 File Encoding         : 65001

 Date: 25/04/2025 09:01:32
*/


-- ----------------------------
-- Sequence structure for cost_estimation_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."cost_estimation_id_seq";
CREATE SEQUENCE "public"."cost_estimation_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for ingredient_idingot_result_sort_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."ingredient_idingot_result_sort_seq";
CREATE SEQUENCE "public"."ingredient_idingot_result_sort_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for ingredient_yield_result_sort_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."ingredient_yield_result_sort_seq";
CREATE SEQUENCE "public"."ingredient_yield_result_sort_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for material_yield_rates_sort_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."material_yield_rates_sort_seq";
CREATE SEQUENCE "public"."material_yield_rates_sort_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for sort_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."sort_seq";
CREATE SEQUENCE "public"."sort_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Table structure for calculation_result
-- ----------------------------
DROP TABLE IF EXISTS "public"."calculation_result";
CREATE TABLE "public"."calculation_result" (
  "id" int8 NOT NULL,
  "standard_ingredient_record_id" int8,
  "raw_material_id" int8,
  "raw_material_name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "purpose_composition_id" int8,
  "composition" numeric,
  "recovery_rate" numeric,
  "wieght" numeric,
  "price" numeric(10,3),
  "createuser" varchar(32) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6),
  "unit_consumption_tons" numeric,
  "single_consume" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."calculation_result"."id" IS '主键';
COMMENT ON COLUMN "public"."calculation_result"."standard_ingredient_record_id" IS '主表ID';
COMMENT ON COLUMN "public"."calculation_result"."raw_material_id" IS '使用的原料ID';
COMMENT ON COLUMN "public"."calculation_result"."raw_material_name" IS '原料名称';
COMMENT ON COLUMN "public"."calculation_result"."purpose_composition_id" IS '目标成分ID';
COMMENT ON COLUMN "public"."calculation_result"."composition" IS '品位%';
COMMENT ON COLUMN "public"."calculation_result"."recovery_rate" IS '收得率%';
COMMENT ON COLUMN "public"."calculation_result"."wieght" IS '配入量%';
COMMENT ON COLUMN "public"."calculation_result"."price" IS '价格';
COMMENT ON COLUMN "public"."calculation_result"."unit_consumption_tons" IS '单耗';
COMMENT ON COLUMN "public"."calculation_result"."single_consume" IS '单耗';
COMMENT ON TABLE "public"."calculation_result" IS '计算结果表';

-- ----------------------------
-- Records of calculation_result
-- ----------------------------
INSERT INTO "public"."calculation_result" VALUES (1913054932731322370, 1913054930269265921, 1, '铁矿石 A1', 1912783964880379905, 60.00, 90.00, 2.50, NULL, NULL, '2025-04-18 10:20:13.470028', '2025-04-18 10:20:13.470028', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1912721409814282242, 1912721407549358081, 3, '废钢 C', 1912706412664983554, 95.00, 92.00, 1.00, NULL, NULL, '2025-04-17 12:14:55.404208', '2025-04-17 12:14:55.404208', 12.071, '12.071');
INSERT INTO "public"."calculation_result" VALUES (1912725233060827138, 1912725232947580930, 3, '废钢 C', 1912706412664983554, 95.00, 92.00, 1.00, NULL, NULL, '2025-04-17 12:30:06.936123', '2025-04-17 12:30:06.936199', 12.071, '12.071');
INSERT INTO "public"."calculation_result" VALUES (1912749978686730241, 1912749976144982017, 3, '废钢 C', 1912749898223202306, 95.00, 92.00, 1.00, NULL, NULL, '2025-04-17 14:08:26.765441', '2025-04-17 14:08:26.765441', 12.071, '12.071');
INSERT INTO "public"."calculation_result" VALUES (1912751438191276033, 1912751435653722113, 3, '废钢 C', 1912751407866458114, 95.00, 92.00, 2.50, NULL, NULL, '2025-04-17 14:14:14.725836', '2025-04-17 14:14:14.725836', 30.177, '30.177');
INSERT INTO "public"."calculation_result" VALUES (1912751611546054657, 1912751608886865921, 3, '废钢 C', 1912751585801416706, 95.00, 92.00, 2.50, NULL, NULL, '2025-04-17 14:14:56.066035', '2025-04-17 14:14:56.066035', 30.177, '30.177');
INSERT INTO "public"."calculation_result" VALUES (1912760669548294146, 1912760666788442114, 3, '废钢 C', 1912757739256508417, 95.00, 92.00, 1.00, NULL, NULL, '2025-04-17 14:50:55.656705', '2025-04-17 14:50:55.657736', 12.071, '12.071');
INSERT INTO "public"."calculation_result" VALUES (1912761612343947266, 1912761609718312961, 3, '废钢 C', 1912761584091115521, 95.00, 92.00, 1.00, NULL, NULL, '2025-04-17 14:54:40.43703', '2025-04-17 14:54:40.43703', 12.071, '12.071');
INSERT INTO "public"."calculation_result" VALUES (1912761824533786626, 1912761822164004865, 3, '废钢 C', 1912761793160392705, 95.00, 92.00, 1.00, NULL, NULL, '2025-04-17 14:55:31.027022', '2025-04-17 14:55:31.027022', 12.071, '12.071');
INSERT INTO "public"."calculation_result" VALUES (1912765590452559874, 1912765587730456577, 3, '废钢 C', 1912765561235038210, 95.00, 92.00, 1.00, NULL, NULL, '2025-04-17 15:10:28.889527', '2025-04-17 15:10:28.889527', 12.071, '12.071');
INSERT INTO "public"."calculation_result" VALUES (1912766234991255553, 1912766232126545922, 3, '废钢 C', 1912766062718607361, 95.00, 92.00, 2.50, NULL, NULL, '2025-04-17 15:13:02.568614', '2025-04-17 15:13:02.568614', 30.177, '30.177');
INSERT INTO "public"."calculation_result" VALUES (1912767271835787266, 1912767269289844737, 1, '铁矿石 A1', 1912767244325347329, 60.00, 90.00, 1.00, NULL, NULL, '2025-04-17 15:17:09.768825', '2025-04-17 15:17:09.768825', 19.537, '19.537');
INSERT INTO "public"."calculation_result" VALUES (1912768096649863169, 1912768094280081409, 1, '铁矿石 A1', 1912768072285151234, 60.00, 90.00, 2.50, NULL, NULL, '2025-04-17 15:20:26.419166', '2025-04-17 15:20:26.419166', 48.843, '48.843');
INSERT INTO "public"."calculation_result" VALUES (1912771322006728705, 1912771319301402626, 1, '铁矿石 A1', 1912771290536865793, 60.00, 90.00, 1.00, NULL, NULL, '2025-04-17 15:33:15.411321', '2025-04-17 15:33:15.411321', 19.537, '19.537');
INSERT INTO "public"."calculation_result" VALUES (1912778874379464706, 1912778871296651265, 1, '铁矿石 A1', 1912778847712079873, 60.00, 90.00, 1.00, NULL, NULL, '2025-04-17 16:03:16.033633', '2025-04-17 16:03:16.033633', 19.537, '19.537');
INSERT INTO "public"."calculation_result" VALUES (1912780662042161153, 1912780659689156610, 1, '铁矿石 A1', 1912780643985682434, 60.00, 90.00, 1.00, NULL, NULL, '2025-04-17 16:10:22.242974', '2025-04-17 16:10:22.242974', 19.537, '19.537');
INSERT INTO "public"."calculation_result" VALUES (1912780741926875138, 1912780739498373122, 1, '铁矿石 A1', 1912780643985682434, 60.00, 90.00, 1.00, NULL, NULL, '2025-04-17 16:10:41.293255', '2025-04-17 16:10:41.293255', 19.537, '19.537');
INSERT INTO "public"."calculation_result" VALUES (1912780886043160577, 1912780882826129410, 1, '铁矿石 A1', 1912780864396357633, 60.00, 90.00, 1.00, NULL, NULL, '2025-04-17 16:11:15.665288', '2025-04-17 16:11:15.666284', 19.537, '19.537');
INSERT INTO "public"."calculation_result" VALUES (1912784003484753922, 1912784000649404417, 1, '铁矿石 A1', 1912783964880379905, 60.00, 90.00, 2.50, NULL, NULL, '2025-04-17 16:23:38.897428', '2025-04-17 16:23:38.897428', 48.843, '48.843');
INSERT INTO "public"."calculation_result" VALUES (1912787562964094977, 1912787560317489153, 3, '废钢 C', 1912783964880379905, 95.00, 92.00, 2.50, NULL, NULL, '2025-04-17 16:37:47.555084', '2025-04-17 16:37:47.555084', 30.177, '30.177');
INSERT INTO "public"."calculation_result" VALUES (1912789370721701890, 1912789368146399233, 3, '废钢 C', 1912783964880379905, 95.00, 92.00, 2.50, NULL, NULL, '2025-04-17 16:44:58.545', '2025-04-17 16:44:58.545', 30.177, '30.177');
INSERT INTO "public"."calculation_result" VALUES (1913054934052528129, 1913054930269265921, 2, '焦炭 B', NULL, 85.00, 88.00, NULL, NULL, NULL, '2025-04-18 10:20:13.780209', '2025-04-18 10:20:13.780209', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1912570589957275650, 1, 1, '铁矿石 A1', 1, 60.00, 90.00, 3, NULL, NULL, '2025-04-17 02:15:37.150175', '2025-04-17 02:15:37.150175', 117.222, '117.222');
INSERT INTO "public"."calculation_result" VALUES (1912570591060377601, 1, 2, '焦炭 B', 2, 85.00, 88.00, 4, NULL, NULL, '2025-04-17 02:15:37.414434', '2025-04-17 02:15:37.414434', 28.209, '28.209');
INSERT INTO "public"."calculation_result" VALUES (1913032696687513601, 1913032696658153473, 1, '铁矿石 A1', 1913032560880144386, 60.00, 90.00, 1.00, NULL, NULL, '2025-04-18 08:51:51.977333', '2025-04-18 08:51:51.977513', 19.537, '19.537');
INSERT INTO "public"."calculation_result" VALUES (1913038529538314242, 1913038529504759810, 1, '铁矿石 A1', 1913038490896191489, 60.00, 90.00, 1.00, NULL, NULL, '2025-04-18 09:15:02.637726', '2025-04-18 09:15:02.637756', 19.537, '19.537');
INSERT INTO "public"."calculation_result" VALUES (1913054935369539585, 1913054930269265921, 3, '废钢 C', 1912783964880379905, 95.00, 92.00, 2.50, NULL, NULL, '2025-04-18 10:20:14.097409', '2025-04-18 10:20:14.097409', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913060086247493633, 1913060083504418818, 1, '铁矿石 A1', 1913060066530070529, 60.00, 90.00, 2.50, NULL, NULL, '2025-04-18 10:40:42.161155', '2025-04-18 10:40:42.161155', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913060087954575361, 1913060083504418818, 2, '焦炭 B', NULL, 85.00, 88.00, NULL, NULL, NULL, '2025-04-18 10:40:42.568924', '2025-04-18 10:40:42.568924', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913060089271586818, 1913060083504418818, 3, '废钢 C', 1913060066530070529, 95.00, 92.00, 2.50, NULL, NULL, '2025-04-18 10:40:42.889638', '2025-04-18 10:40:42.889638', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913060907760652290, 1913060905055326210, 1, '铁矿石 A1', 1913060706006241282, 60.00, 90.00, 1.00, NULL, NULL, '2025-04-18 10:43:58.020404', '2025-04-18 10:43:58.020404', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913060909077663745, 1913060905055326210, 2, '焦炭 B', NULL, 85.00, 88.00, NULL, NULL, NULL, '2025-04-18 10:43:58.339588', '2025-04-18 10:43:58.339588', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913060910273040385, 1913060905055326210, 3, '废钢 C', 1913060706006241282, 95.00, 92.00, 1.00, NULL, NULL, '2025-04-18 10:43:58.630365', '2025-04-18 10:43:58.630365', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913062736275869698, 1913062733704761345, 1, '铁矿石 A1', 1913060706006241282, 60.00, 90.00, 1.00, NULL, NULL, '2025-04-18 10:51:13.979767', '2025-04-18 10:51:13.979767', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913063925621092354, 1913063923515551745, 1, '铁矿石 A1', 1913063915252772865, 60.00, 90.00, 1.00, NULL, NULL, '2025-04-18 10:55:57.541943', '2025-04-18 10:55:57.541943', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913063927198150658, 1913063923515551745, 2, '焦炭 B', 1, 85.00, 88.00, 6.00, NULL, NULL, '2025-04-18 10:55:57.920523', '2025-04-18 10:55:57.920523', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913063928318029826, 1913063923515551745, 3, '废钢 C', 1913063915252772865, 95.00, 92.00, 1.00, NULL, NULL, '2025-04-18 10:55:58.179793', '2025-04-18 10:55:58.179793', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913064349195464706, 1913064347148644353, 1, '铁矿石 A1', 1913063915252772865, 60.00, 90.00, 1.00, NULL, NULL, '2025-04-18 10:57:38.52981', '2025-04-18 10:57:38.52981', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913064350919323649, 1913064347148644353, 2, '焦炭 B', 1, 85.00, 88.00, 6.00, NULL, NULL, '2025-04-18 10:57:38.939786', '2025-04-18 10:57:38.939786', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913064352240529409, 1913064347148644353, 3, '废钢 C', 1913063915252772865, 95.00, 92.00, 1.00, NULL, NULL, '2025-04-18 10:57:39.250734', '2025-04-18 10:57:39.251732', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913065287285116930, 1913065284915335170, 1, '铁矿石 A1', 1913065274211471361, 60.00, 90.00, 3.00, NULL, NULL, '2025-04-18 11:01:22.182957', '2025-04-18 11:01:22.182957', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913065289004781569, 1913065284915335170, 2, '焦炭 B', 1, 85.00, 88.00, 6.00, NULL, NULL, '2025-04-18 11:01:22.591475', '2025-04-18 11:01:22.591475', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913065290321793025, 1913065284915335170, 3, '废钢 C', 1913065274211471361, 95.00, 92.00, 3.00, NULL, NULL, '2025-04-18 11:01:22.910622', '2025-04-18 11:01:22.910622', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913065419107897345, 1913065416671006721, 1, '铁矿石 A1', 1913065274211471361, 60.00, 90.00, 3.00, NULL, NULL, '2025-04-18 11:01:53.620945', '2025-04-18 11:01:53.621943', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913065420957585409, 1913065416671006721, 2, '焦炭 B', 1, 85.00, 88.00, 6.00, NULL, NULL, '2025-04-18 11:01:54.063602', '2025-04-18 11:01:54.063602', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913065422350094338, 1913065416671006721, 3, '废钢 C', 1913065274211471361, 95.00, 92.00, 3.00, NULL, NULL, '2025-04-18 11:01:54.391011', '2025-04-18 11:01:54.391011', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913065718275018753, 1913065715762630657, 1, '铁矿石 A1', 1913065704270237697, 60.00, 90.00, 51.00, NULL, NULL, '2025-04-18 11:03:04.941347', '2025-04-18 11:03:04.941347', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913065719923380226, 1913065715762630657, 2, '焦炭 B', 1, 85.00, 88.00, 6.00, NULL, NULL, '2025-04-18 11:03:05.340839', '2025-04-18 11:03:05.340839', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913065721311694849, 1913065715762630657, 3, '废钢 C', 1913065704270237697, 95.00, 92.00, 51.00, NULL, NULL, '2025-04-18 11:03:05.660983', '2025-04-18 11:03:05.660983', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913075160794263553, 1913075158432870401, 1, '铁矿石 A1', 1912783964880379905, 60.00, 90.00, 2.50, NULL, NULL, '2025-04-18 11:40:36.214256', '2025-04-18 11:40:36.215251', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913075162434236417, 1913075158432870401, 2, '焦炭 B', 1, 85.00, 88.00, 6.00, NULL, NULL, '2025-04-18 11:40:36.603181', '2025-04-18 11:40:36.603181', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913075163755442178, 1913075158432870401, 3, '废钢 C', 1912783964880379905, 95.00, 92.00, 2.50, NULL, NULL, '2025-04-18 11:40:36.923324', '2025-04-18 11:40:36.923324', NULL, NULL);
INSERT INTO "public"."calculation_result" VALUES (1913077914681884674, 1913077912479875074, 1, '铁矿石 A1', 1912783964880379905, 60.00, 90.00, 2.50, NULL, NULL, '2025-04-18 11:51:32.797027', '2025-04-18 11:51:32.797027', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1913077916388966402, 1913077912479875074, 2, '焦炭 B', 1, 85.00, 88.00, 6.00, NULL, NULL, '2025-04-18 11:51:33.20394', '2025-04-18 11:51:33.20394', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1913077917638868994, 1913077912479875074, 3, '废钢 C', 1912783964880379905, 95.00, 92.00, 2.50, NULL, NULL, '2025-04-18 11:51:33.495161', '2025-04-18 11:51:33.495161', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1913156068352118786, 1913156066032668674, 1, '铁矿石 A1', 1912783964880379905, 60.00, 90.00, 2.50, NULL, NULL, '2025-04-18 17:02:06.078474', '2025-04-18 17:02:06.078474', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1913156070000480257, 1913156066032668674, 2, '焦炭 B', 1, 85.00, 88.00, 6.00, NULL, NULL, '2025-04-18 17:02:06.476378', '2025-04-18 17:02:06.476378', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1913156071258771458, 1913156066032668674, 3, '废钢 C', 1912783964880379905, 95.00, 92.00, 2.50, NULL, NULL, '2025-04-18 17:02:06.776612', '2025-04-18 17:02:06.776612', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1913156786379280385, 1913156784034664450, 1, '铁矿石 A1', 1912783964880379905, 60.00, 90.00, 2.50, NULL, NULL, '2025-04-18 17:04:57.278896', '2025-04-18 17:04:57.278896', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1913156788023447553, 1913156784034664450, 2, '焦炭 B', 1, 85.00, 88.00, 6.00, NULL, NULL, '2025-04-18 17:04:57.66748', '2025-04-18 17:04:57.66748', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1913156789466288129, 1913156784034664450, 3, '废钢 C', 1912783964880379905, 95.00, 92.00, 2.50, NULL, NULL, '2025-04-18 17:04:58.007192', '2025-04-18 17:04:58.007192', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1913158342348611585, 1913158339643285505, 1, '铁矿石 A1', 1, 60.00, 90.00, 6.00, NULL, NULL, '2025-04-18 17:11:08.249625', '2025-04-18 17:11:08.249625', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1913253480693620737, 1913253480551014401, 1, '铁矿石 A1', 1913150302102769665, 60.00, 90.00, 3.00, NULL, NULL, '2025-04-18 23:29:10.986507', '2025-04-18 23:29:10.986587', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1913253582543904770, 1913253582439047169, 1, '铁矿石 A1', 1913150302102769665, 60.00, 90.00, 3.00, NULL, NULL, '2025-04-18 23:29:35.27051', '2025-04-18 23:29:35.270579', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1913253816703508481, 1913253816661565441, 1, '铁矿石 A1', 1913150302102769665, 60.00, 90.00, 3.00, NULL, NULL, '2025-04-18 23:30:31.097836', '2025-04-18 23:30:31.097915', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1913264727061291010, 1913264727019347970, 1, '铁矿石 A1', 1913150302102769665, 60.00, 90.00, 3.00, NULL, NULL, '2025-04-19 00:13:52.329595', '2025-04-19 00:13:52.329877', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1913961487987499009, 1913961487953944577, 1, '铁矿石 A1', 1, 60.00, 90.00, 6.00, NULL, NULL, '2025-04-20 22:22:33.07937', '2025-04-20 22:22:33.079411', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1913961660201426945, 1913961660167872513, 5, '硅铁 E', 1, 75.00, 85.00, 6.00, NULL, NULL, '2025-04-20 22:23:14.137841', '2025-04-20 22:23:14.13787', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1913961660214009857, 1913961660167872513, 6, '锰铁 F', 1, 65.00, 82.00, 6.00, NULL, NULL, '2025-04-20 22:23:14.141514', '2025-04-20 22:23:14.141541', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1913961660230787074, 1913961660167872513, 8, '钼铁 H', 1, 60.00, 80.00, 6.00, NULL, NULL, '2025-04-20 22:23:14.145482', '2025-04-20 22:23:14.145514', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914132742007021569, 1914132741986050049, 5, '硅铁 E', 1, 75.00, 85.00, 6.00, NULL, NULL, '2025-04-21 09:43:03.219584', '2025-04-21 09:43:03.219613', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914132742027993089, 1914132741986050049, 6, '锰铁 F', 1, 65.00, 82.00, 6.00, NULL, NULL, '2025-04-21 09:43:03.224332', '2025-04-21 09:43:03.224358', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914132742044770306, 1914132741986050049, 8, '钼铁 H', 1, 60.00, 80.00, 6.00, NULL, NULL, '2025-04-21 09:43:03.227961', '2025-04-21 09:43:03.227988', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914230607475519489, 1914230604556283906, 5, '硅铁 E', 1, 75.00, 85.00, 6.00, NULL, NULL, '2025-04-21 16:11:56.172193', '2025-04-21 16:11:56.172193', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914230609371344897, 1914230604556283906, 6, '锰铁 F', 1, 65.00, 82.00, 6.00, NULL, NULL, '2025-04-21 16:11:56.622388', '2025-04-21 16:11:56.622388', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914230611242004481, 1914230604556283906, 8, '钼铁 H', 1, 60.00, 80.00, 6.00, NULL, NULL, '2025-04-21 16:11:57.072011', '2025-04-21 16:11:57.073008', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914230886098939905, 1914230883364253697, 5, '硅铁 E', 1, 75.00, 85.00, 6.00, NULL, NULL, '2025-04-21 16:13:02.603787', '2025-04-21 16:13:02.603787', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914230887734718466, 1914230883364253697, 6, '锰铁 F', 1, 65.00, 82.00, 6.00, NULL, NULL, '2025-04-21 16:13:02.992411', '2025-04-21 16:13:02.992411', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914230889437605890, 1914230883364253697, 8, '钼铁 H', 1, 60.00, 80.00, 6.00, NULL, NULL, '2025-04-21 16:13:03.392991', '2025-04-21 16:13:03.392991', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914233565596495873, 1914233562857615361, 5, '硅铁 E', 1, 75.00, 85.00, 6.00, NULL, NULL, '2025-04-21 16:23:41.44377', '2025-04-21 16:23:41.44377', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914233567362297858, 1914233562857615361, 6, '锰铁 F', 1, 65.00, 82.00, 6.00, NULL, NULL, '2025-04-21 16:23:41.864344', '2025-04-21 16:23:41.864344', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914233569132294146, 1914233562857615361, 8, '钼铁 H', 1, 60.00, 80.00, 6.00, NULL, NULL, '2025-04-21 16:23:42.284086', '2025-04-21 16:23:42.284086', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914237130658414593, 1914237127323942914, 5, '硅铁 E', 1, 75.00, 85.00, 6.00, NULL, NULL, '2025-04-21 16:37:51.417701', '2025-04-21 16:37:51.417701', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914237132420022274, 1914237127323942914, 6, '锰铁 F', 1, 65.00, 82.00, 6.00, NULL, NULL, '2025-04-21 16:37:51.84592', '2025-04-21 16:37:51.84592', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914237134173241345, 1914237127323942914, 8, '钼铁 H', 1, 60.00, 80.00, 6.00, NULL, NULL, '2025-04-21 16:37:52.25631', '2025-04-21 16:37:52.25631', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914590990724046849, 1914590990585634818, 5, '硅铁 E', 1914590982104752130, 75.00, 85.00, 1.00, NULL, NULL, '2025-04-22 16:03:58.228226', '2025-04-22 16:03:58.228271', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914590990765989889, 1914590990585634818, 6, '锰铁 F', 1914590982134112258, 65.00, 82.00, 1.00, NULL, NULL, '2025-04-22 16:03:58.237717', '2025-04-22 16:03:58.237761', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914590990786961409, 1914590990585634818, 8, '钼铁 H', 1914590982062809090, 60.00, 80.00, 1.00, NULL, NULL, '2025-04-22 16:03:58.242463', '2025-04-22 16:03:58.242493', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914591448779792386, 1914591448708489218, 5, '硅铁 E', 1914591434850508802, 75.00, 85.00, 1.00, NULL, NULL, '2025-04-22 16:05:47.436434', '2025-04-22 16:05:47.436455', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914591448792375298, 1914591448708489218, 6, '锰铁 F', 1914591434858897409, 65.00, 82.00, 1.00, NULL, NULL, '2025-04-22 16:05:47.439845', '2025-04-22 16:05:47.439869', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914591448804958209, 1914591448708489218, 8, '钼铁 H', 1914591434825342978, 60.00, 80.00, 1.00, NULL, NULL, '2025-04-22 16:05:47.442806', '2025-04-22 16:05:47.442842', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914592304019349505, 1914592303956434945, 5, '硅铁 E', 1914592298180878337, 75.00, 85.00, 1.00, NULL, NULL, '2025-04-22 16:09:11.341187', '2025-04-22 16:09:11.34124', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914592304031932417, 1914592303956434945, 6, '锰铁 F', 1914592298185072642, 65.00, 82.00, 1.00, NULL, NULL, '2025-04-22 16:09:11.344161', '2025-04-22 16:09:11.344182', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914592304040321026, 1914592303956434945, 8, '钼铁 H', 1914592298151518209, 60.00, 80.00, 1.00, NULL, NULL, '2025-04-22 16:09:11.346657', '2025-04-22 16:09:11.346686', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914845583810883586, 1914845583655694338, 5, '硅铁 E', 1914845574805712898, 75.00, 85.00, 1.00, NULL, NULL, '2025-04-23 08:55:37.948537', '2025-04-23 08:55:37.948585', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914845583836049410, 1914845583655694338, 6, '锰铁 F', 1914845574814101505, 65.00, 82.00, 1.00, NULL, NULL, '2025-04-23 08:55:37.954705', '2025-04-23 08:55:37.954744', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914845583852826625, 1914845583655694338, 8, '钼铁 H', 1914845574759575553, 60.00, 80.00, 1.00, NULL, NULL, '2025-04-23 08:55:37.95897', '2025-04-23 08:55:37.959008', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914846331923722242, 1914846331814670338, 5, '硅铁 E', 1914846316555792386, 75.00, 85.00, 1.00, NULL, NULL, '2025-04-23 08:58:36.31228', '2025-04-23 08:58:36.312417', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914846331940499457, 1914846331814670338, 6, '锰铁 F', 1914846316564180994, 65.00, 82.00, 1.00, NULL, NULL, '2025-04-23 08:58:36.316252', '2025-04-23 08:58:36.316281', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914846331953082369, 1914846331814670338, 8, '钼铁 H', 1914846316530626562, 60.00, 80.00, 1.00, NULL, NULL, '2025-04-23 08:58:36.319664', '2025-04-23 08:58:36.319695', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881482816860162, 1914881482674253826, 4, '石灰石 D', 1, 90.00, 95.00, 6.00, NULL, NULL, '2025-04-23 11:18:16.938721', '2025-04-23 11:18:16.938762', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914881482837831682, 1914881482674253826, 5, '硅铁 E', 1914881471471267841, 75.00, 85.00, 1.00, NULL, NULL, '2025-04-23 11:18:16.943902', '2025-04-23 11:18:16.943933', 421.677, '421.677');
INSERT INTO "public"."calculation_result" VALUES (1914881482858803201, 1914881482674253826, 6, '锰铁 F', 1914881471479656449, 65.00, 82.00, 1.00, NULL, NULL, '2025-04-23 11:18:16.94862', '2025-04-23 11:18:16.948648', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881482871386114, 1914881482674253826, 7, '镍板 G', 1914881471420936194, 99.00, 98.00, 333.00, NULL, NULL, '2025-04-23 11:18:16.952297', '2025-04-23 11:18:16.952325', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881482883969026, 1914881482674253826, 8, '钼铁 H', 1914881471433519106, 60.00, 80.00, 1.00, NULL, NULL, '2025-04-23 11:18:16.956044', '2025-04-23 11:18:16.956079', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881482913329153, 1914881482674253826, 9, '钛铁 I', 1, 70.00, 83.00, 6.00, NULL, NULL, '2025-04-23 11:18:16.961854', '2025-04-23 11:18:16.961894', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881482930106370, 1914881482674253826, 10, '铜精矿 J', 1914881471441907714, 25.00, 87.00, 1.00, NULL, NULL, '2025-04-23 11:18:16.966073', '2025-04-23 11:18:16.966109', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881482955272194, 1914881482674253826, 11, '锌精矿 K', 1, 55.00, 91.00, 6.00, NULL, NULL, '2025-04-23 11:18:16.972098', '2025-04-23 11:18:16.972126', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881482976243713, 1914881482674253826, 12, '铝锭 L', 1, 99.50, 96.00, 6.00, NULL, NULL, '2025-04-23 11:18:16.976647', '2025-04-23 11:18:16.976694', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881482993020930, 1914881482674253826, 13, '铅精矿 M', 1, 45.00, 89.00, 6.00, NULL, NULL, '2025-04-23 11:18:16.980849', '2025-04-23 11:18:16.980879', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881483009798145, 1914881482674253826, 14, '锡精矿 N', 1, 30.00, 86.00, 6.00, NULL, NULL, '2025-04-23 11:18:16.984531', '2025-04-23 11:18:16.984584', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881483026575361, 1914881482674253826, 1, '铁矿石 A1', 1, 60.00, 90.00, 6.00, NULL, NULL, '2025-04-23 11:18:16.989112', '2025-04-23 11:18:16.989149', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881483047546881, 1914881482674253826, 2, '焦炭 B', 1, 85.00, 88.00, 6.00, NULL, NULL, '2025-04-23 11:18:16.993778', '2025-04-23 11:18:16.99384', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881483064324097, 1914881482674253826, 3, '废钢 C', 1, 95.00, 92.00, 6.00, NULL, NULL, '2025-04-23 11:18:16.998134', '2025-04-23 11:18:16.998172', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881483085295618, 1914881482674253826, 1909794432769794050, 'cs2', 1, 5.00, 11.00, 6.00, NULL, NULL, '2025-04-23 11:18:17.002927', '2025-04-23 11:18:17.002967', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881483106267137, 1914881482674253826, 1909788114226909185, '1', 1, 4.00, 2.00, 6.00, NULL, NULL, '2025-04-23 11:18:17.008022', '2025-04-23 11:18:17.008291', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881483127238658, 1914881482674253826, 15, '硼铁 O', 1, 20.00, 78.00, 6.00, NULL, NULL, '2025-04-23 11:18:17.013143', '2025-04-23 11:18:17.013184', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881483148210177, 1914881482674253826, 1909787651972665345, 'cs', 1, 22.00, 123.00, 6.00, NULL, NULL, '2025-04-23 11:18:17.017749', '2025-04-23 11:18:17.017778', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881483160793090, 1914881482674253826, 1909447077478428673, '铁矿石 AAAA', 1, 61.00, 90.01, 6.00, NULL, NULL, '2025-04-23 11:18:17.021615', '2025-04-23 11:18:17.021641', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881483177570305, 1914881482674253826, 1909787950200262658, '5', 1, 4.00, 2.00, 6.00, NULL, NULL, '2025-04-23 11:18:17.025278', '2025-04-23 11:18:17.025308', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881483194347521, 1914881482674253826, 1909447281355157506, '铁矿石 AAAA222', 1, 61.00, 90.01, 6.00, NULL, NULL, '2025-04-23 11:18:17.028727', '2025-04-23 11:18:17.028752', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881483206930433, 1914881482674253826, 16, '铁矿石 A', 1, 60.00, 90.00, 6.00, NULL, NULL, '2025-04-23 11:18:17.032118', '2025-04-23 11:18:17.032148', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881483223707649, 1914881482674253826, 0, '333', 1, 333.00, 333.00, 6.00, NULL, NULL, '2025-04-23 11:18:17.035383', '2025-04-23 11:18:17.035408', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550030581762, 1914881549934112769, 4, '石灰石 D', 1, 90.00, 95.00, 6.00, NULL, NULL, '2025-04-23 11:18:32.963962', '2025-04-23 11:18:32.963988', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914881550043164674, 1914881549934112769, 5, '硅铁 E', 1914881471471267841, 75.00, 85.00, 1.00, NULL, NULL, '2025-04-23 11:18:32.967083', '2025-04-23 11:18:32.967116', 421.677, '421.677');
INSERT INTO "public"."calculation_result" VALUES (1914881550055747585, 1914881549934112769, 6, '锰铁 F', 1914881471479656449, 65.00, 82.00, 1.00, NULL, NULL, '2025-04-23 11:18:32.970298', '2025-04-23 11:18:32.970356', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550072524801, 1914881549934112769, 7, '镍板 G', 1914881471420936194, 99.00, 98.00, 333.00, NULL, NULL, '2025-04-23 11:18:32.973457', '2025-04-23 11:18:32.973506', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550085107714, 1914881549934112769, 8, '钼铁 H', 1914881471433519106, 60.00, 80.00, 1.00, NULL, NULL, '2025-04-23 11:18:32.976764', '2025-04-23 11:18:32.97679', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550101884930, 1914881549934112769, 9, '钛铁 I', 1, 70.00, 83.00, 6.00, NULL, NULL, '2025-04-23 11:18:32.980748', '2025-04-23 11:18:32.980781', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550114467842, 1914881549934112769, 10, '铜精矿 J', 1914881471441907714, 25.00, 87.00, 1.00, NULL, NULL, '2025-04-23 11:18:32.983917', '2025-04-23 11:18:32.983951', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550131245057, 1914881549934112769, 11, '锌精矿 K', 1, 55.00, 91.00, 6.00, NULL, NULL, '2025-04-23 11:18:32.988039', '2025-04-23 11:18:32.988076', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550148022273, 1914881549934112769, 12, '铝锭 L', 1, 99.50, 96.00, 6.00, NULL, NULL, '2025-04-23 11:18:32.992206', '2025-04-23 11:18:32.992245', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550164799490, 1914881549934112769, 13, '铅精矿 M', 1, 45.00, 89.00, 6.00, NULL, NULL, '2025-04-23 11:18:32.996183', '2025-04-23 11:18:32.996217', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550181576705, 1914881549934112769, 14, '锡精矿 N', 1, 30.00, 86.00, 6.00, NULL, NULL, '2025-04-23 11:18:33.000184', '2025-04-23 11:18:33.00025', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550198353922, 1914881549934112769, 1, '铁矿石 A1', 1, 60.00, 90.00, 6.00, NULL, NULL, '2025-04-23 11:18:33.004167', '2025-04-23 11:18:33.004224', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550215131138, 1914881549934112769, 2, '焦炭 B', 1, 85.00, 88.00, 6.00, NULL, NULL, '2025-04-23 11:18:33.007998', '2025-04-23 11:18:33.008051', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550231908353, 1914881549934112769, 3, '废钢 C', 1, 95.00, 92.00, 6.00, NULL, NULL, '2025-04-23 11:18:33.011911', '2025-04-23 11:18:33.011947', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550278045697, 1914881549934112769, 1909794432769794050, 'cs2', 1, 5.00, 11.00, 6.00, NULL, NULL, '2025-04-23 11:18:33.023356', '2025-04-23 11:18:33.023407', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550294822914, 1914881549934112769, 1909788114226909185, '1', 1, 4.00, 2.00, 6.00, NULL, NULL, '2025-04-23 11:18:33.027408', '2025-04-23 11:18:33.027437', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550311600130, 1914881549934112769, 15, '硼铁 O', 1, 20.00, 78.00, 6.00, NULL, NULL, '2025-04-23 11:18:33.031374', '2025-04-23 11:18:33.031417', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550328377345, 1914881549934112769, 1909787651972665345, 'cs', 1, 22.00, 123.00, 6.00, NULL, NULL, '2025-04-23 11:18:33.035334', '2025-04-23 11:18:33.035363', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550345154562, 1914881549934112769, 1909447077478428673, '铁矿石 AAAA', 1, 61.00, 90.01, 6.00, NULL, NULL, '2025-04-23 11:18:33.038649', '2025-04-23 11:18:33.038674', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550357737473, 1914881549934112769, 1909787950200262658, '5', 1, 4.00, 2.00, 6.00, NULL, NULL, '2025-04-23 11:18:33.041736', '2025-04-23 11:18:33.041761', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550370320386, 1914881549934112769, 1909447281355157506, '铁矿石 AAAA222', 1, 61.00, 90.01, 6.00, NULL, NULL, '2025-04-23 11:18:33.045189', '2025-04-23 11:18:33.045212', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550382903297, 1914881549934112769, 16, '铁矿石 A', 1, 60.00, 90.00, 6.00, NULL, NULL, '2025-04-23 11:18:33.04828', '2025-04-23 11:18:33.048305', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914881550391291905, 1914881549934112769, 0, '333', 1, 333.00, 333.00, 6.00, NULL, NULL, '2025-04-23 11:18:33.05168', '2025-04-23 11:18:33.051725', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914926907582164994, 1914926907494084609, 5, '硅铁 E', 1914926895393517570, 75.00, 85.00, 1.00, NULL, NULL, '2025-04-23 14:18:47.046415', '2025-04-23 14:18:47.046445', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914926907590553601, 1914926907494084609, 6, '锰铁 F', 1914926895397711874, 65.00, 82.00, 1.00, NULL, NULL, '2025-04-23 14:18:47.049285', '2025-04-23 14:18:47.049309', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914926907603136514, 1914926907494084609, 8, '钼铁 H', 1914926895368351746, 60.00, 80.00, 1.00, NULL, NULL, '2025-04-23 14:18:47.051624', '2025-04-23 14:18:47.051646', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914927759789240321, 1914927759713742849, 5, '硅铁 E', 1914927753040605185, 75.00, 85.00, 1.00, NULL, NULL, '2025-04-23 14:22:10.229313', '2025-04-23 14:22:10.229343', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914927759801823233, 1914927759713742849, 6, '锰铁 F', 1914927753044799490, 65.00, 82.00, 1.00, NULL, NULL, '2025-04-23 14:22:10.231985', '2025-04-23 14:22:10.232006', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914927759814406145, 1914927759713742849, 8, '钼铁 H', 1914927753011245057, 60.00, 80.00, 1.00, NULL, NULL, '2025-04-23 14:22:10.234459', '2025-04-23 14:22:10.234482', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914934190185537538, 1914934186020593665, 4, '石灰石 D', 1, 90.00, 95.00, 6.00, NULL, NULL, '2025-04-23 14:47:43.358958', '2025-04-23 14:47:43.358958', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914934191619989505, 1914934186020593665, 5, '硅铁 E', 1914934141485473793, 75.00, 85.00, 3.50, NULL, NULL, '2025-04-23 14:47:43.706674', '2025-04-23 14:47:43.706674', 953.471, '953.471');
INSERT INTO "public"."calculation_result" VALUES (1914934193511620610, 1914934186020593665, 6, '锰铁 F', 1, 65.00, 82.00, 6.00, NULL, NULL, '2025-04-23 14:47:44.156871', '2025-04-23 14:47:44.156871', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914934195478749186, 1914934186020593665, 9, '钛铁 I', 1, 70.00, 83.00, 6.00, NULL, NULL, '2025-04-23 14:47:44.61702', '2025-04-23 14:47:44.61702', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914934197164859393, 1914934186020593665, 1909794432769794050, 'cs2', 1, 5.00, 11.00, 6.00, NULL, NULL, '2025-04-23 14:47:45.02851', '2025-04-23 14:47:45.02851', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914934198809026561, 1914934186020593665, 1909787651972665345, 'cs', 1, 22.00, 123.00, 6.00, NULL, NULL, '2025-04-23 14:47:45.417591', '2025-04-23 14:47:45.417591', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914934263501971457, 1914934258460418049, 4, '石灰石 D', 1, 90.00, 95.00, 6.00, NULL, NULL, '2025-04-23 14:48:00.844119', '2025-04-23 14:48:00.844119', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914934266530258946, 1914934258460418049, 5, '硅铁 E', 1914934141485473793, 75.00, 85.00, 3.50, NULL, NULL, '2025-04-23 14:48:01.56717', '2025-04-23 14:48:01.56717', 953.471, '953.471');
INSERT INTO "public"."calculation_result" VALUES (1914934269667598337, 1914934258460418049, 6, '锰铁 F', 1, 65.00, 82.00, 6.00, NULL, NULL, '2025-04-23 14:48:02.317417', '2025-04-23 14:48:02.317417', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914934271894773762, 1914934258460418049, 9, '钛铁 I', 1, 70.00, 83.00, 6.00, NULL, NULL, '2025-04-23 14:48:02.836886', '2025-04-23 14:48:02.836886', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914934273517969409, 1914934258460418049, 1909794432769794050, 'cs2', 1, 5.00, 11.00, 6.00, NULL, NULL, '2025-04-23 14:48:03.226965', '2025-04-23 14:48:03.226965', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914934275497680898, 1914934258460418049, 1909787651972665345, 'cs', 1, 22.00, 123.00, 6.00, NULL, NULL, '2025-04-23 14:48:03.697435', '2025-04-23 14:48:03.697435', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914934638468554753, 1914934634228113410, 4, '石灰石 D', 1, 90.00, 95.00, 6.00, NULL, NULL, '2025-04-23 14:49:30.23667', '2025-04-23 14:49:30.23667', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914934639777177601, 1914934634228113410, 5, '硅铁 E', 1914934141485473793, 75.00, 85.00, 3.50, NULL, NULL, '2025-04-23 14:49:30.557108', '2025-04-23 14:49:30.557108', 953.471, '953.471');
INSERT INTO "public"."calculation_result" VALUES (1914934641551368194, 1914934634228113410, 6, '锰铁 F', 1, 65.00, 82.00, 6.00, NULL, NULL, '2025-04-23 14:49:30.970785', '2025-04-23 14:49:30.970785', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914934643451387905, 1914934634228113410, 9, '钛铁 I', 1, 70.00, 83.00, 6.00, NULL, NULL, '2025-04-23 14:49:31.427015', '2025-04-23 14:49:31.427015', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914934645078777857, 1914934634228113410, 1909794432769794050, 'cs2', 1, 5.00, 11.00, 6.00, NULL, NULL, '2025-04-23 14:49:31.817637', '2025-04-23 14:49:31.817637', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914934646781665281, 1914934634228113410, 1909787651972665345, 'cs', 1, 22.00, 123.00, 6.00, NULL, NULL, '2025-04-23 14:49:32.226951', '2025-04-23 14:49:32.226951', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914938944907489282, 1914938944831991809, 5, '硅铁 E', 1914938900351397890, 75.00, 85.00, 1.00, NULL, NULL, '2025-04-23 15:06:36.968704', '2025-04-23 15:06:36.968729', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914938944920072194, 1914938944831991809, 6, '锰铁 F', 1914938900359786498, 65.00, 82.00, 1.00, NULL, NULL, '2025-04-23 15:06:36.971402', '2025-04-23 15:06:36.971426', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914938944928460801, 1914938944831991809, 8, '钼铁 H', 1914938900330426369, 60.00, 80.00, 1.00, NULL, NULL, '2025-04-23 15:06:36.973859', '2025-04-23 15:06:36.973879', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914939792265949186, 1914939792207228930, 5, '硅铁 E', 1914939784300965889, 75.00, 85.00, 1.00, NULL, NULL, '2025-04-23 15:09:58.995005', '2025-04-23 15:09:58.995028', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914939792278532098, 1914939792207228930, 6, '锰铁 F', 1914939784305160194, 65.00, 82.00, 1.00, NULL, NULL, '2025-04-23 15:09:58.997576', '2025-04-23 15:09:58.997629', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914939792286920706, 1914939792207228930, 8, '钼铁 H', 1914939784279994369, 60.00, 80.00, 1.00, NULL, NULL, '2025-04-23 15:09:59.00007', '2025-04-23 15:09:59.000094', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914956331044065282, 1914956330867904514, 5, '硅铁 E', 1914956320449253378, 75.00, 85.00, 1.00, NULL, NULL, '2025-04-23 16:15:42.14717', '2025-04-23 16:15:42.147247', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914956331073425410, 1914956330867904514, 6, '锰铁 F', 1914956320461836290, 65.00, 82.00, 1.00, NULL, NULL, '2025-04-23 16:15:42.153641', '2025-04-23 16:15:42.153679', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914956331115368449, 1914956330867904514, 8, '钼铁 H', 1914956320415698946, 60.00, 80.00, 1.00, NULL, NULL, '2025-04-23 16:15:42.164501', '2025-04-23 16:15:42.164545', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914964029739388930, 1914964029634531329, 5, '硅铁 E', 1914963141666820097, 75.00, 85.00, 1.00, NULL, NULL, '2025-04-23 16:46:17.659341', '2025-04-23 16:46:17.659572', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914964029764554753, 1914964029634531329, 6, '锰铁 F', 1914963141675208705, 65.00, 82.00, 1.00, NULL, NULL, '2025-04-23 16:46:17.664663', '2025-04-23 16:46:17.664698', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914964029781331969, 1914964029634531329, 8, '钼铁 H', 1914963141637459970, 60.00, 80.00, 1.00, NULL, NULL, '2025-04-23 16:46:17.668897', '2025-04-23 16:46:17.668936', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914969335416508417, 1914969335315845121, 5, '硅铁 E', 1914969327745126401, 75.00, 85.00, 1.00, NULL, NULL, '2025-04-23 17:07:22.631113', '2025-04-23 17:07:22.631154', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914969335429091330, 1914969335315845121, 6, '锰铁 F', 1914969327753515010, 65.00, 82.00, 1.00, NULL, NULL, '2025-04-23 17:07:22.634459', '2025-04-23 17:07:22.634499', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914969335437479938, 1914969335315845121, 8, '钼铁 H', 1914969327724154882, 60.00, 80.00, 1.00, NULL, NULL, '2025-04-23 17:07:22.637665', '2025-04-23 17:07:22.637703', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914975378636820481, 1914975378540351489, 5, '硅铁 E', 1914975363755429890, 75.00, 85.00, 1.00, NULL, NULL, '2025-04-23 17:31:23.44675', '2025-04-23 17:31:23.446786', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914975378649403394, 1914975378540351489, 6, '锰铁 F', 1914975363759624193, 65.00, 82.00, 1.00, NULL, NULL, '2025-04-23 17:31:23.450507', '2025-04-23 17:31:23.450546', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914975378666180609, 1914975378540351489, 8, '钼铁 H', 1914975363734458369, 60.00, 80.00, 1.00, NULL, NULL, '2025-04-23 17:31:23.453617', '2025-04-23 17:31:23.453656', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1914976426734669826, 1914976426642395138, 5, '硅铁 E', 1914976417070993410, 75.00, 85.00, 1.00, NULL, NULL, '2025-04-23 17:35:33.332758', '2025-04-23 17:35:33.332807', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1914976426747252738, 1914976426642395138, 6, '锰铁 F', 1914976417079382017, 65.00, 82.00, 1.00, NULL, NULL, '2025-04-23 17:35:33.336055', '2025-04-23 17:35:33.336114', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1914976426759835650, 1914976426642395138, 8, '钼铁 H', 1914976417054216193, 60.00, 80.00, 1.00, NULL, NULL, '2025-04-23 17:35:33.339376', '2025-04-23 17:35:33.339449', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915221826943123457, 1915221826846654466, 5, '硅铁 E', 1915221749222670338, 75.00, 85.00, 1.00, NULL, NULL, '2025-04-24 09:50:41.301884', '2025-04-24 09:50:41.862272', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1915221826955706370, 1915221826846654466, 6, '锰铁 F', 1915221749235253250, 65.00, 82.00, 1.00, NULL, NULL, '2025-04-24 09:50:41.304959', '2025-04-24 09:50:41.864446', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1915221826972483586, 1915221826846654466, 8, '钼铁 H', 1915221749197504513, 60.00, 80.00, 1.00, NULL, NULL, '2025-04-24 09:50:41.308982', '2025-04-24 09:50:41.865795', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915241595721150465, 1915241584211980289, 12, '铝锭 L', 1915239217039048705, 99.50, 96.00, 0.03, NULL, NULL, '2025-04-24 11:09:14.551359', '2025-04-24 11:09:20.015781', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1915241597554061313, 1915241584211980289, 13, '铅精矿 M', 1915239211062165506, 45.00, 89.00, 0.01, NULL, NULL, '2025-04-24 11:09:14.99302', '2025-04-24 11:09:20.098716', 953.471, '953.471');
INSERT INTO "public"."calculation_result" VALUES (1915241599445692417, 1915241584211980289, 14, '锡精矿 N', 1915239211062165506, 30.00, 86.00, 0.01, NULL, NULL, '2025-04-24 11:09:15.439529', '2025-04-24 11:09:20.178801', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915241601349906433, 1915241584211980289, 15, '硼铁 O', 1915239211062165506, 20.00, 78.00, 0.01, NULL, NULL, '2025-04-24 11:09:15.900025', '2025-04-24 11:09:20.260838', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915241603241537537, 1915241584211980289, 16, '铁矿石 A', 1915239211062165506, 60.00, 90.00, 0.01, NULL, NULL, '2025-04-24 11:09:16.340038', '2025-04-24 11:09:20.338772', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915241604868927490, 1915241584211980289, 0, '333', 1915239211062165506, 333.00, 333.00, 0.01, NULL, NULL, '2025-04-24 11:09:16.738967', '2025-04-24 11:09:20.418375', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915243038511472642, 1915243038301757441, 4, '石灰石 D', 1915238006756016129, 90.00, 95.00, 3.00, NULL, NULL, '2025-04-24 11:14:58.534352', '2025-04-24 11:14:59.283908', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1915243038624718850, 1915243038301757441, 1909787651972665345, 'cs', 1915238006756016129, 22.00, 123.00, 3.00, NULL, NULL, '2025-04-24 11:14:58.561319', '2025-04-24 11:14:59.287741', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1915243038700216321, 1915243038301757441, 1909794432769794050, 'cs2', 1915238006756016129, 5.00, 11.00, 3.00, NULL, NULL, '2025-04-24 11:14:58.579899', '2025-04-24 11:14:59.290347', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915253796171423745, 1915253795978485762, 4, '石灰石 D', 1915253783382990850, 90.00, 95.00, 0.01, NULL, NULL, '2025-04-24 11:57:43.360163', '2025-04-24 11:57:43.419698', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1915253796204978178, 1915253795978485762, 1909787651972665345, 'cs', 1915253783382990850, 22.00, 123.00, 0.01, NULL, NULL, '2025-04-24 11:57:43.368371', '2025-04-24 11:57:43.421679', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1915253796246921217, 1915253795978485762, 1909794432769794050, 'cs2', 1915253783382990850, 5.00, 11.00, 0.01, NULL, NULL, '2025-04-24 11:57:43.377687', '2025-04-24 11:57:43.423136', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915286568315396097, 1915286568281841666, 4, '石灰石 D', 1915286479769444353, 90.00, 95.00, 1.00, NULL, NULL, '2025-04-24 14:07:56.848379', '2025-04-24 14:07:56.878672', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1915286568336367618, 1915286568281841666, 1909787651972665345, 'cs', 1915286479769444353, 22.00, 123.00, 1.00, NULL, NULL, '2025-04-24 14:07:56.853276', '2025-04-24 14:07:56.879918', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1915286568348950530, 1915286568281841666, 1909794432769794050, 'cs2', 1915286479769444353, 5.00, 11.00, 1.00, NULL, NULL, '2025-04-24 14:07:56.857612', '2025-04-24 14:07:56.880746', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915287383050559489, 1915287382949896194, 4, '石灰石 D', 1915274372743176194, 90.00, 95.00, 0.01, NULL, NULL, '2025-04-24 14:11:11.095798', '2025-04-24 14:11:11.126919', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1915287383067336706, 1915287382949896194, 1909787651972665345, 'cs', 1915274372743176194, 22.00, 123.00, 0.01, NULL, NULL, '2025-04-24 14:11:11.099989', '2025-04-24 14:11:11.128016', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1915287383084113922, 1915287382949896194, 1909794432769794050, 'cs2', 1915274372743176194, 5.00, 11.00, 0.01, NULL, NULL, '2025-04-24 14:11:11.104425', '2025-04-24 14:11:11.128881', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915289245422198786, 1915289245321535489, 4, '石灰石 D', 1915289234298904578, 90.00, 95.00, 0.30, NULL, NULL, '2025-04-24 14:18:35.119534', '2025-04-24 14:18:35.151938', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1915289245438976002, 1915289245321535489, 1909787651972665345, 'cs', 1915289234298904578, 22.00, 123.00, 0.30, NULL, NULL, '2025-04-24 14:18:35.123886', '2025-04-24 14:18:35.153458', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1915289245455753218, 1915289245321535489, 1909794432769794050, 'cs2', 1915289234298904578, 5.00, 11.00, 0.30, NULL, NULL, '2025-04-24 14:18:35.127765', '2025-04-24 14:18:35.154624', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915301218331402242, 1915301218239127553, 4, '石灰石 D', 1915299550910877697, 90.00, 95.00, 0.01, NULL, NULL, '2025-04-24 15:06:09.684455', '2025-04-24 15:06:09.71274', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1915301218348179457, 1915301218239127553, 1909787651972665345, 'cs', 1915299550910877697, 22.00, 123.00, 0.01, NULL, NULL, '2025-04-24 15:06:09.688142', '2025-04-24 15:06:09.714115', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1915301218364956674, 1915301218239127553, 1909794432769794050, 'cs2', 1915299550910877697, 5.00, 11.00, 0.01, NULL, NULL, '2025-04-24 15:06:09.691702', '2025-04-24 15:06:09.715035', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915305583549083649, 1915305580722122754, 4, '石灰石 D', 1915305495665831938, 90.00, 95.00, 3.00, NULL, NULL, '2025-04-24 15:23:30.43937', '2025-04-24 15:23:32.130244', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1915305584987729921, 1915305580722122754, 5, '硅铁 E', 1915305495665831938, 75.00, 85.00, 3.00, NULL, NULL, '2025-04-24 15:23:30.787516', '2025-04-24 15:23:32.236979', 1078.6, '1078.599');
INSERT INTO "public"."calculation_result" VALUES (1915305809735327746, 1915305809626275842, 4, '石灰石 D', 1915305642516815874, 90.00, 95.00, 0.01, NULL, NULL, '2025-04-24 15:24:24.360341', '2025-04-24 15:24:24.391966', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1915305809752104961, 1915305809626275842, 1909787651972665345, 'cs', 1915305642516815874, 22.00, 123.00, 0.01, NULL, NULL, '2025-04-24 15:24:24.364293', '2025-04-24 15:24:24.393177', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1915305809768882178, 1915305809626275842, 1909794432769794050, 'cs2', 1915305642516815874, 5.00, 11.00, 0.01, NULL, NULL, '2025-04-24 15:24:24.367939', '2025-04-24 15:24:24.393976', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915309030340042754, 1915309030243573761, 4, '石灰石 D', 1915308794888593410, 90.00, 95.00, 0.01, NULL, NULL, '2025-04-24 15:37:12.211889', '2025-04-24 15:37:12.234732', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1915309030352625665, 1915309030243573761, 1909787651972665345, 'cs', 1915308794888593410, 22.00, 123.00, 0.01, NULL, NULL, '2025-04-24 15:37:12.215489', '2025-04-24 15:37:12.235729', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1915309030369402881, 1915309030243573761, 1909794432769794050, 'cs2', 1915308794888593410, 5.00, 11.00, 0.01, NULL, NULL, '2025-04-24 15:37:12.21871', '2025-04-24 15:37:12.236433', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915309568490217474, 1915309568393748482, 4, '石灰石 D', 1915308794888593410, 90.00, 95.00, 0.01, NULL, NULL, '2025-04-24 15:39:20.516786', '2025-04-24 15:39:20.54306', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1915309568506994690, 1915309568393748482, 1909787651972665345, 'cs', 1915308794888593410, 22.00, 123.00, 0.01, NULL, NULL, '2025-04-24 15:39:20.52085', '2025-04-24 15:39:20.544205', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1915309568519577602, 1915309568393748482, 1909794432769794050, 'cs2', 1915308794888593410, 5.00, 11.00, 0.01, NULL, NULL, '2025-04-24 15:39:20.524435', '2025-04-24 15:39:20.544921', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915309729710870530, 1915309729375326209, 4, '石灰石 D', 1915308794888593410, 90.00, 95.00, 0.01, NULL, NULL, '2025-04-24 15:39:58.954411', '2025-04-24 15:39:59.490671', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1915309729748619265, 1915309729375326209, 1909787651972665345, 'cs', 1915308794888593410, 22.00, 123.00, 0.01, NULL, NULL, '2025-04-24 15:39:58.963976', '2025-04-24 15:39:59.492684', 1047.32, '1047.317');
INSERT INTO "public"."calculation_result" VALUES (1915309729798950913, 1915309729375326209, 1909794432769794050, 'cs2', 1915308794888593410, 5.00, 11.00, 0.01, NULL, NULL, '2025-04-24 15:39:58.975125', '2025-04-24 15:39:59.495081', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915309964159881217, 1915309963987914754, 4, '石灰石 D', 1915308794888593410, 90.00, 95.00, 0.01, NULL, NULL, '2025-04-24 15:40:54.851146', '2025-04-24 15:40:54.899378', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1915309964185047041, 1915309963987914754, 5, '硅铁 E', 1915308794892787713, 75.00, 85.00, 0.20, NULL, NULL, '2025-04-24 15:40:54.857456', '2025-04-24 15:40:54.900768', 1078.6, '1078.599');
INSERT INTO "public"."calculation_result" VALUES (1915310061048303617, 1915310060893114369, 4, '石灰石 D', 1915308794888593410, 90.00, 95.00, 0.01, NULL, NULL, '2025-04-24 15:41:17.951174', '2025-04-24 15:41:18.003607', 80, '80.0');
INSERT INTO "public"."calculation_result" VALUES (1915310061060886530, 1915310060893114369, 5, '硅铁 E', 1915308794892787713, 75.00, 85.00, 0.20, NULL, NULL, '2025-04-24 15:41:17.9561', '2025-04-24 15:41:18.005414', 984.753, '984.753');
INSERT INTO "public"."calculation_result" VALUES (1915310061077663746, 1915310060893114369, 6, '锰铁 F', 1915308794901176322, 65.00, 82.00, 0.70, NULL, NULL, '2025-04-24 15:41:17.96027', '2025-04-24 15:41:18.007047', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915310061090246657, 1915310060893114369, 7, '镍板 G', 1915308794905370625, 99.00, 98.00, 1.55, NULL, NULL, '2025-04-24 15:41:17.965001', '2025-04-24 15:41:18.008411', 27.735, '27.735');
INSERT INTO "public"."calculation_result" VALUES (1915310061127995394, 1915310060893114369, 14, '锡精矿 N', 1915308794888593410, 30.00, 86.00, 0.01, NULL, NULL, '2025-04-24 15:41:17.971192', '2025-04-24 15:41:18.010165', 27.735, '27.735');

-- ----------------------------
-- Table structure for chemical_elements
-- ----------------------------
DROP TABLE IF EXISTS "public"."chemical_elements";
CREATE TABLE "public"."chemical_elements" (
  "id" int8 NOT NULL,
  "element_name" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "element_symbol" varchar(5) COLLATE "pg_catalog"."default" NOT NULL,
  "createtime" timestamp(6) DEFAULT now(),
  "updatetime" timestamp(6) DEFAULT now()
)
;
COMMENT ON COLUMN "public"."chemical_elements"."element_name" IS '元素名称';
COMMENT ON COLUMN "public"."chemical_elements"."element_symbol" IS '元素符号';
COMMENT ON TABLE "public"."chemical_elements" IS '化学元素表';

-- ----------------------------
-- Records of chemical_elements
-- ----------------------------
INSERT INTO "public"."chemical_elements" VALUES (1, '碳', 'C', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (2, '锰', 'Mn', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (3, '硅', 'Si', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (4, '磷', 'P', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (5, '铬', 'Cr', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (6, '钒', 'V', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (7, '钼', 'Mo', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (8, '镍', 'Ni', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (9, '钨', 'W', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (10, '铜', 'Cu', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (11, '钛', 'Ti', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (12, '铌', 'Nb', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (13, '钴', 'Co', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (14, '硫', 'S', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (15, '锡', 'Sn', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (16, '铝', 'Al', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (17, '铁', 'Fe', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (18, '硼', 'B', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (19, '锆', 'Zr', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (20, '镧', 'La', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (21, '铈', 'Ce', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (22, '钙', 'Ca', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (23, '铅', 'Pb', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (24, '铋', 'Bi', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (25, '锑', 'Sb', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (26, '砷', 'As', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (27, '可溶铝', 'Als', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (28, '钽', 'Ta', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (29, '镁', 'Mg', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (30, '银', 'Ag', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (31, '汞', 'Hg', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (32, '镉', 'Cd', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (33, '锌', 'Zn', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (34, '碲', 'Te', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (35, '硒', 'Se', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (36, '镨', 'Pr', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (37, '钕', 'Nd', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (38, '钪', 'Sc', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (39, '钇', 'Y', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (40, '铪', 'Hf', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (41, '碳当量', 'Pcm', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (42, '氢', 'H', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (43, '氧', 'O', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');
INSERT INTO "public"."chemical_elements" VALUES (44, '氮', 'N', '2025-04-23 00:59:02.894719', '2025-04-23 00:59:02.894719');

-- ----------------------------
-- Table structure for cost_estimation
-- ----------------------------
DROP TABLE IF EXISTS "public"."cost_estimation";
CREATE TABLE "public"."cost_estimation" (
  "id" int8 NOT NULL,
  "estimation_no" varchar(255) COLLATE "pg_catalog"."default",
  "user_id" varchar(255) COLLATE "pg_catalog"."default",
  "username" varchar(255) COLLATE "pg_catalog"."default",
  "company_name" varchar(255) COLLATE "pg_catalog"."default",
  "steel_number" varchar(255) COLLATE "pg_catalog"."default",
  "finished_product_specification" varchar(255) COLLATE "pg_catalog"."default",
  "estimated_order_quantity" varchar(255) COLLATE "pg_catalog"."default",
  "quotation_or_cost_estimation" varchar(255) COLLATE "pg_catalog"."default",
  "forged_material" varchar(255) COLLATE "pg_catalog"."default",
  "change_standard_cost" varchar(255) COLLATE "pg_catalog"."default",
  "length_delivery_status" varchar(255) COLLATE "pg_catalog"."default",
  "surface_delivery_status" varchar(255) COLLATE "pg_catalog"."default",
  "heat_delivery_status" varchar(255) COLLATE "pg_catalog"."default",
  "technical_standard" varchar(255) COLLATE "pg_catalog"."default",
  "process_route" varchar(255) COLLATE "pg_catalog"."default",
  "approve_status" varchar(255) COLLATE "pg_catalog"."default",
  "sub_status" int4,
  "department_id" varchar(255) COLLATE "pg_catalog"."default",
  "processinstanceid" varchar(255) COLLATE "pg_catalog"."default",
  "created_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6),
  "created_time" timestamp(6),
  "task_id" varchar COLLATE "pg_catalog"."default",
  "num" int4 DEFAULT 1,
  "user_ids" int8
)
;
COMMENT ON COLUMN "public"."cost_estimation"."task_id" IS '任务id';
COMMENT ON COLUMN "public"."cost_estimation"."num" IS '数量';
COMMENT ON COLUMN "public"."cost_estimation"."user_ids" IS '登录用户Id';

-- ----------------------------
-- Records of cost_estimation
-- ----------------------------
INSERT INTO "public"."cost_estimation" VALUES (1911607750866497538, 'CE20250305001', '', '张三', '东北分公司', '45Mn', 'Φ50mm×1000mm', '1000件', '报价', '1', '0', '不定尺', '磨光', '退火', 'GB/T 1234-2023', '下料→锻造→热处理→机加工', '待标准科分发', 0, NULL, NULL, 'admin', 'admin', '2025-04-14 10:29:37', '2025-04-14 10:29:37', NULL, 1, NULL);
INSERT INTO "public"."cost_estimation" VALUES (1911607778662150145, 'CE20250305001', '', '张三', '东北分公司', '45Mn', 'Φ50mm×1000mm', '1000件', '报价', '1', '0', '不定尺', '磨光', '退火', 'GB/T 1234-2023', '下料→锻造→热处理→机加工', '配料室审核', 0, NULL, 'baca174f-18e1-11f0-9d12-a28069ead940', 'admin', 'admin', '2025-04-14 11:39:00', '2025-04-14 10:29:44', '1911607778662150145', 1, NULL);
INSERT INTO "public"."cost_estimation" VALUES (1900004409594056708, NULL, NULL, '张三', '东北分公司', '45Mn', '100*50', '100', '报价', '是', '是', '不定尺', '磨光', '退火', 'GB/T 1234-2023', '下料→锻造→热处理→机加工', '配料室审核', 1, '1', '4aded88f-1a8b-11f0-b6b5-6678af5c726d', NULL, NULL, '2025-04-16 14:32:58', '2025-04-16 14:23:20', NULL, 1, NULL);
INSERT INTO "public"."cost_estimation" VALUES (1913030053730725890, 'Cost-1744884398446-4898', '1', '11111', '新公司', '45Mn', '111', '11', '报价', '否', '是', '不定尺', '磨光', '退火', '11', '111', '配料室审核', 0, '6', 'a6f2e3c5-1b73-11f0-8dab-6678af5c726d', NULL, NULL, '2025-04-21 15:49:53.941936', '2025-04-18 08:41:21', 'a6fc31a1-1b73-11f0-8dab-6678af5c726d', 2, NULL);
INSERT INTO "public"."cost_estimation" VALUES (1912809922400366594, 'Cost-1744884398446-4898', '1', '11111', '新公司', '45Mn', '111', '11', '报价', '否', '是', '不定尺', '磨光', '退火', '11', '111', '配料室审核', 0, '6', 'a6f2e3c5-1b73-11f0-8dab-6678af5c726d', NULL, NULL, '2025-04-21 15:50:47.50094', '2025-04-18 08:41:21', '1912809922400366594', 2, NULL);
INSERT INTO "public"."cost_estimation" VALUES (1911607771900932097, 'CE20250305001', '', '张三', '东北分公司', '45Mn', 'Φ50mm×1000mm', '1000件', '报价', '1', '0', '不定尺', '磨光', '退火', 'GB/T 1234-2023', '下料→锻造→热处理→机加工', '待标准科分发', 0, NULL, NULL, 'admin', 'admin', '2025-04-15 23:47:22', NULL, NULL, 1, NULL);
INSERT INTO "public"."cost_estimation" VALUES (1914586817693499394, 'Cost-1745308043265-7828', '1', '张三', '东北分公司', 'SPHC', '11', '11', '报价', '是', '是', '不定尺', '磨光', '退火', '11', '11', '技术中心高强钢室', 0, '12', '06fa6b1e-1f4e-11f0-9219-6678af5c726d', NULL, NULL, '2025-04-22 16:21:14.452463', '2025-04-22 15:48:12', '1914586817693499394', 2, NULL);
INSERT INTO "public"."cost_estimation" VALUES (1914201862547214337, 'Cost-1745216262814-1154', '1', '李四', '分公司', '45Mn', '11', '1111', '报价', '是', '是', '不定尺', '磨光1', '退火', '111', '11', '配料室审核', 0, '16', '5590156b-1e78-11f0-8774-6678af5c726d', NULL, NULL, '2025-04-22 15:45:40.477728', '2025-04-22 15:45:40.477721', '1914201862547214337', 1, NULL);
INSERT INTO "public"."cost_estimation" VALUES (1914600318227013634, 'Cost-1745311261983-4193', '1', '印度采埃孚风电公司', '国贸公司(抚顺)', '18CrNiMo7-6-HH', 'J612-286001，见图纸', '50件', '否', '否', '是', '定尺', '车光或削皮', '调质', 'F873-2025，QJ/DT01.13641-2017-B/0', '炼(电炉+LF+VD+模铸)一>锻造3150开坯+1800成材一>锻造新线退火、正火、调质一 >外委锯切、加工(外圆车光、端车、包装)', '技术中心高强钢室科室主任审核', 0, '502', '85819ac9-1f55-11f0-9c3c-6678af5c726d', NULL, NULL, '2025-04-22 17:14:28.895518', '2025-04-22 17:14:28.895512', '1914600318227013634', 1, NULL);
INSERT INTO "public"."cost_estimation" VALUES (1900004409594056706, 'Cost-1744858540735-3297', '1', '张三', '东北分公司', '45Mn', '100*50', '100', '报价', '是', '是', '不定尺', '磨光', '退火', 'GB/T 1234-2023', '下料→锻造→热处理→机加工', '', 1, '1', '4aded88f-1a8b-11f0-b6b5-6678af5c726d', '张三', '张三', '2025-04-18 16:41:20.204976', '2025-04-16 14:23:20', '1912391340898160641', 2, 1);
INSERT INTO "public"."cost_estimation" VALUES (1915221181716615169, 'Cost-1745459287454-6333', '1', '张三', '东北分公司', '45Mn', '44', '22', '测算成本', '否', '否', '不定尺', '磨光', '11', '1', '2', '技术中心高强钢室审核', 0, '12', '2b817cc8-20ae-11f0-8b03-6678af5c726d', NULL, NULL, '2025-04-24 10:04:19.229185', '2025-04-24 09:49:30', '1915221528539418626', 2, 3);
INSERT INTO "public"."cost_estimation" VALUES (1914893868625891329, 'Cost-1745381249930-3530', '1', '周日', '分公司', '45Mn', '11', '111', '报价', '是', '是', '不定尺', '磨光', '退火', '1111', '11', '技术中心高强钢室', 0, '12', '798560a3-1ff8-11f0-a6d2-6678af5c726d', NULL, NULL, '2025-04-23 16:14:06.348699', '2025-04-23 12:09:29', '1914893868625891329', 2, 3);
INSERT INTO "public"."cost_estimation" VALUES (1914239124127420418, 'Cost-1745225146644-7150', '2', '11', '111', 'SPHC', '111', '11', '111', '11', '111', '111', '11', '111', '111', '11', '技术中心', 0, '10', '04bd92d4-1e8d-11f0-812a-6678af5c726d', NULL, NULL, '2025-04-23 08:57:49.031212', '2025-04-22 11:32:40', '1914239124127420418', 2, 11);
INSERT INTO "public"."cost_estimation" VALUES (1914874589234925569, 'Cost-1745376653357-5878', '1', '测试张三', '东北分公司', '45Mn', '11', '11', '111', '是', '111', '111', '111', '111', '111', '111', '技术中心', 0, '10', 'c5c509a1-1fed-11f0-a6d2-6678af5c726d', NULL, NULL, '2025-04-23 10:56:36.76153', '2025-04-23 10:56:36.76151', '1914874589234925569', 1, 11);
INSERT INTO "public"."cost_estimation" VALUES (1914962744017547266, 'Cost-1745397671071-9920', '1', '测试张三', '东北分公司', '45Mn', '111', '111', '报价', '是', '是', '不定尺', '磨光', '3333', '11', '111', '技术中心高温合金二室审核', 0, '13', 'b5510985-201e-11f0-9a75-6678af5c726d', NULL, NULL, '2025-04-23 17:07:11.181539', '2025-04-23 16:41:31', '1914962827836518401', 2, 36);
INSERT INTO "public"."cost_estimation" VALUES (1914240732462649346, 'Cost-1745225530126-7970', '1', '张三', '东北分公司', '45Mn', '2', '2', '报价', '是', '是', '不定尺', '磨光', '退火1', '2', '2', '配料室审核', 0, '12', 'e94a2e01-1e8d-11f0-812a-6678af5c726d', NULL, NULL, '2025-04-23 15:53:46.123204', '2025-04-21 16:55:34', '1914241590877294594', 2, 3);
INSERT INTO "public"."cost_estimation" VALUES (1914925034347237378, 'Cost-1745388680419-2893', '1', '赵五', '分公司', 'SPHC', '11', '111', '报价', '是', '是', '不定尺', '磨光', '退火', '11', '11111', '技术中心高强钢室', 0, '12', 'c66ef3d4-2009-11f0-a6d2-6678af5c726d', NULL, NULL, '2025-04-23 15:05:43.533685', '2025-04-23 15:05:43.533682', '1914925124696739842', 1, 3);
INSERT INTO "public"."cost_estimation" VALUES (1914939585054707714, 'Cost-1745392149579-2260', '1', '李四', '东北分公司', 'SPHC', '11', '1111', '测算成本', '否', '否', '不定尺', '磨光', '退火1', '111', '11', '技术中心高强钢室', 0, '12', 'da37fae8-2011-11f0-a6d2-6678af5c726d', NULL, NULL, '2025-04-23 15:09:44.609001', '2025-04-23 15:09:44.608998', '1914939669486047233', 1, 3);
INSERT INTO "public"."cost_estimation" VALUES (1914974843376619521, 'Cost-1745400555788-1908', '1', '张三', '东北分公司', 'SPHC', '111', '11', '报价', '是', '是', '不定尺', '磨光', '调质', '11', '111', '技术中心高强钢室', 0, '12', '6cbfe707-2025-11f0-8b03-6678af5c726d', NULL, NULL, '2025-04-23 17:30:59.524434', '2025-04-23 17:30:59.524428', '1914975076047245314', 1, 3);
INSERT INTO "public"."cost_estimation" VALUES (1915241342175551490, 'Cost-1745464094054-3293', '1', '张三', '分公司', '111', '111', '111', '报价', '是', '是', '不定尺', '磨光', '退火', '1111', '1111', '技术中心高强钢室', 0, '12', '5c7f98d8-20b9-11f0-9050-6678af5c726d', NULL, NULL, '2025-04-24 11:57:29.711834', '2025-04-24 11:57:29.711801', '1915241436174098433', 1, 3);
INSERT INTO "public"."cost_estimation" VALUES (1914955526379458562, 'Cost-1745395950252-6943', '1', '李四', '东北分公司', '45Mn', '111', '111', '报价', '是', '111', '不定尺', '磨光', '11', '111', '111', '技术中心高强钢室', 0, '12', 'b3a21df6-201a-11f0-ab92-6678af5c726d', NULL, NULL, '2025-04-23 16:15:44.401531', '2025-04-23 16:12:55', '1914955630800850945', 2, 3);
INSERT INTO "public"."cost_estimation" VALUES (1914976236699242497, 'Cost-1745400888005-3695', '1', '赵五', '东北分公司', 'SPHC', '111', '111', '报价', '是', '是', '不定尺', '磨光', '退火1', '11', '111', '技术中心高强钢室审核', 0, '12', '32b9f888-2026-11f0-8b03-6678af5c726d', NULL, NULL, '2025-04-24 09:53:09.266904', '2025-04-23 17:35:08', '1914976321264799746', 2, 3);
INSERT INTO "public"."cost_estimation" VALUES (1915237328008511490, 'Cost-1745463136995-9360', '1', '李四', '新公司1', '18CrNiMo7-6-HH', '1', '2', '测算成本', '是', '否', '不定尺', '车光或削皮', '调质', '2', '3', '技术中心高强钢室', 0, '12', '220c7190-20b7-11f0-8ad0-6678af5c726d', NULL, NULL, '2025-04-24 10:54:39.181561', '2025-04-24 10:52:45', '1915237445759401986', 2, 3);
INSERT INTO "public"."cost_estimation" VALUES (1915274126034288641, 'Cost-1745471910337-7145', '1', '张三', '东北分公司', '18CrNiMo7-6-HH', '11', '11', '测算成本', '否', '否', '不定尺', '磨光', '退火', '11', '111', '技术中心高强钢室', 0, '12', '8f5c0e2d-20cb-11f0-9060-6678af5c726d', NULL, NULL, '2025-04-24 14:10:54.348565', '2025-04-24 13:18:53', '1915274223786737666', 2, 3);
INSERT INTO "public"."cost_estimation" VALUES (1915288927431114754, 'Cost-1745475439288-1503', '1', '李四', '东北分公司', '18CrNiMo7-6+HH', '11', '111', '报价', '是', '是', '不定尺', '磨光1', '退火', '111', '111', '已驳回', 0, '12', 'c6c2f900-20d3-11f0-9060-6678af5c726d', NULL, NULL, '2025-04-24 16:25:29.343054', '2025-04-24 14:17:59', '1915288927431114754', 2, 3);
INSERT INTO "public"."cost_estimation" VALUES (1915300609339437057, 'Cost-1745478224452-7922', '1', '印度采埃孚风电公司', '国贸公司(抚顺)', '18CrNiMo7-6+HH', '1', '11', '报价', '是', '是', '不定尺', '磨光', '退火', '111', '1111', '技术中心高强钢室', 0, '12', '42dd15bb-20da-11f0-8527-6678af5c726d', NULL, NULL, '2025-04-24 15:05:56.13991', '2025-04-24 15:05:56.139905', '1915300839980019714', 1, 3);
INSERT INTO "public"."cost_estimation" VALUES (1915300943457693697, 'Cost-1745479225349-2161', '1', '印度采埃孚风电公司', '东北分公司', '18CrNiMo7-6+HH', 'φ100', '1000t', '报价', '否', '是', '不定尺', '车光或削皮', '退火', 'F873-2025，QJ/DT01.13641-2017-B/0', '一炼(电炉+LF+VD+模铸)→锻造3150开坯+1800成材→锻造新线退火、正火、调质→外委锯切、加工(外圆车光、端车、包装)', '技术中心高强钢室', 0, '12', '976b7073-20dc-11f0-8527-6678af5c726d', NULL, NULL, '2025-04-24 15:23:07.49166', '2025-04-24 15:23:07.491655', '1915305374882476033', 1, 3);
INSERT INTO "public"."cost_estimation" VALUES (1915302284649312258, 'Cost-1745478884144-5677', '1', '张三', '东北分公司', '45Mn', '111', '111', '报价', '是', '是', '不定尺', '磨光', '退火', '111', '111', '技术中心高强钢室', 0, '12', 'cc0c2531-20db-11f0-8527-6678af5c726d', NULL, NULL, '2025-04-24 17:35:08.222235', '2025-04-24 15:18:03', '1915304213936877570', 2, 3);
INSERT INTO "public"."cost_estimation" VALUES (1915303882943377409, 'Cost-1745479004964-4047', '1', '印度采埃孚风电公司', '东北分公司', '18CrNiMo7-6+HH', 'φ50', '10t', '报价', '是', '是', '不定尺', '车光或削皮', '退火', 'F873-2025，QJ/DT01.13641-2017-B/0', '一炼(电炉+LF+VD+模铸) →锻造3150开坯+1800成材一锻造新线退火、正火、调质→外委锯切、加工(外圆车光、端车、包装)', '技术中心', 0, '10', '140e70ee-20dc-11f0-8527-6678af5c726d', NULL, NULL, '2025-04-24 15:51:24.342787', '2025-04-24 15:51:24.34278', '1915304151882149889', 1, NULL);
INSERT INTO "public"."cost_estimation" VALUES (1915312419656445953, 'Cost-1745481047447-1687', '1', '李四', '分公司', '46Mn', '11', '111', '111', '111', '否', '不定尺', '磨光', '退火', '11', '111', '技术中心', 0, '10', 'd57b0e70-20e0-11f0-8527-6678af5c726d', NULL, NULL, '2025-04-24 16:39:20.240248', '2025-04-24 16:39:20.240211', '1915313654803476481', 1, 20);
INSERT INTO "public"."cost_estimation" VALUES (1915307660660715522, 'Cost-1745479922319-4228', '1', '小米公司', '东北分公司', '18CrNiMo7-6+HH', '10000', '1000', '测算成本', '是', '是', '不定尺', '车光或削皮', '调质', '测试', '测试', '已驳回', 0, '10', '36d85119-20de-11f0-8527-6678af5c726d', NULL, NULL, '2025-04-24 16:39:22.530707', '2025-04-24 15:32:26', '1915307833860304897', 2, 20);

-- ----------------------------
-- Table structure for cost_estimation2
-- ----------------------------
DROP TABLE IF EXISTS "public"."cost_estimation2";
CREATE TABLE "public"."cost_estimation2" (
  "id" int4 NOT NULL DEFAULT nextval('cost_estimation_id_seq'::regclass),
  "estimation_no" varchar COLLATE "pg_catalog"."default",
  "username" varchar(32) COLLATE "pg_catalog"."default",
  "company_id" int4,
  "steel_number_id" int4,
  "finished_product_specification" varchar(255) COLLATE "pg_catalog"."default",
  "estimated_order_quantity" varchar(255) COLLATE "pg_catalog"."default",
  "quotation_or_cost_estimation" varchar(32) COLLATE "pg_catalog"."default",
  "forged_material" varchar(32) COLLATE "pg_catalog"."default",
  "change_standard_cost" varchar(32) COLLATE "pg_catalog"."default",
  "length_delivery_status_id" int4,
  "surface_delivery_status_id" int4,
  "heat_delivery_status_id" int4,
  "technical_standard" varchar(255) COLLATE "pg_catalog"."default",
  "process_route" varchar(255) COLLATE "pg_catalog"."default",
  "approve_status" varchar COLLATE "pg_catalog"."default",
  "created_by" varchar COLLATE "pg_catalog"."default",
  "created_time" timestamp(6),
  "updated_by" varchar(32) COLLATE "pg_catalog"."default",
  "updated_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."cost_estimation2"."id" IS '主键';
COMMENT ON COLUMN "public"."cost_estimation2"."estimation_no" IS '评审编号';
COMMENT ON COLUMN "public"."cost_estimation2"."username" IS '用户名称';
COMMENT ON COLUMN "public"."cost_estimation2"."company_id" IS '所属分公司ID';
COMMENT ON COLUMN "public"."cost_estimation2"."steel_number_id" IS '钢号ID';
COMMENT ON COLUMN "public"."cost_estimation2"."finished_product_specification" IS '成品规格';
COMMENT ON COLUMN "public"."cost_estimation2"."estimated_order_quantity" IS '预计订货量';
COMMENT ON COLUMN "public"."cost_estimation2"."quotation_or_cost_estimation" IS '报价或测算成本';
COMMENT ON COLUMN "public"."cost_estimation2"."forged_material" IS '是否锻材';
COMMENT ON COLUMN "public"."cost_estimation2"."change_standard_cost" IS '是否修改标准成本';
COMMENT ON COLUMN "public"."cost_estimation2"."length_delivery_status_id" IS '长度交货状态ID';
COMMENT ON COLUMN "public"."cost_estimation2"."surface_delivery_status_id" IS '表面交货状态ID';
COMMENT ON COLUMN "public"."cost_estimation2"."heat_delivery_status_id" IS '热处理交货状态ID';
COMMENT ON COLUMN "public"."cost_estimation2"."technical_standard" IS '技术标准';
COMMENT ON COLUMN "public"."cost_estimation2"."process_route" IS '工艺路线';
COMMENT ON COLUMN "public"."cost_estimation2"."approve_status" IS '审核状态';
COMMENT ON COLUMN "public"."cost_estimation2"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."cost_estimation2"."created_time" IS '创建时间';
COMMENT ON COLUMN "public"."cost_estimation2"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."cost_estimation2"."updated_time" IS '更新时间';
COMMENT ON TABLE "public"."cost_estimation2" IS '成本测算';

-- ----------------------------
-- Records of cost_estimation2
-- ----------------------------

-- ----------------------------
-- Table structure for departments
-- ----------------------------
DROP TABLE IF EXISTS "public"."departments";
CREATE TABLE "public"."departments" (
  "id" int8 NOT NULL,
  "department_name" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "createuser" varchar(32) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6)
)
;
COMMENT ON COLUMN "public"."departments"."id" IS '主键';
COMMENT ON COLUMN "public"."departments"."department_name" IS '部门名称';
COMMENT ON TABLE "public"."departments" IS '部门表';

-- ----------------------------
-- Records of departments
-- ----------------------------
INSERT INTO "public"."departments" VALUES (1, '1', '1', '2025-04-03 17:22:06', '2025-04-03 17:22:07');
INSERT INTO "public"."departments" VALUES (2, '2', '2', '2025-04-03 17:22:37', '2025-04-03 17:22:39');
INSERT INTO "public"."departments" VALUES (3, '部门名称', '3', '2025-04-07 14:50:43', '2025-04-07 14:50:45');
INSERT INTO "public"."departments" VALUES (11, '烧结厂', '王工', '2024-01-01 08:30:00', '2024-01-01 08:30:00');
INSERT INTO "public"."departments" VALUES (12, '炼铁分厂', '李工', '2024-01-01 09:00:00', '2024-01-01 09:00:00');
INSERT INTO "public"."departments" VALUES (13, '炼钢车间', '张工', '2024-01-01 09:30:00', '2024-01-01 09:30:00');
INSERT INTO "public"."departments" VALUES (4, '轧钢事业部', '陈工', '2024-01-01 10:00:00', '2024-01-01 10:00:00');
INSERT INTO "public"."departments" VALUES (5, '技术研发中心', '赵工', '2024-01-01 10:30:00', '2024-01-01 10:30:00');
INSERT INTO "public"."departments" VALUES (6, '质量检测部', '周工', '2024-01-01 11:00:00', '2024-01-01 11:00:00');
INSERT INTO "public"."departments" VALUES (7, '设备管理处', '吴工', '2024-01-01 13:30:00', '2024-01-01 13:30:00');
INSERT INTO "public"."departments" VALUES (8, '安全环保部', '郑工', '2024-01-01 14:00:00', '2024-01-01 14:00:00');
INSERT INTO "public"."departments" VALUES (9, '生产调度中心', '王主任', '2024-01-01 14:30:00', '2024-01-01 14:30:00');
INSERT INTO "public"."departments" VALUES (10, '原燃料采购部', '李经理', '2024-01-01 15:00:00', '2024-01-01 15:00:00');

-- ----------------------------
-- Table structure for execution_standard
-- ----------------------------
DROP TABLE IF EXISTS "public"."execution_standard";
CREATE TABLE "public"."execution_standard" (
  "id" int8 NOT NULL,
  "standard_name" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "createuser" varchar(32) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6)
)
;
COMMENT ON COLUMN "public"."execution_standard"."id" IS '主键';
COMMENT ON COLUMN "public"."execution_standard"."standard_name" IS '标准名称';
COMMENT ON TABLE "public"."execution_standard" IS '执行标准';

-- ----------------------------
-- Records of execution_standard
-- ----------------------------
INSERT INTO "public"."execution_standard" VALUES (2, 'F873-2025', '阿水', '2025-04-23 15:34:47', '2025-04-23 15:34:51');
INSERT INTO "public"."execution_standard" VALUES (3, 'QJ/DT01', '阿水', '2025-04-23 15:35:39', '2025-04-23 15:35:40');
INSERT INTO "public"."execution_standard" VALUES (4, '13641-2017-B/O', '阿水', '2025-04-23 15:36:49', '2025-04-23 15:36:52');

-- ----------------------------
-- Table structure for ingot_yield_rates
-- ----------------------------
DROP TABLE IF EXISTS "public"."ingot_yield_rates";
CREATE TABLE "public"."ingot_yield_rates" (
  "id" int8 NOT NULL,
  "process_path" varchar(32) COLLATE "pg_catalog"."default",
  "department_name" varchar(32) COLLATE "pg_catalog"."default",
  "ingot_yield" numeric(5,2),
  "createuser" varchar(32) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6),
  "device" varchar COLLATE "pg_catalog"."default",
  "updateuser" varchar COLLATE "pg_catalog"."default",
  "sort" int4 DEFAULT nextval('sort_seq'::regclass)
)
;
COMMENT ON COLUMN "public"."ingot_yield_rates"."process_path" IS '工序';
COMMENT ON COLUMN "public"."ingot_yield_rates"."department_name" IS '部门名称';
COMMENT ON COLUMN "public"."ingot_yield_rates"."ingot_yield" IS '成锭率';
COMMENT ON COLUMN "public"."ingot_yield_rates"."device" IS '设备';
COMMENT ON COLUMN "public"."ingot_yield_rates"."updateuser" IS '更新人';
COMMENT ON COLUMN "public"."ingot_yield_rates"."sort" IS '排序字段';
COMMENT ON TABLE "public"."ingot_yield_rates" IS '成锭率';

-- ----------------------------
-- Records of ingot_yield_rates
-- ----------------------------

-- ----------------------------
-- Table structure for ingredient_idingot_result
-- ----------------------------
DROP TABLE IF EXISTS "public"."ingredient_idingot_result";
CREATE TABLE "public"."ingredient_idingot_result" (
  "id" int8 NOT NULL,
  "standard_ingredient_record_id" int8 NOT NULL,
  "igingot_id" int8,
  "process_path" varchar(32) COLLATE "pg_catalog"."default",
  "department_name" varchar(32) COLLATE "pg_catalog"."default",
  "ingot_yield" numeric(5,2),
  "sort" int4 DEFAULT nextval('ingredient_idingot_result_sort_seq'::regclass),
  "device" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ingredient_idingot_result"."id" IS '主键';
COMMENT ON COLUMN "public"."ingredient_idingot_result"."standard_ingredient_record_id" IS '配料单ID';
COMMENT ON COLUMN "public"."ingredient_idingot_result"."igingot_id" IS '成锭率表ID';
COMMENT ON COLUMN "public"."ingredient_idingot_result"."process_path" IS '工序';
COMMENT ON COLUMN "public"."ingredient_idingot_result"."department_name" IS '部门名称';
COMMENT ON COLUMN "public"."ingredient_idingot_result"."ingot_yield" IS '成锭率';
COMMENT ON COLUMN "public"."ingredient_idingot_result"."sort" IS '排序字段';
COMMENT ON COLUMN "public"."ingredient_idingot_result"."device" IS '设备';
COMMENT ON TABLE "public"."ingredient_idingot_result" IS '配料单成锭率';

-- ----------------------------
-- Records of ingredient_idingot_result
-- ----------------------------

-- ----------------------------
-- Table structure for ingredient_yield_result
-- ----------------------------
DROP TABLE IF EXISTS "public"."ingredient_yield_result";
CREATE TABLE "public"."ingredient_yield_result" (
  "id" int8 NOT NULL,
  "standard_ingredient_record_id" int8 NOT NULL,
  "yield_id" int8,
  "production_dept" varchar(32) COLLATE "pg_catalog"."default",
  "line_name" varchar(32) COLLATE "pg_catalog"."default",
  "material_yield" varchar(32) COLLATE "pg_catalog"."default",
  "sort" int4 DEFAULT nextval('ingredient_yield_result_sort_seq'::regclass),
  "device" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ingredient_yield_result"."id" IS '主键';
COMMENT ON COLUMN "public"."ingredient_yield_result"."standard_ingredient_record_id" IS '配料单ID';
COMMENT ON COLUMN "public"."ingredient_yield_result"."yield_id" IS '成材率ID';
COMMENT ON COLUMN "public"."ingredient_yield_result"."production_dept" IS '生产车间';
COMMENT ON COLUMN "public"."ingredient_yield_result"."line_name" IS '作业线';
COMMENT ON COLUMN "public"."ingredient_yield_result"."material_yield" IS '成材率';
COMMENT ON COLUMN "public"."ingredient_yield_result"."sort" IS '排序字段';
COMMENT ON COLUMN "public"."ingredient_yield_result"."device" IS '设备';
COMMENT ON TABLE "public"."ingredient_yield_result" IS '配料单成材率';

-- ----------------------------
-- Records of ingredient_yield_result
-- ----------------------------

-- ----------------------------
-- Table structure for material_elements
-- ----------------------------
DROP TABLE IF EXISTS "public"."material_elements";
CREATE TABLE "public"."material_elements" (
  "id" int8 NOT NULL,
  "material_id" int8 NOT NULL,
  "element" varchar(5) COLLATE "pg_catalog"."default",
  "composition" numeric(5,2),
  "sort" int2,
  "createuser" varchar(32) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6)
)
;
COMMENT ON COLUMN "public"."material_elements"."id" IS '主键ID';
COMMENT ON COLUMN "public"."material_elements"."material_id" IS '原料ID';
COMMENT ON COLUMN "public"."material_elements"."element" IS '元素名称';
COMMENT ON COLUMN "public"."material_elements"."composition" IS '元素含量%';
COMMENT ON COLUMN "public"."material_elements"."sort" IS '元素排序';
COMMENT ON TABLE "public"."material_elements" IS '原料元素表';

-- ----------------------------
-- Records of material_elements
-- ----------------------------
INSERT INTO "public"."material_elements" VALUES (1, 1, 'Cr', 65.00, 1, 'admin', '2025-04-21 09:26:11.76621', '2025-04-21 09:26:11.76621');
INSERT INTO "public"."material_elements" VALUES (2, 1, 'C', 7.50, 2, 'admin', '2025-04-21 09:26:11.76621', '2025-04-21 09:26:11.76621');
INSERT INTO "public"."material_elements" VALUES (3, 1, 'Si', 1.50, 3, 'admin', '2025-04-21 09:26:11.76621', '2025-04-21 09:26:11.76621');
INSERT INTO "public"."material_elements" VALUES (4, 2, 'Si', 75.00, 1, 'admin', '2025-04-21 09:26:11.76621', '2025-04-21 09:26:11.76621');
INSERT INTO "public"."material_elements" VALUES (5, 2, 'Fe', 23.00, 2, 'admin', '2025-04-21 09:26:11.76621', '2025-04-21 09:26:11.76621');
INSERT INTO "public"."material_elements" VALUES (6, 2, 'Al', 2.00, 3, 'admin', '2025-04-21 09:26:11.76621', '2025-04-21 09:26:11.76621');
INSERT INTO "public"."material_elements" VALUES (7, 3, 'Mn', 78.00, 1, 'admin', '2025-04-21 09:26:11.76621', '2025-04-21 09:26:11.76621');
INSERT INTO "public"."material_elements" VALUES (8, 3, 'C', 7.00, 2, 'admin', '2025-04-21 09:26:11.76621', '2025-04-21 09:26:11.76621');
INSERT INTO "public"."material_elements" VALUES (9, 3, 'Si', 3.00, 3, 'admin', '2025-04-21 09:26:11.76621', '2025-04-21 09:26:11.76621');
INSERT INTO "public"."material_elements" VALUES (10, 4, 'Mo', 65.00, 1, 'admin', '2025-04-21 09:26:11.76621', '2025-04-21 09:26:11.76621');
INSERT INTO "public"."material_elements" VALUES (11, 4, 'Fe', 34.00, 2, 'admin', '2025-04-21 09:26:11.76621', '2025-04-21 09:26:11.76621');
INSERT INTO "public"."material_elements" VALUES (12, 4, 'Si', 1.00, 3, 'admin', '2025-04-21 09:26:11.76621', '2025-04-21 09:26:11.76621');
INSERT INTO "public"."material_elements" VALUES (13, 5, 'V', 50.00, 1, 'admin', '2025-04-21 09:26:11.76621', '2025-04-21 09:26:11.76621');
INSERT INTO "public"."material_elements" VALUES (14, 5, 'Fe', 48.00, 2, 'admin', '2025-04-21 09:26:11.76621', '2025-04-21 09:26:11.76621');
INSERT INTO "public"."material_elements" VALUES (15, 5, 'Al', 2.00, 3, 'admin', '2025-04-21 09:26:11.76621', '2025-04-21 09:26:11.76621');

-- ----------------------------
-- Table structure for material_yield_rates
-- ----------------------------
DROP TABLE IF EXISTS "public"."material_yield_rates";
CREATE TABLE "public"."material_yield_rates" (
  "id" int8 NOT NULL,
  "production_dept" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "line_name" varchar(32) COLLATE "pg_catalog"."default",
  "material_category_name" varchar(32) COLLATE "pg_catalog"."default",
  "product_category" varchar(32) COLLATE "pg_catalog"."default",
  "steel_class" varchar(32) COLLATE "pg_catalog"."default",
  "steel_grade" varchar(32) COLLATE "pg_catalog"."default",
  "specifications" varchar(32) COLLATE "pg_catalog"."default",
  "material_yield" varchar(32) COLLATE "pg_catalog"."default",
  "unfixed_length_black_skin" varchar(32) COLLATE "pg_catalog"."default",
  "unfixed_yield_polished" varchar(32) COLLATE "pg_catalog"."default",
  "unfixed_yield_peeling" varchar(32) COLLATE "pg_catalog"."default",
  "fixed_yield_polished" varchar(32) COLLATE "pg_catalog"."default",
  "fixed_yield_lathe" varchar(32) COLLATE "pg_catalog"."default",
  "fixed_yield_peeling" varchar(32) COLLATE "pg_catalog"."default",
  "unfixed_polished_lathe" varchar(32) COLLATE "pg_catalog"."default",
  "unfixed_polished_peeling" varchar(32) COLLATE "pg_catalog"."default",
  "fixed_polished_lathe" varchar(32) COLLATE "pg_catalog"."default",
  "fixed_polished_peeling" varchar(32) COLLATE "pg_catalog"."default",
  "fixed_length_rate" varchar(32) COLLATE "pg_catalog"."default",
  "polished_rate" varchar(32) COLLATE "pg_catalog"."default",
  "lathe_rate" varchar(32) COLLATE "pg_catalog"."default",
  "peeling_rate" varchar(32) COLLATE "pg_catalog"."default",
  "burning_loss" varchar(32) COLLATE "pg_catalog"."default",
  "device" varchar(32) COLLATE "pg_catalog"."default",
  "sort" int4 DEFAULT nextval('material_yield_rates_sort_seq'::regclass)
)
;
COMMENT ON COLUMN "public"."material_yield_rates"."id" IS 'ID';
COMMENT ON COLUMN "public"."material_yield_rates"."production_dept" IS '生产车间';
COMMENT ON COLUMN "public"."material_yield_rates"."line_name" IS '作业线';
COMMENT ON COLUMN "public"."material_yield_rates"."material_category_name" IS '供料类别';
COMMENT ON COLUMN "public"."material_yield_rates"."product_category" IS '产品类别';
COMMENT ON COLUMN "public"."material_yield_rates"."steel_class" IS '钢类';
COMMENT ON COLUMN "public"."material_yield_rates"."steel_grade" IS '钢号';
COMMENT ON COLUMN "public"."material_yield_rates"."specifications" IS '规格';
COMMENT ON COLUMN "public"."material_yield_rates"."material_yield" IS '成材率';
COMMENT ON COLUMN "public"."material_yield_rates"."unfixed_length_black_skin" IS '不定尺（黑皮）';
COMMENT ON COLUMN "public"."material_yield_rates"."unfixed_yield_polished" IS '不定尺成材率（磨光）';
COMMENT ON COLUMN "public"."material_yield_rates"."unfixed_yield_peeling" IS '不定尺成材率（削皮）';
COMMENT ON COLUMN "public"."material_yield_rates"."fixed_yield_polished" IS '成材率（定尺+磨光）';
COMMENT ON COLUMN "public"."material_yield_rates"."fixed_yield_lathe" IS '成材率（定尺+车光）';
COMMENT ON COLUMN "public"."material_yield_rates"."fixed_yield_peeling" IS '成材率（定尺+削皮）';
COMMENT ON COLUMN "public"."material_yield_rates"."unfixed_polished_lathe" IS '不定尺磨光车光';
COMMENT ON COLUMN "public"."material_yield_rates"."unfixed_polished_peeling" IS '不定尺磨光削皮';
COMMENT ON COLUMN "public"."material_yield_rates"."fixed_polished_lathe" IS '定尺磨光车光';
COMMENT ON COLUMN "public"."material_yield_rates"."fixed_polished_peeling" IS '定尺磨光削皮';
COMMENT ON COLUMN "public"."material_yield_rates"."fixed_length_rate" IS '定尺率';
COMMENT ON COLUMN "public"."material_yield_rates"."polished_rate" IS '磨光率';
COMMENT ON COLUMN "public"."material_yield_rates"."lathe_rate" IS '车光率';
COMMENT ON COLUMN "public"."material_yield_rates"."peeling_rate" IS '削皮率';
COMMENT ON COLUMN "public"."material_yield_rates"."burning_loss" IS '烧损';
COMMENT ON COLUMN "public"."material_yield_rates"."device" IS '生产设备';
COMMENT ON COLUMN "public"."material_yield_rates"."sort" IS '排序字段';
COMMENT ON TABLE "public"."material_yield_rates" IS '成材率';

-- ----------------------------
-- Records of material_yield_rates
-- ----------------------------

-- ----------------------------
-- Table structure for path_materials
-- ----------------------------
DROP TABLE IF EXISTS "public"."path_materials";
CREATE TABLE "public"."path_materials" (
  "path_id" int8 NOT NULL,
  "raw_material_id" int8 NOT NULL
)
;
COMMENT ON COLUMN "public"."path_materials"."path_id" IS '路径ID';
COMMENT ON COLUMN "public"."path_materials"."raw_material_id" IS '材料ID';
COMMENT ON TABLE "public"."path_materials" IS '工艺路径材料关联表';

-- ----------------------------
-- Records of path_materials
-- ----------------------------
INSERT INTO "public"."path_materials" VALUES (1, 5);
INSERT INTO "public"."path_materials" VALUES (1, 6);
INSERT INTO "public"."path_materials" VALUES (1, 8);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 4);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 5);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 6);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 7);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 8);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 9);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 10);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 11);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 12);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 13);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 14);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 1);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 2);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 3);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 1909794432769794050);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 1909788114226909185);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 15);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 1909787651972665345);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 1909447077478428673);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 1909787950200262658);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 1909447281355157506);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 16);
INSERT INTO "public"."path_materials" VALUES (1912676551003115500, 0);
INSERT INTO "public"."path_materials" VALUES (1914933981380501506, 4);
INSERT INTO "public"."path_materials" VALUES (1914933981380501506, 5);
INSERT INTO "public"."path_materials" VALUES (1914933981380501506, 6);
INSERT INTO "public"."path_materials" VALUES (1914933981380501506, 9);
INSERT INTO "public"."path_materials" VALUES (1914933981380501506, 1909794432769794050);
INSERT INTO "public"."path_materials" VALUES (1914933981380501506, 1909787651972665345);
INSERT INTO "public"."path_materials" VALUES (1915239000382275586, 12);
INSERT INTO "public"."path_materials" VALUES (1915239000382275586, 13);
INSERT INTO "public"."path_materials" VALUES (1915239000382275586, 14);
INSERT INTO "public"."path_materials" VALUES (1915239000382275586, 15);
INSERT INTO "public"."path_materials" VALUES (1915239000382275586, 16);
INSERT INTO "public"."path_materials" VALUES (1915239000382275586, 0);
INSERT INTO "public"."path_materials" VALUES (1915237675292754000, 4);
INSERT INTO "public"."path_materials" VALUES (1915237675292754000, 1909787651972665345);
INSERT INTO "public"."path_materials" VALUES (1915237675292754000, 1909794432769794050);
INSERT INTO "public"."path_materials" VALUES (1915237675292753922, 4);
INSERT INTO "public"."path_materials" VALUES (1915237675292753922, 5);
INSERT INTO "public"."path_materials" VALUES (1915237675292753922, 6);
INSERT INTO "public"."path_materials" VALUES (1915237675292753922, 7);
INSERT INTO "public"."path_materials" VALUES (1915237675292753922, 14);

-- ----------------------------
-- Table structure for process_path
-- ----------------------------
DROP TABLE IF EXISTS "public"."process_path";
CREATE TABLE "public"."process_path" (
  "id" int8 NOT NULL,
  "path_name" varchar(255) COLLATE "pg_catalog"."default",
  "purpose" varchar(255) COLLATE "pg_catalog"."default",
  "note" varchar(255) COLLATE "pg_catalog"."default",
  "frequence" int4,
  "material_yield" int8,
  "createuser" varchar(32) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6),
  "steel_grades_id" int4
)
;
COMMENT ON COLUMN "public"."process_path"."id" IS '主键';
COMMENT ON COLUMN "public"."process_path"."path_name" IS '路径名称';
COMMENT ON COLUMN "public"."process_path"."purpose" IS '用途描述';
COMMENT ON COLUMN "public"."process_path"."note" IS '备注';
COMMENT ON COLUMN "public"."process_path"."frequence" IS '使用频次';
COMMENT ON COLUMN "public"."process_path"."material_yield" IS '成材率id';
COMMENT ON COLUMN "public"."process_path"."steel_grades_id" IS '钢种ID';
COMMENT ON TABLE "public"."process_path" IS '工艺路径表';

-- ----------------------------
-- Records of process_path
-- ----------------------------
INSERT INTO "public"."process_path" VALUES (1915237675292753922, '22', '33', '44', NULL, NULL, NULL, '2025-04-24 10:53:39.843302', '2025-04-24 10:53:39.843361', 16);
INSERT INTO "public"."process_path" VALUES (1915237827369828354, '2', '3', '4', NULL, NULL, NULL, '2025-04-24 10:54:16.101017', '2025-04-24 10:54:16.101067', 16);
INSERT INTO "public"."process_path" VALUES (1915238496239517697, '测试路径', '测试用途', '测试备注', NULL, NULL, NULL, '2025-04-24 10:56:55.572454', '2025-04-24 10:56:55.573452', 16);
INSERT INTO "public"."process_path" VALUES (1915239000382275586, '测试路径', '测试用途', '测试备注', NULL, NULL, NULL, '2025-04-24 10:58:55.772159', '2025-04-24 10:58:55.772159', 16);

-- ----------------------------
-- Table structure for process_path_steps
-- ----------------------------
DROP TABLE IF EXISTS "public"."process_path_steps";
CREATE TABLE "public"."process_path_steps" (
  "id" int8 NOT NULL,
  "step_number" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "equipment_id" int8,
  "description" varchar(255) COLLATE "pg_catalog"."default",
  "path_id" int8 NOT NULL,
  "createtime" timestamp(6),
  "updatetime" timestamp(6),
  "type" int4
)
;
COMMENT ON COLUMN "public"."process_path_steps"."id" IS '主键';
COMMENT ON COLUMN "public"."process_path_steps"."step_number" IS '步骤编号';
COMMENT ON COLUMN "public"."process_path_steps"."equipment_id" IS '设备ID';
COMMENT ON COLUMN "public"."process_path_steps"."description" IS '描述';
COMMENT ON COLUMN "public"."process_path_steps"."path_id" IS '关联的工艺路径ID';
COMMENT ON COLUMN "public"."process_path_steps"."type" IS '工序类别，1代表设备，2代表模板';
COMMENT ON TABLE "public"."process_path_steps" IS '工序表';

-- ----------------------------
-- Records of process_path_steps
-- ----------------------------
INSERT INTO "public"."process_path_steps" VALUES (1915237675355668482, '1', 2, NULL, 1915237675292753922, '2025-04-24 10:53:39.857674', '2025-04-24 10:53:39.857718', 1);
INSERT INTO "public"."process_path_steps" VALUES (1915237827378216961, '1', 2, NULL, 1915237827369828354, '2025-04-24 10:54:16.102312', '2025-04-24 10:54:16.102348', 1);
INSERT INTO "public"."process_path_steps" VALUES (1915237827378216962, '2', 1, NULL, 1915237827369828354, '2025-04-24 10:54:16.102699', '2025-04-24 10:54:16.102727', 1);
INSERT INTO "public"."process_path_steps" VALUES (1915238496851886081, '1', 1, NULL, 1915238496239517697, '2025-04-24 10:56:55.729719', '2025-04-24 10:56:55.729719', 1);
INSERT INTO "public"."process_path_steps" VALUES (1915238496986103809, '2', 2, NULL, 1915238496239517697, '2025-04-24 10:56:55.749435', '2025-04-24 10:56:55.749435', 1);
INSERT INTO "public"."process_path_steps" VALUES (1915238496986103810, '3', 3, NULL, 1915238496239517697, '2025-04-24 10:56:55.749435', '2025-04-24 10:56:55.749435', 1);
INSERT INTO "public"."process_path_steps" VALUES (1915238496986103811, '4', 4, NULL, 1915238496239517697, '2025-04-24 10:56:55.749435', '2025-04-24 10:56:55.749435', 1);
INSERT INTO "public"."process_path_steps" VALUES (1915238496986103812, '5', 1913058984915537922, NULL, 1915238496239517697, '2025-04-24 10:56:55.750432', '2025-04-24 10:56:55.750432', 2);
INSERT INTO "public"."process_path_steps" VALUES (1915239000839454721, '1', 1, NULL, 1915239000382275586, '2025-04-24 10:58:55.879819', '2025-04-24 10:58:55.879819', 1);
INSERT INTO "public"."process_path_steps" VALUES (1915239000839454722, '2', 2, NULL, 1915239000382275586, '2025-04-24 10:58:55.880853', '2025-04-24 10:58:55.880853', 1);
INSERT INTO "public"."process_path_steps" VALUES (1915239000839454723, '3', 3, NULL, 1915239000382275586, '2025-04-24 10:58:55.881826', '2025-04-24 10:58:55.881826', 1);
INSERT INTO "public"."process_path_steps" VALUES (1915239000839454724, '4', 4, NULL, 1915239000382275586, '2025-04-24 10:58:55.883808', '2025-04-24 10:58:55.883808', 1);
INSERT INTO "public"."process_path_steps" VALUES (1915239000839454725, '5', 1913068398917709825, NULL, 1915239000382275586, '2025-04-24 10:58:55.884841', '2025-04-24 10:58:55.884841', 2);

-- ----------------------------
-- Table structure for production_equipments
-- ----------------------------
DROP TABLE IF EXISTS "public"."production_equipments";
CREATE TABLE "public"."production_equipments" (
  "id" int8 NOT NULL,
  "equipment_name" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "type" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "images_url" varchar(900) COLLATE "pg_catalog"."default",
  "department_id" int8 NOT NULL,
  "createuser" varchar(32) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6)
)
;
COMMENT ON COLUMN "public"."production_equipments"."id" IS '主键';
COMMENT ON COLUMN "public"."production_equipments"."equipment_name" IS '设备名称';
COMMENT ON COLUMN "public"."production_equipments"."type" IS '设备类型';
COMMENT ON COLUMN "public"."production_equipments"."images_url" IS '图片链接';
COMMENT ON COLUMN "public"."production_equipments"."department_id" IS '关联的厂ID';
COMMENT ON TABLE "public"."production_equipments" IS '设备表';

-- ----------------------------
-- Records of production_equipments
-- ----------------------------
INSERT INTO "public"."production_equipments" VALUES (2, '高sda铬', '2', 'http://************:9021/images/img_1.png', 1, NULL, NULL, NULL);
INSERT INTO "public"."production_equipments" VALUES (7, '起重机', '设备吊运', 'http://************:9021/images/img_1.png', 7, '吴工', '2024-01-01 13:30:00', '2024-01-01 13:30:00');
INSERT INTO "public"."production_equipments" VALUES (14, '轧机', '轧钢设备', 'http://************:9021/images/img_1.png', 14, '陈工', '2024-01-01 10:30:00', '2024-01-01 10:30:00');
INSERT INTO "public"."production_equipments" VALUES (1, '高ddsd', '1', 'http://************:9021/images/img_1.png', 1, NULL, NULL, NULL);
INSERT INTO "public"."production_equipments" VALUES (103, '103', '103', 'http://************:9021/images/img_1.png', 1, '1', '2025-04-03 17:24:27', '2025-04-03 17:24:29');
INSERT INTO "public"."production_equipments" VALUES (101, '101', '101', 'http://************:9021/images/img_1.png', 1, '1', '2025-04-03 17:23:46', '2025-04-03 17:23:47');
INSERT INTO "public"."production_equipments" VALUES (3, '电炉', '3', 'http://************:9021/images/img_1.png', 1, NULL, NULL, NULL);
INSERT INTO "public"."production_equipments" VALUES (6, '光谱分析仪', '检测设备', 'http://************:9021/images/img_1.png', 6, '周工', '2024-01-01 11:30:00', '2024-01-01 11:30:00');
INSERT INTO "public"."production_equipments" VALUES (4, 'VOD品种', '4', 'http://************:9021/images/img_1.png', 1, NULL, NULL, NULL);
INSERT INTO "public"."production_equipments" VALUES (11, '烧结机', '烧结设备', 'http://************:9021/images/img_1.png', 11, '王工', '2024-01-01 09:00:00', '2024-01-01 09:00:00');
INSERT INTO "public"."production_equipments" VALUES (8, '环保监测仪', '环保设备', 'http://************:9021/images/img_1.png', 8, '郑工', '2024-01-01 14:00:00', '2024-01-01 14:00:00');
INSERT INTO "public"."production_equipments" VALUES (13, '转炉', '炼钢设备', 'http://************:9021/images/img_1.png', 13, '张工', '2024-01-01 10:00:00', '2024-01-01 10:00:00');
INSERT INTO "public"."production_equipments" VALUES (9, '调度系统服务器', '调度设备', 'http://************:9021/images/img_1.png', 9, '王主任', '2024-01-01 14:30:00', '2024-01-01 14:30:00');
INSERT INTO "public"."production_equipments" VALUES (12, '高炉', '炼铁设备', 'http://************:9021/images/img_1.png', 12, '李工', '2024-01-01 09:30:00', '2024-01-01 09:30:00');
INSERT INTO "public"."production_equipments" VALUES (15, '实验炉', '研发设备', 'http://************:9021/images/img_1.png', 15, '赵工', '2024-01-01 11:00:00', '2024-01-01 11:00:00');
INSERT INTO "public"."production_equipments" VALUES (10, '矿石装卸机', '采购装卸设备', 'http://************:9021/images/img_1.png', 10, '李经理', '2024-01-01 15:00:00', '2024-01-01 15:00:00');

-- ----------------------------
-- Table structure for production_factory
-- ----------------------------
DROP TABLE IF EXISTS "public"."production_factory";
CREATE TABLE "public"."production_factory" (
  "id" int8 NOT NULL,
  "name" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "images_url" varchar(900) COLLATE "pg_catalog"."default",
  "createuser" varchar(32) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6)
)
;
COMMENT ON COLUMN "public"."production_factory"."id" IS '主键';
COMMENT ON COLUMN "public"."production_factory"."name" IS '厂名称;如冶炼厂、生产厂等';
COMMENT ON COLUMN "public"."production_factory"."images_url" IS '图片链接';
COMMENT ON TABLE "public"."production_factory" IS '厂表';

-- ----------------------------
-- Records of production_factory
-- ----------------------------

-- ----------------------------
-- Table structure for purpose_compositions
-- ----------------------------
DROP TABLE IF EXISTS "public"."purpose_compositions";
CREATE TABLE "public"."purpose_compositions" (
  "id" int8 NOT NULL,
  "standard_ingredient_record_id" int8,
  "element_name" varchar(5) COLLATE "pg_catalog"."default",
  "min_value" numeric(5,2),
  "max_value" numeric(5,2),
  "average_value" numeric(5,2),
  "code" varchar(5) COLLATE "pg_catalog"."default",
  "createuser" varchar(32) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6)
)
;
COMMENT ON COLUMN "public"."purpose_compositions"."id" IS '主键';
COMMENT ON COLUMN "public"."purpose_compositions"."standard_ingredient_record_id" IS '标准配料ID';
COMMENT ON COLUMN "public"."purpose_compositions"."element_name" IS '元素名称';
COMMENT ON COLUMN "public"."purpose_compositions"."min_value" IS '最小值';
COMMENT ON COLUMN "public"."purpose_compositions"."max_value" IS '最大值';
COMMENT ON COLUMN "public"."purpose_compositions"."average_value" IS '平均值（目标值）';
COMMENT ON COLUMN "public"."purpose_compositions"."code" IS '数学符号';
COMMENT ON TABLE "public"."purpose_compositions" IS '目标成分';

-- ----------------------------
-- Records of purpose_compositions
-- ----------------------------
INSERT INTO "public"."purpose_compositions" VALUES (1915237713926488066, NULL, 'Ni', 3.00, 4.00, 3.50, '=', NULL, '2025-04-24 10:53:49.054089', '2025-04-24 10:53:49.054142');
INSERT INTO "public"."purpose_compositions" VALUES (1915289234298904578, 1915289245321535489, 'Mo', 0.25, 0.35, 0.30, '=', NULL, '2025-04-24 14:18:32.468267', '2025-04-24 14:18:35.1116');
INSERT INTO "public"."purpose_compositions" VALUES (1915289234307293185, 1915289245321535489, 'Al', 0.02, 0.04, 0.03, '=', NULL, '2025-04-24 14:18:32.469581', '2025-04-24 14:18:35.113486');
INSERT INTO "public"."purpose_compositions" VALUES (1915289234311487489, 1915289245321535489, 'N', 0.01, 0.02, 0.02, '=', NULL, '2025-04-24 14:18:32.470774', '2025-04-24 14:18:35.115255');
INSERT INTO "public"."purpose_compositions" VALUES (1915286479769444353, 1915286568281841666, 'C', 1.00, 1.00, 1.00, '=', NULL, '2025-04-24 14:07:35.736633', '2025-04-24 14:07:56.842735');
INSERT INTO "public"."purpose_compositions" VALUES (1915239211062165506, 1915241584211980289, 'P', 0.00, 0.02, 0.01, '=', NULL, '2025-04-24 10:59:46.010962', '2025-04-24 11:09:12.054602');
INSERT INTO "public"."purpose_compositions" VALUES (1915239211787780097, 1915241584211980289, 'Si', 0.00, 0.40, 0.20, '=', NULL, '2025-04-24 10:59:46.173386', '2025-04-24 11:09:12.300311');
INSERT INTO "public"."purpose_compositions" VALUES (1915239212450480129, 1915241584211980289, 'Mn', 0.50, 0.90, 0.70, '=', NULL, '2025-04-24 10:59:46.338324', '2025-04-24 11:09:12.54175');
INSERT INTO "public"."purpose_compositions" VALUES (1915239213234814977, 1915241584211980289, 'Ni', 1.40, 1.70, 1.55, '=', NULL, '2025-04-24 10:59:46.528661', '2025-04-24 11:09:12.801968');
INSERT INTO "public"."purpose_compositions" VALUES (1915239214090452993, 1915241584211980289, 'C', 0.15, 0.21, 0.18, '=', NULL, '2025-04-24 10:59:46.72813', '2025-04-24 11:09:13.018387');
INSERT INTO "public"."purpose_compositions" VALUES (1915239214878982145, 1915241584211980289, 'S', 0.00, 0.01, 0.01, '=', NULL, '2025-04-24 10:59:46.910662', '2025-04-24 11:09:13.242401');
INSERT INTO "public"."purpose_compositions" VALUES (1915239215533293570, 1915241584211980289, 'Cr', 1.50, 1.80, 1.65, '=', NULL, '2025-04-24 10:59:47.07794', '2025-04-24 11:09:13.502953');
INSERT INTO "public"."purpose_compositions" VALUES (1915239216254713858, 1915241584211980289, 'Mo', 0.25, 0.35, 0.30, '=', NULL, '2025-04-24 10:59:47.248806', '2025-04-24 11:09:13.722969');
INSERT INTO "public"."purpose_compositions" VALUES (1915239217039048705, 1915241584211980289, 'Al', 0.02, 0.04, 0.03, '=', NULL, '2025-04-24 10:59:47.430213', '2025-04-24 11:09:13.979558');
INSERT INTO "public"."purpose_compositions" VALUES (1915239217823383553, 1915241584211980289, 'N', 0.01, 0.02, 0.02, '=', NULL, '2025-04-24 10:59:47.61775', '2025-04-24 11:09:14.210349');
INSERT INTO "public"."purpose_compositions" VALUES (1915238006756016129, 1915243038301757441, 'C', 3.00, 3.00, 3.00, '=', NULL, '2025-04-24 10:54:58.870153', '2025-04-24 11:14:58.506198');
INSERT INTO "public"."purpose_compositions" VALUES (1915253783382990850, 1915253795978485762, 'P', 0.00, 0.02, 0.01, '=', NULL, '2025-04-24 11:57:40.311051', '2025-04-24 11:57:43.31954');
INSERT INTO "public"."purpose_compositions" VALUES (1915253783399768065, 1915253795978485762, 'Si', 0.00, 0.40, 0.20, '=', NULL, '2025-04-24 11:57:40.31483', '2025-04-24 11:57:43.322755');
INSERT INTO "public"."purpose_compositions" VALUES (1915253783412350977, 1915253795978485762, 'Mn', 0.50, 0.90, 0.70, '=', NULL, '2025-04-24 11:57:40.317876', '2025-04-24 11:57:43.32608');
INSERT INTO "public"."purpose_compositions" VALUES (1915253783424933890, 1915253795978485762, 'Ni', 1.40, 1.70, 1.55, '=', NULL, '2025-04-24 11:57:40.320653', '2025-04-24 11:57:43.329726');
INSERT INTO "public"."purpose_compositions" VALUES (1915253783437516802, 1915253795978485762, 'C', 0.15, 0.21, 0.18, '=', NULL, '2025-04-24 11:57:40.32449', '2025-04-24 11:57:43.334929');
INSERT INTO "public"."purpose_compositions" VALUES (1915253783454294017, 1915253795978485762, 'S', 0.00, 0.01, 0.01, '=', NULL, '2025-04-24 11:57:40.328188', '2025-04-24 11:57:43.339274');
INSERT INTO "public"."purpose_compositions" VALUES (1915253783466876930, 1915253795978485762, 'Cr', 1.50, 1.80, 1.65, '=', NULL, '2025-04-24 11:57:40.33126', '2025-04-24 11:57:43.342444');
INSERT INTO "public"."purpose_compositions" VALUES (1915253783483654145, 1915253795978485762, 'Mo', 0.25, 0.35, 0.30, '=', NULL, '2025-04-24 11:57:40.334512', '2025-04-24 11:57:43.345687');
INSERT INTO "public"."purpose_compositions" VALUES (1915253783492042753, 1915253795978485762, 'Al', 0.02, 0.04, 0.03, '=', NULL, '2025-04-24 11:57:40.33705', '2025-04-24 11:57:43.349425');
INSERT INTO "public"."purpose_compositions" VALUES (1915253783500431362, 1915253795978485762, 'N', 0.01, 0.02, 0.02, '=', NULL, '2025-04-24 11:57:40.339429', '2025-04-24 11:57:43.352358');
INSERT INTO "public"."purpose_compositions" VALUES (1915299632066465793, NULL, 'Mn', 2.00, 4.00, 3.00, '=', NULL, '2025-04-24 14:59:51.490479', '2025-04-24 14:59:51.490479');
INSERT INTO "public"."purpose_compositions" VALUES (1915305642516815874, 1915305809626275842, 'P', 0.00, 0.02, 0.01, '=', NULL, '2025-04-24 15:23:44.492126', '2025-04-24 15:24:24.336322');
INSERT INTO "public"."purpose_compositions" VALUES (1915305642525204482, 1915305809626275842, 'Si', 0.00, 0.40, 0.20, '=', NULL, '2025-04-24 15:23:44.493981', '2025-04-24 15:24:24.338505');
INSERT INTO "public"."purpose_compositions" VALUES (1915299550910877697, 1915301218239127553, 'P', 0.00, 0.02, 0.01, '=', NULL, '2025-04-24 14:59:32.140143', '2025-04-24 15:06:09.664584');
INSERT INTO "public"."purpose_compositions" VALUES (1915299551447748610, 1915301218239127553, 'Si', 0.00, 0.40, 0.20, '=', NULL, '2025-04-24 14:59:32.276079', '2025-04-24 15:06:09.666338');
INSERT INTO "public"."purpose_compositions" VALUES (1915299552164974594, 1915301218239127553, 'Mn', 0.50, 0.90, 0.70, '=', NULL, '2025-04-24 14:59:32.450053', '2025-04-24 15:06:09.668289');
INSERT INTO "public"."purpose_compositions" VALUES (1915299553012224002, 1915301218239127553, 'Ni', 1.40, 1.70, 1.55, '=', NULL, '2025-04-24 14:59:32.647013', '2025-04-24 15:06:09.669996');
INSERT INTO "public"."purpose_compositions" VALUES (1915299553666535425, 1915301218239127553, 'C', 0.15, 0.21, 0.18, '=', NULL, '2025-04-24 14:59:32.807419', '2025-04-24 15:06:09.671743');
INSERT INTO "public"."purpose_compositions" VALUES (1915299554383761409, 1915301218239127553, 'S', 0.00, 0.01, 0.01, '=', NULL, '2025-04-24 14:59:32.969671', '2025-04-24 15:06:09.673504');
INSERT INTO "public"."purpose_compositions" VALUES (1915274372743176194, 1915287382949896194, 'P', 0.00, 0.02, 0.01, '=', NULL, '2025-04-24 13:19:29.196691', '2025-04-24 14:11:11.074747');
INSERT INTO "public"."purpose_compositions" VALUES (1915274372751564801, 1915287382949896194, 'Si', 0.00, 0.40, 0.20, '=', NULL, '2025-04-24 13:19:29.199147', '2025-04-24 14:11:11.077241');
INSERT INTO "public"."purpose_compositions" VALUES (1915274372759953410, 1915287382949896194, 'Mn', 0.50, 0.90, 0.70, '=', NULL, '2025-04-24 13:19:29.20102', '2025-04-24 14:11:11.079093');
INSERT INTO "public"."purpose_compositions" VALUES (1915274372768342017, 1915287382949896194, 'Ni', 1.40, 1.70, 1.55, '=', NULL, '2025-04-24 13:19:29.202791', '2025-04-24 14:11:11.080959');
INSERT INTO "public"."purpose_compositions" VALUES (1915274372776730625, 1915287382949896194, 'C', 0.15, 0.21, 0.18, '=', NULL, '2025-04-24 13:19:29.204642', '2025-04-24 14:11:11.082747');
INSERT INTO "public"."purpose_compositions" VALUES (1915274372780924929, 1915287382949896194, 'S', 0.00, 0.01, 0.01, '=', NULL, '2025-04-24 13:19:29.206416', '2025-04-24 14:11:11.084583');
INSERT INTO "public"."purpose_compositions" VALUES (1915274372789313538, 1915287382949896194, 'Cr', 1.50, 1.80, 1.65, '=', NULL, '2025-04-24 13:19:29.208132', '2025-04-24 14:11:11.086166');
INSERT INTO "public"."purpose_compositions" VALUES (1915274372797702146, 1915287382949896194, 'Mo', 0.25, 0.35, 0.30, '=', NULL, '2025-04-24 13:19:29.209852', '2025-04-24 14:11:11.08825');
INSERT INTO "public"."purpose_compositions" VALUES (1915274372806090754, 1915287382949896194, 'Al', 0.02, 0.04, 0.03, '=', NULL, '2025-04-24 13:19:29.211599', '2025-04-24 14:11:11.089931');
INSERT INTO "public"."purpose_compositions" VALUES (1915274372810285057, 1915287382949896194, 'N', 0.01, 0.02, 0.02, '=', NULL, '2025-04-24 13:19:29.213367', '2025-04-24 14:11:11.091551');
INSERT INTO "public"."purpose_compositions" VALUES (1915289234256961537, 1915289245321535489, 'P', 0.00, 0.02, 0.01, '=', NULL, '2025-04-24 14:18:32.457594', '2025-04-24 14:18:35.098156');
INSERT INTO "public"."purpose_compositions" VALUES (1915289234265350145, 1915289245321535489, 'Si', 0.00, 0.40, 0.20, '=', NULL, '2025-04-24 14:18:32.459867', '2025-04-24 14:18:35.100174');
INSERT INTO "public"."purpose_compositions" VALUES (1915289234269544449, 1915289245321535489, 'Mn', 0.50, 0.90, 0.70, '=', NULL, '2025-04-24 14:18:32.461465', '2025-04-24 14:18:35.102168');
INSERT INTO "public"."purpose_compositions" VALUES (1915289234277933058, 1915289245321535489, 'Ni', 1.40, 1.70, 1.55, '=', NULL, '2025-04-24 14:18:32.46291', '2025-04-24 14:18:35.10401');
INSERT INTO "public"."purpose_compositions" VALUES (1915289234282127361, 1915289245321535489, 'C', 0.15, 0.21, 0.18, '=', NULL, '2025-04-24 14:18:32.464286', '2025-04-24 14:18:35.106066');
INSERT INTO "public"."purpose_compositions" VALUES (1915289234290515969, 1915289245321535489, 'S', 0.00, 0.01, 0.01, '=', NULL, '2025-04-24 14:18:32.465701', '2025-04-24 14:18:35.107883');
INSERT INTO "public"."purpose_compositions" VALUES (1915289234294710274, 1915289245321535489, 'Cr', 1.50, 1.80, 1.65, '=', NULL, '2025-04-24 14:18:32.467011', '2025-04-24 14:18:35.109652');
INSERT INTO "public"."purpose_compositions" VALUES (1915299555029684225, 1915301218239127553, 'Cr', 1.50, 1.80, 1.65, '=', NULL, '2025-04-24 14:59:33.124648', '2025-04-24 15:06:09.67503');
INSERT INTO "public"."purpose_compositions" VALUES (1915299555683995649, 1915301218239127553, 'Mo', 0.25, 0.35, 0.30, '=', NULL, '2025-04-24 14:59:33.288433', '2025-04-24 15:06:09.676582');
INSERT INTO "public"."purpose_compositions" VALUES (1915299556334112770, 1915301218239127553, 'Al', 0.02, 0.04, 0.03, '=', NULL, '2025-04-24 14:59:33.436447', '2025-04-24 15:06:09.678331');
INSERT INTO "public"."purpose_compositions" VALUES (1915299557042950146, 1915301218239127553, 'N', 0.01, 0.02, 0.02, '=', NULL, '2025-04-24 14:59:33.615278', '2025-04-24 15:06:09.679848');
INSERT INTO "public"."purpose_compositions" VALUES (1915302296460333057, NULL, 'Cu', 2.00, 2.00, 2.00, '=', NULL, '2025-04-24 15:10:26.73635', '2025-04-24 15:10:26.73635');
INSERT INTO "public"."purpose_compositions" VALUES (1915305495665831938, 1915305580722122754, 'Si', 2.00, 4.00, 3.00, '=', NULL, '2025-04-24 15:23:09.481359', '2025-04-24 15:23:30.00094');
INSERT INTO "public"."purpose_compositions" VALUES (1915308794888593410, 1915310060893114369, 'P', 0.00, 0.02, 0.01, '=', NULL, '2025-04-24 15:36:16.076075', '2025-04-24 15:41:17.919522');
INSERT INTO "public"."purpose_compositions" VALUES (1915308794892787713, 1915310060893114369, 'Si', 0.00, 0.40, 0.20, '=', NULL, '2025-04-24 15:36:16.077416', '2025-04-24 15:41:17.922545');
INSERT INTO "public"."purpose_compositions" VALUES (1915305642529398785, 1915305809626275842, 'Mn', 0.50, 0.90, 0.70, '=', NULL, '2025-04-24 15:23:44.495329', '2025-04-24 15:24:24.340703');
INSERT INTO "public"."purpose_compositions" VALUES (1915305642537787393, 1915305809626275842, 'Ni', 1.40, 1.70, 1.55, '=', NULL, '2025-04-24 15:23:44.49663', '2025-04-24 15:24:24.342746');
INSERT INTO "public"."purpose_compositions" VALUES (1915305642541981698, 1915305809626275842, 'C', 0.15, 0.21, 0.18, '=', NULL, '2025-04-24 15:23:44.498078', '2025-04-24 15:24:24.344665');
INSERT INTO "public"."purpose_compositions" VALUES (1915305642550370306, 1915305809626275842, 'S', 0.00, 0.01, 0.01, '=', NULL, '2025-04-24 15:23:44.499508', '2025-04-24 15:24:24.346335');
INSERT INTO "public"."purpose_compositions" VALUES (1915305642554564610, 1915305809626275842, 'Cr', 1.50, 1.80, 1.65, '=', NULL, '2025-04-24 15:23:44.500782', '2025-04-24 15:24:24.347962');
INSERT INTO "public"."purpose_compositions" VALUES (1915305642558758914, 1915305809626275842, 'Mo', 0.25, 0.35, 0.30, '=', NULL, '2025-04-24 15:23:44.502016', '2025-04-24 15:24:24.352315');
INSERT INTO "public"."purpose_compositions" VALUES (1915305642562953217, 1915305809626275842, 'Al', 0.02, 0.04, 0.03, '=', NULL, '2025-04-24 15:23:44.503235', '2025-04-24 15:24:24.354193');
INSERT INTO "public"."purpose_compositions" VALUES (1915305642571341825, 1915305809626275842, 'N', 0.01, 0.02, 0.02, '=', NULL, '2025-04-24 15:23:44.504523', '2025-04-24 15:24:24.35574');
INSERT INTO "public"."purpose_compositions" VALUES (1915308794901176322, 1915310060893114369, 'Mn', 0.50, 0.90, 0.70, '=', NULL, '2025-04-24 15:36:16.078533', '2025-04-24 15:41:17.925028');
INSERT INTO "public"."purpose_compositions" VALUES (1915308794905370625, 1915310060893114369, 'Ni', 1.40, 1.70, 1.55, '=', NULL, '2025-04-24 15:36:16.079503', '2025-04-24 15:41:17.927205');
INSERT INTO "public"."purpose_compositions" VALUES (1915308794905370626, 1915310060893114369, 'C', 0.15, 0.21, 0.18, '=', NULL, '2025-04-24 15:36:16.080457', '2025-04-24 15:41:17.929441');
INSERT INTO "public"."purpose_compositions" VALUES (1915308794909564930, 1915310060893114369, 'S', 0.00, 0.01, 0.01, '=', NULL, '2025-04-24 15:36:16.081435', '2025-04-24 15:41:17.931838');
INSERT INTO "public"."purpose_compositions" VALUES (1915308794913759234, 1915310060893114369, 'Cr', 1.50, 1.80, 1.65, '=', NULL, '2025-04-24 15:36:16.082402', '2025-04-24 15:41:17.93486');
INSERT INTO "public"."purpose_compositions" VALUES (1915308794917953537, 1915310060893114369, 'Mo', 0.25, 0.35, 0.30, '=', NULL, '2025-04-24 15:36:16.083462', '2025-04-24 15:41:17.940199');
INSERT INTO "public"."purpose_compositions" VALUES (1915308794926342146, 1915310060893114369, 'Al', 0.02, 0.04, 0.03, '=', NULL, '2025-04-24 15:36:16.084522', '2025-04-24 15:41:17.942414');
INSERT INTO "public"."purpose_compositions" VALUES (1915308794930536450, 1915310060893114369, 'N', 0.01, 0.02, 0.02, '=', NULL, '2025-04-24 15:36:16.085526', '2025-04-24 15:41:17.944652');

-- ----------------------------
-- Table structure for standard_compositions
-- ----------------------------
DROP TABLE IF EXISTS "public"."standard_compositions";
CREATE TABLE "public"."standard_compositions" (
  "id" int8 NOT NULL,
  "element_name" varchar(3) COLLATE "pg_catalog"."default" NOT NULL,
  "min_value" numeric(5,2),
  "max_value" numeric(5,2),
  "standard_id" int8,
  "is_customize" varchar(1) COLLATE "pg_catalog"."default",
  "createuser" varchar(32) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6)
)
;
COMMENT ON COLUMN "public"."standard_compositions"."id" IS '主键';
COMMENT ON COLUMN "public"."standard_compositions"."element_name" IS '元素名称';
COMMENT ON COLUMN "public"."standard_compositions"."min_value" IS '最小值';
COMMENT ON COLUMN "public"."standard_compositions"."max_value" IS '最大值';
COMMENT ON COLUMN "public"."standard_compositions"."standard_id" IS '标准ID';
COMMENT ON COLUMN "public"."standard_compositions"."is_customize" IS '是否自定义';
COMMENT ON TABLE "public"."standard_compositions" IS '标准成分';

-- ----------------------------
-- Records of standard_compositions
-- ----------------------------
INSERT INTO "public"."standard_compositions" VALUES (14, 'P', 0.00, 0.02, 2, '1', '阿水', '2025-04-02 11:38:08', '2025-04-10 11:38:09');
INSERT INTO "public"."standard_compositions" VALUES (16, 'Si', 0.00, 0.40, 2, '1', '阿水', '2025-04-02 11:38:08', '2025-04-10 11:38:09');
INSERT INTO "public"."standard_compositions" VALUES (15, 'Mn', 0.50, 0.90, 2, '1', '阿水', '2025-04-02 11:38:08', '2025-04-10 11:38:09');
INSERT INTO "public"."standard_compositions" VALUES (11, 'Ni', 1.40, 1.70, 2, '1', '阿水', '2025-04-02 11:38:08', '2025-04-10 11:38:09');
INSERT INTO "public"."standard_compositions" VALUES (17, 'C', 0.15, 0.21, 2, '1', '阿水', '2025-04-02 11:38:08', '2025-04-10 11:38:09');
INSERT INTO "public"."standard_compositions" VALUES (13, 'S', 0.00, 0.01, 2, '1', '阿水', '2025-04-02 11:38:08', '2025-04-10 11:38:09');
INSERT INTO "public"."standard_compositions" VALUES (12, 'Cr', 1.50, 1.80, 2, '1', '阿水', '2025-04-02 11:38:08', '2025-04-10 11:38:09');
INSERT INTO "public"."standard_compositions" VALUES (18, 'Mo', 0.25, 0.35, 2, '1', '阿水', '2025-04-23 15:57:34', '2025-04-23 15:57:38');
INSERT INTO "public"."standard_compositions" VALUES (19, 'Al', 0.02, 0.04, 2, '1', '阿水', '2025-04-23 15:57:36', '2025-04-23 15:57:39');
INSERT INTO "public"."standard_compositions" VALUES (20, 'N', 0.01, 0.02, 2, '1', '阿水', '2025-04-23 15:57:37', '2025-04-23 15:57:40');

-- ----------------------------
-- Table structure for standard_ingredient_records
-- ----------------------------
DROP TABLE IF EXISTS "public"."standard_ingredient_records";
CREATE TABLE "public"."standard_ingredient_records" (
  "id" int8 NOT NULL,
  "user_name" varchar(32) COLLATE "pg_catalog"."default",
  "department_id" int8,
  "steel_grade_id" int8,
  "calculation_process_no" varchar(32) COLLATE "pg_catalog"."default",
  "execution_standard_id" int8,
  "raw_material_total" numeric(10,2),
  "cost_price" numeric(10,2),
  "mixing_date" timestamp(6),
  "release_date" timestamp(6),
  "category" varchar(32) COLLATE "pg_catalog"."default",
  "calculation_result_id" int8,
  "process_path_id" int8,
  "special_notes" varchar(900) COLLATE "pg_catalog"."default",
  "cost_estimattion_id" int8,
  "createuser" varchar(32) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6),
  "status" int4 DEFAULT 1
)
;
COMMENT ON COLUMN "public"."standard_ingredient_records"."id" IS '主键';
COMMENT ON COLUMN "public"."standard_ingredient_records"."user_name" IS '用户名称';
COMMENT ON COLUMN "public"."standard_ingredient_records"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."standard_ingredient_records"."steel_grade_id" IS '钢种牌号ID';
COMMENT ON COLUMN "public"."standard_ingredient_records"."calculation_process_no" IS '计算流程编号';
COMMENT ON COLUMN "public"."standard_ingredient_records"."execution_standard_id" IS '执行标准ID';
COMMENT ON COLUMN "public"."standard_ingredient_records"."raw_material_total" IS '原材料总量';
COMMENT ON COLUMN "public"."standard_ingredient_records"."cost_price" IS '总成本';
COMMENT ON COLUMN "public"."standard_ingredient_records"."mixing_date" IS '配料日期';
COMMENT ON COLUMN "public"."standard_ingredient_records"."release_date" IS '发布日期';
COMMENT ON COLUMN "public"."standard_ingredient_records"."category" IS '类别;标准配料、优化配料';
COMMENT ON COLUMN "public"."standard_ingredient_records"."calculation_result_id" IS '计算结果ID';
COMMENT ON COLUMN "public"."standard_ingredient_records"."process_path_id" IS '工艺路径ID';
COMMENT ON COLUMN "public"."standard_ingredient_records"."special_notes" IS '特殊说明';
COMMENT ON COLUMN "public"."standard_ingredient_records"."cost_estimattion_id" IS '成本测算ID;当时从车本测算发起时，填写此信息';
COMMENT ON COLUMN "public"."standard_ingredient_records"."createuser" IS '配料人';
COMMENT ON COLUMN "public"."standard_ingredient_records"."status" IS '0代表草稿，1代表历史配料';
COMMENT ON TABLE "public"."standard_ingredient_records" IS '标准配料记录表';

-- ----------------------------
-- Records of standard_ingredient_records
-- ----------------------------
INSERT INTO "public"."standard_ingredient_records" VALUES (1915253795978485762, '张三', 12, 16, 'Cost-1745464094054-3293', 2, 0.03, 4.00, '2025-04-24 11:57:43.315317', '2025-04-24 11:57:43.315337', '标准配料', NULL, 1915237675292754000, '', 1915241342175551490, NULL, '2025-04-24 11:57:43.315776', '2025-04-24 11:57:43.38578', 1);
INSERT INTO "public"."standard_ingredient_records" VALUES (1915286568281841666, '张三', 12, 16, 'Cost-1745471910337-7145', 3, 3.00, 400.00, '2025-04-24 14:07:56.840073', '2025-04-24 14:07:56.840081', '标准配料', NULL, 1915237675292754000, '', 1915274126034288641, NULL, '2025-04-24 14:07:56.840367', '2025-04-24 14:07:56.862353', 1);
INSERT INTO "public"."standard_ingredient_records" VALUES (1915287382949896194, '张三', 12, 16, 'Cost-1745471910337-7145', 2, 0.03, 4.00, '2025-04-24 14:11:11.072169', '2025-04-24 14:11:11.072191', '标准配料', NULL, 1915237675292754000, '', 1915274126034288641, NULL, '2025-04-24 14:11:11.07243', '2025-04-24 14:11:11.109657', 1);
INSERT INTO "public"."standard_ingredient_records" VALUES (1915289245321535489, '李四', 12, 16, 'Cost-1745475439288-1503', 4, 0.90, 120.00, '2025-04-24 14:18:35.095468', '2025-04-24 14:18:35.095477', '标准配料', NULL, 1915237675292754000, '', 1915288927431114754, NULL, '2025-04-24 14:18:35.095744', '2025-04-24 14:18:35.131889', 1);
INSERT INTO "public"."standard_ingredient_records" VALUES (1915301218239127553, '印度采埃孚风电公司', 12, 16, 'Cost-1745478224452-7922', 2, 0.03, 4.00, '2025-04-24 15:06:09.661657', '2025-04-24 15:06:09.661664', '标准配料', NULL, 1915237675292754000, '', 1915300609339437057, NULL, '2025-04-24 15:06:09.662239', '2025-04-24 15:06:09.694916', 1);
INSERT INTO "public"."standard_ingredient_records" VALUES (1915305580722122754, '印度采埃孚风电公司', 12, 16, 'Cost-1745479225349-2161', 2, 6.00, 12600.00, '2025-04-24 15:23:29.758915', '2025-04-24 15:23:29.758915', '标准配料', NULL, 1915237675292753922, '无特殊说明', 1915300943457693697, NULL, '2025-04-24 15:23:29.763411', '2025-04-24 15:23:31.124001', 1);
INSERT INTO "public"."standard_ingredient_records" VALUES (1915305809626275842, '印度采埃孚风电公司', 12, 16, 'Cost-1745479225349-2161', 4, 0.03, 4.00, '2025-04-24 15:24:24.333722', '2025-04-24 15:24:24.333728', '标准配料', NULL, 1915237675292754000, '测试', 1915300943457693697, NULL, '2025-04-24 15:24:24.333986', '2025-04-24 15:24:24.375136', 1);
INSERT INTO "public"."standard_ingredient_records" VALUES (1915309030243573761, '小米公司', 10, 16, 'Cost-1745479922319-4228', 2, 0.03, 4.00, '2025-04-24 15:37:12.188509', '2025-04-24 15:37:12.188514', '标准配料', NULL, 1915237675292754000, '测试', 1915307660660715522, NULL, '2025-04-24 15:37:12.188713', '2025-04-24 15:37:12.221762', 1);
INSERT INTO "public"."standard_ingredient_records" VALUES (1915309568393748482, '小米公司', 10, 16, 'Cost-1745479922319-4228', 2, 0.03, 4.00, '2025-04-24 15:39:20.49394', '2025-04-24 15:39:20.493946', '标准配料', NULL, 1915237675292754000, '测试', 1915307660660715522, NULL, '2025-04-24 15:39:20.494118', '2025-04-24 15:39:20.527447', 1);
INSERT INTO "public"."standard_ingredient_records" VALUES (1915309729375326209, '小米公司', 10, 16, 'Cost-1745479922319-4228', 2, 0.03, 4.00, '2025-04-24 15:39:58.869189', '2025-04-24 15:39:58.869214', '标准配料', NULL, 1915237675292754000, '测试', 1915307660660715522, NULL, '2025-04-24 15:39:58.876281', '2025-04-24 15:39:58.984663', 1);
INSERT INTO "public"."standard_ingredient_records" VALUES (1915309963987914754, '小米公司', 10, 16, 'Cost-1745479922319-4228', 2, 0.21, 802.00, '2025-04-24 15:40:54.809772', '2025-04-24 15:40:54.809793', '标准配料', NULL, 1915237675292753922, '测试', 1915307660660715522, NULL, '2025-04-24 15:40:54.81037', '2025-04-24 15:40:54.865148', 1);
INSERT INTO "public"."standard_ingredient_records" VALUES (1915310060893114369, '小米公司', 10, 16, 'Cost-1745479922319-4228', 2, 2.47, 26527.00, '2025-04-24 15:41:17.915989', '2025-04-24 15:41:17.916004', '标准配料', NULL, 1915237675292753922, '测试', 1915307660660715522, NULL, '2025-04-24 15:41:17.916368', '2025-04-24 15:41:17.979356', 1);

-- ----------------------------
-- Table structure for standard_raw_materials
-- ----------------------------
DROP TABLE IF EXISTS "public"."standard_raw_materials";
CREATE TABLE "public"."standard_raw_materials" (
  "id" int8 NOT NULL,
  "name" varchar(32) COLLATE "pg_catalog"."default",
  "element" varchar(5) COLLATE "pg_catalog"."default",
  "secondary_element" varchar(5) COLLATE "pg_catalog"."default",
  "carbon_element" varchar(5) COLLATE "pg_catalog"."default",
  "department_id" int8,
  "production_equipment_id" int8,
  "composition" numeric(5,2),
  "yield_rate" numeric(5,2),
  "price" numeric(10,2),
  "is_custom" char(1) COLLATE "pg_catalog"."default",
  "category" varchar(32) COLLATE "pg_catalog"."default",
  "createuser" varchar(32) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6),
  "single_consume" varchar(255) COLLATE "pg_catalog"."default",
  "department_name" varchar(255) COLLATE "pg_catalog"."default",
  "priority" int4,
  "c_content" int4
)
;
COMMENT ON COLUMN "public"."standard_raw_materials"."name" IS '名称';
COMMENT ON COLUMN "public"."standard_raw_materials"."element" IS '主元素';
COMMENT ON COLUMN "public"."standard_raw_materials"."secondary_element" IS '次元素';
COMMENT ON COLUMN "public"."standard_raw_materials"."carbon_element" IS 'C元素';
COMMENT ON COLUMN "public"."standard_raw_materials"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."standard_raw_materials"."production_equipment_id" IS '生产设备ID';
COMMENT ON COLUMN "public"."standard_raw_materials"."composition" IS '基准品味%';
COMMENT ON COLUMN "public"."standard_raw_materials"."yield_rate" IS '收得率%';
COMMENT ON COLUMN "public"."standard_raw_materials"."price" IS '价格';
COMMENT ON COLUMN "public"."standard_raw_materials"."is_custom" IS '是否自定义';
COMMENT ON COLUMN "public"."standard_raw_materials"."category" IS '原料类别';
COMMENT ON COLUMN "public"."standard_raw_materials"."single_consume" IS '单耗';
COMMENT ON COLUMN "public"."standard_raw_materials"."department_name" IS '部门名称';
COMMENT ON COLUMN "public"."standard_raw_materials"."priority" IS '优先级';
COMMENT ON COLUMN "public"."standard_raw_materials"."c_content" IS '碳含量';
COMMENT ON TABLE "public"."standard_raw_materials" IS '标准原料';

-- ----------------------------
-- Records of standard_raw_materials
-- ----------------------------
INSERT INTO "public"."standard_raw_materials" VALUES (4, '石灰石 D', 'Ca', 'Mg', NULL, 1, 104, 90.00, 95.00, 200.00, 'N', '熔剂类', '赵六', '2024-01-04 11:00:00', '2024-01-04 11:00:00', NULL, NULL, NULL, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (5, '硅铁 E', 'Si', 'Fe', 'C', 4, 105, 75.00, 85.00, 4000.00, 'N', '铁合金类', '孙七', '2024-01-05 12:00:00', '2024-01-05 12:00:00', NULL, NULL, NULL, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (6, '锰铁 F', 'Mn', 'Fe', 'C', 4, 106, 65.00, 82.00, 3500.00, 'N', '铁合金类', '周八', '2024-01-06 13:00:00', '2024-01-06 13:00:00', NULL, NULL, NULL, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (7, '镍板 G', 'Ni', NULL, NULL, 5, 107, 99.00, 98.00, 15000.00, 'N', '金属类', '吴九', '2024-01-07 14:00:00', '2024-01-07 14:00:00', NULL, NULL, NULL, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (8, '钼铁 H', 'Mo', 'Fe', 'C', 4, 108, 60.00, 80.00, 50000.00, 'N', '铁合金类', '郑十', '2024-01-08 15:00:00', '2024-01-08 15:00:00', NULL, NULL, NULL, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (9, '钛铁 I', 'Ti', 'Fe', 'C', 4, 109, 70.00, 83.00, 38000.00, 'N', '铁合金类', '王十一', '2024-01-09 16:00:00', '2024-01-09 16:00:00', NULL, NULL, NULL, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (10, '铜精矿 J', 'Cu', 'Fe', NULL, 6, 110, 25.00, 87.00, 2000.00, 'N', '矿石类', '李十二', '2024-01-10 17:00:00', '2024-01-10 17:00:00', NULL, NULL, NULL, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (11, '锌精矿 K', 'Zn', 'Fe', NULL, 6, 111, 55.00, 91.00, 1800.00, 'N', '矿石类', '张十三', '2024-01-11 18:00:00', '2024-01-11 18:00:00', NULL, NULL, NULL, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (12, '铝锭 L', 'Al', NULL, NULL, 7, 112, 99.50, 96.00, 18000.00, 'N', '金属类', '李十四', '2024-01-12 19:00:00', '2024-01-12 19:00:00', NULL, NULL, NULL, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (13, '铅精矿 M', 'Pb', 'Zn', NULL, 6, 113, 45.00, 89.00, 1500.00, 'N', '矿石类', '王十五', '2024-01-13 20:00:00', '2024-01-13 20:00:00', NULL, NULL, NULL, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (14, '锡精矿 N', 'Sn', 'Fe', NULL, 6, 114, 30.00, 86.00, 2500.00, 'N', '矿石类', '赵十六', '2024-01-14 21:00:00', '2024-01-14 21:00:00', NULL, NULL, NULL, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (1, '铁矿石 A1', 'Fe', 'Si', 'C', 1, 101, 60.00, 90.00, 500.00, '是', '矿石类', '张三', '2024-01-01 08:00:00', '2025-04-09 10:28:26.06179', NULL, NULL, 1, 5);
INSERT INTO "public"."standard_raw_materials" VALUES (2, '焦炭 B', 'C', NULL, 'C', 2, 102, 85.00, 88.00, 1200.00, 'N', '燃料类', '李四', '2024-01-02 09:00:00', '2024-01-02 09:00:00', NULL, NULL, 1, 6);
INSERT INTO "public"."standard_raw_materials" VALUES (3, '废钢 C', 'Fe', 'Mn', 'C', 3, 103, 95.00, 92.00, 3000.00, 'N', '金属类', '王五', '2024-01-03 10:00:00', '2024-01-03 10:00:00', NULL, NULL, 0, 7);
INSERT INTO "public"."standard_raw_materials" VALUES (15, '硼铁 O', 'B', 'Fe', 'C', 4, 115, 20.00, 78.00, 8000.00, 'N', '铁合金类', '孙十七', '2024-01-15 22:00:00', '2024-01-15 22:00:00', NULL, NULL, 10, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (16, '铁矿石 A', 'Fe', 'Si', 'C', 1, 101, 60.00, 90.00, 500.00, 'N', '矿石类', '张三', '2024-01-01 08:00:00', '2024-01-01 08:00:00', NULL, NULL, 9, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (0, '333', '333', '333', '333', 3, 101, 333.00, 333.00, 333.00, '1', '333', '', NULL, NULL, NULL, NULL, 8, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (1909787651972665345, 'cs', '11', '33', '44', 1, 103, 22.00, 123.00, 100.00, '否', '熔剂类', NULL, '2025-04-09 09:57:13.027366', '2025-04-09 09:57:13.028365', NULL, NULL, 5, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (1909794432769794050, 'cs2', '4', '6', '22', 2, 101, 5.00, 11.00, 100.00, '否', '铁合金类', NULL, '2025-04-09 10:24:09.695611', '2025-04-09 10:24:09.69661', NULL, NULL, 3, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (1909447077478428673, '铁矿石 AAAA', 'Feee', 'Siii', 'Cccc', 1, 101, 61.00, 90.01, 100.00, '1', '矿石类', NULL, '2025-04-08 11:23:53.740912', '2025-04-08 11:23:53.741909', NULL, NULL, 7, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (1909788114226909185, '1', '3', '5', '6', 1, 103, 4.00, 2.00, 100.00, '是', '铁合金类', NULL, '2025-04-09 09:59:03.250043', '2025-04-09 09:59:03.250043', NULL, NULL, 4, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (1909447281355157506, '铁矿石 AAAA222', 'Feee', 'Siii', 'Cccc', 1, 101, 61.00, 90.01, 100.00, '1', '矿石类', NULL, '2025-04-08 11:24:42.349303', '2025-04-08 11:37:21.752521', NULL, NULL, 6, NULL);
INSERT INTO "public"."standard_raw_materials" VALUES (1909787950200262658, '5', '3', '5', '6', 1, 103, 4.00, 2.00, 100.00, '是', '铁合金类', NULL, '2025-04-09 09:58:24.130152', '2025-04-09 09:58:24.130152', NULL, NULL, 2, NULL);

-- ----------------------------
-- Table structure for standard_raw_materials_plus
-- ----------------------------
DROP TABLE IF EXISTS "public"."standard_raw_materials_plus";
CREATE TABLE "public"."standard_raw_materials_plus" (
  "id" int8 NOT NULL,
  "name" varchar(32) COLLATE "pg_catalog"."default",
  "department_id" int8,
  "production_equipment_id" int8,
  "yield_rate" numeric(5,2),
  "price" numeric(10,2),
  "is_custom" char(1) COLLATE "pg_catalog"."default",
  "category" varchar(32) COLLATE "pg_catalog"."default",
  "priority" int4,
  "c_content" int4,
  "single_consume" varchar COLLATE "pg_catalog"."default",
  "department_name" varchar(255) COLLATE "pg_catalog"."default",
  "createuser" varchar(32) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6),
  "steel_grade" varchar(32) COLLATE "pg_catalog"."default",
  "type" varchar(32) COLLATE "pg_catalog"."default",
  "C" numeric(10,4),
  "Mn" numeric(10,4),
  "Si" numeric(10,4),
  "P" numeric(10,4),
  "Cr" numeric(10,4),
  "V" numeric(10,4),
  "Mo" numeric(10,4),
  "Ni" numeric(10,4),
  "W" numeric(10,4),
  "Cu" numeric(10,4),
  "Ti" numeric(10,4),
  "Nb" numeric(10,4),
  "Co" numeric(10,4),
  "S" numeric(10,4),
  "Sn" numeric(10,4),
  "Al" numeric(10,4),
  "Fe" numeric(10,4),
  "B" numeric(10,4),
  "Zr" numeric(10,4),
  "La" numeric(10,4),
  "Ce" numeric(10,4),
  "Ca" numeric(10,4),
  "Pb" numeric(10,4),
  "Bi" numeric(10,4),
  "Sb" numeric(10,4),
  "As" numeric(10,4),
  "Als" numeric(10,4),
  "Ta" numeric(10,4),
  "Mg" numeric(10,4),
  "Ag" numeric(10,4),
  "Hg" numeric(10,4),
  "Cd" numeric(10,4),
  "Zn" numeric(10,4),
  "Te" numeric(10,4),
  "Se" numeric(10,4),
  "Pr" numeric(10,4),
  "Nd" numeric(10,4),
  "Sc" numeric(10,4),
  "Y" numeric(10,4),
  "Hf" numeric(10,4),
  "Pcm" numeric(10,4),
  "H" numeric(10,4),
  "O" numeric(10,4),
  "N" numeric(10,4),
  "material_id" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."standard_raw_materials_plus"."steel_grade" IS '钢种';
COMMENT ON COLUMN "public"."standard_raw_materials_plus"."type" IS '类型(成分上限/成分下限/目标值)';
COMMENT ON COLUMN "public"."standard_raw_materials_plus"."material_id" IS '原料id';
COMMENT ON TABLE "public"."standard_raw_materials_plus" IS '标准原料扩展表';

-- ----------------------------
-- Records of standard_raw_materials_plus
-- ----------------------------
INSERT INTO "public"."standard_raw_materials_plus" VALUES (1915218671433928705, '测试名称', 8, 10, 12.00, NULL, '是', '矿石类', 1, NULL, NULL, '安全环保部', 'test', '2025-04-24 09:38:08.981116', '2025-04-24 09:38:08.981116', NULL, '成分上限', 1.0000, 4.0000, 7.0000, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '6b1d9928a26a4dd3');
INSERT INTO "public"."standard_raw_materials_plus" VALUES (1915218671853359106, '测试名称', 8, 10, 12.00, NULL, '是', '矿石类', 1, NULL, NULL, '安全环保部', 'test', '2025-04-24 09:38:09.071236', '2025-04-24 09:38:09.071236', NULL, '成分下限', 2.0000, 5.0000, 8.0000, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '6b1d9928a26a4dd3');
INSERT INTO "public"."standard_raw_materials_plus" VALUES (1915218672180514817, '测试名称', 8, 10, 12.00, NULL, '是', '矿石类', 1, NULL, NULL, '安全环保部', 'test', '2025-04-24 09:38:09.147997', '2025-04-24 09:38:09.147997', NULL, '目标值', 3.0000, 6.0000, 9.0000, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '6b1d9928a26a4dd3');
INSERT INTO "public"."standard_raw_materials_plus" VALUES (1915218914913275905, '测试2', 12, 11, 10.00, NULL, '是', '铁合金类', 1, NULL, NULL, '炼铁分厂', 'test', '2025-04-24 09:39:07.033237', '2025-04-24 09:39:07.033237', NULL, '成分上限', 6.0000, 2.0000, 5.0000, 8.0000, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'e547b45e33b748c4');
INSERT INTO "public"."standard_raw_materials_plus" VALUES (1915218915303346177, '测试2', 12, 11, 10.00, NULL, '是', '铁合金类', 1, NULL, NULL, '炼铁分厂', 'test', '2025-04-24 09:39:07.11778', '2025-04-24 09:39:07.11778', NULL, '成分下限', 9.0000, 4.0000, 7.0000, 9.0000, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'e547b45e33b748c4');
INSERT INTO "public"."standard_raw_materials_plus" VALUES (1915218916616163329, '测试2', 12, 11, 10.00, NULL, '是', '铁合金类', 1, NULL, NULL, '炼铁分厂', 'test', '2025-04-24 09:39:07.425935', '2025-04-24 09:39:07.425935', NULL, '目标值', 3.0000, 7.0000, 3.0000, 7.0000, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'e547b45e33b748c4');

-- ----------------------------
-- Table structure for standard_template
-- ----------------------------
DROP TABLE IF EXISTS "public"."standard_template";
CREATE TABLE "public"."standard_template" (
  "id" int8 NOT NULL,
  "path_name" varchar(32) COLLATE "pg_catalog"."default",
  "remark" varchar(255) COLLATE "pg_catalog"."default",
  "department_id" int8,
  "createuser" varchar(255) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6),
  "device" varchar(255) COLLATE "pg_catalog"."default",
  "department_name" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."standard_template"."id" IS '主键';
COMMENT ON COLUMN "public"."standard_template"."path_name" IS '路径名称';
COMMENT ON COLUMN "public"."standard_template"."remark" IS '备注';
COMMENT ON COLUMN "public"."standard_template"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."standard_template"."device" IS '设备';
COMMENT ON COLUMN "public"."standard_template"."department_name" IS '部门名称';
COMMENT ON TABLE "public"."standard_template" IS '标准配料模版表';

-- ----------------------------
-- Records of standard_template
-- ----------------------------
INSERT INTO "public"."standard_template" VALUES (1913068398917709825, '水切割工艺路径', '123321', NULL, 'test', NULL, NULL, '111', '炼铁分厂');

-- ----------------------------
-- Table structure for steel_grade_standard_mapping
-- ----------------------------
DROP TABLE IF EXISTS "public"."steel_grade_standard_mapping";
CREATE TABLE "public"."steel_grade_standard_mapping" (
  "steel_grade_id" int8 NOT NULL,
  "standard_id" int8
)
;
COMMENT ON COLUMN "public"."steel_grade_standard_mapping"."steel_grade_id" IS '钢种ID';
COMMENT ON COLUMN "public"."steel_grade_standard_mapping"."standard_id" IS '标准ID';
COMMENT ON TABLE "public"."steel_grade_standard_mapping" IS '钢种标准对应表';

-- ----------------------------
-- Records of steel_grade_standard_mapping
-- ----------------------------
INSERT INTO "public"."steel_grade_standard_mapping" VALUES (1, 1);
INSERT INTO "public"."steel_grade_standard_mapping" VALUES (16, 2);
INSERT INTO "public"."steel_grade_standard_mapping" VALUES (16, 3);
INSERT INTO "public"."steel_grade_standard_mapping" VALUES (16, 4);

-- ----------------------------
-- Table structure for steel_grades
-- ----------------------------
DROP TABLE IF EXISTS "public"."steel_grades";
CREATE TABLE "public"."steel_grades" (
  "id" int8 NOT NULL,
  "steel_grade" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "createuser" varchar(32) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6)
)
;
COMMENT ON COLUMN "public"."steel_grades"."id" IS '主键';
COMMENT ON COLUMN "public"."steel_grades"."steel_grade" IS '钢种';
COMMENT ON TABLE "public"."steel_grades" IS '钢种表';

-- ----------------------------
-- Records of steel_grades
-- ----------------------------
INSERT INTO "public"."steel_grades" VALUES (16, '18CrNiMo7-6+HH', '阿水', '2025-04-23 15:33:10', '2025-04-23 15:33:16');

-- ----------------------------
-- Table structure for template_equipment_mapping
-- ----------------------------
DROP TABLE IF EXISTS "public"."template_equipment_mapping";
CREATE TABLE "public"."template_equipment_mapping" (
  "template_id" int8 NOT NULL,
  "equipment_id" int8 NOT NULL
)
;
COMMENT ON COLUMN "public"."template_equipment_mapping"."template_id" IS '模板ID';
COMMENT ON COLUMN "public"."template_equipment_mapping"."equipment_id" IS '设备ID';
COMMENT ON TABLE "public"."template_equipment_mapping" IS '模板与设备关联表';

-- ----------------------------
-- Records of template_equipment_mapping
-- ----------------------------

-- ----------------------------
-- Table structure for template_materials_mapping
-- ----------------------------
DROP TABLE IF EXISTS "public"."template_materials_mapping";
CREATE TABLE "public"."template_materials_mapping" (
  "id" int8 NOT NULL,
  "standard_template_id" int8 NOT NULL,
  "raw_material_id" int8 NOT NULL,
  "createuser" varchar(255) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6)
)
;
COMMENT ON COLUMN "public"."template_materials_mapping"."standard_template_id" IS '模板ID';
COMMENT ON COLUMN "public"."template_materials_mapping"."raw_material_id" IS '原料ID';
COMMENT ON TABLE "public"."template_materials_mapping" IS '配料模板与原料关联表';

-- ----------------------------
-- Records of template_materials_mapping
-- ----------------------------
INSERT INTO "public"."template_materials_mapping" VALUES (2, 1, 4, '1', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1910194284184436738, 2, 6, NULL, NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1910194284482232321, 2, 7, NULL, NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1910194284742279169, 2, 8, NULL, NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1910222406430666754, 2, 9, NULL, NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1910223947337949185, 1, 3, NULL, NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068448674738178, 1913068398917709825, 4, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068448934785025, 1913068398917709825, 5, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068449396158466, 1913068398917709825, 6, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068449727508482, 1913068398917709825, 7, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068449983361025, 1913068398917709825, 8, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068450381819905, 1913068398917709825, 9, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068450704781314, 1913068398917709825, 10, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068450906107905, 1913068398917709825, 11, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068451237457922, 1913068398917709825, 12, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068451564613633, 1913068398917709825, 13, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068451895963650, 1913068398917709825, 14, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068452223119361, 1913068398917709825, 15, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068452554469378, 1913068398917709825, 16, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068452818710530, 1913068398917709825, 0, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068453007454210, 1913068398917709825, 1909447077478428700, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068453275889665, 1913068398917709825, 1909447281355157500, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068453607239681, 1913068398917709825, 1909787651972665300, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068453871480833, 1913068398917709825, 1909788114226909200, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068454135721985, 1913068398917709825, 1909794432769794000, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068454387380226, 1913068398917709825, 1909787950200262700, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068456299982849, 1913068398917709825, 4, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068456627138562, 1913068398917709825, 5, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068456891379713, 1913068398917709825, 6, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068457159815169, 1913068398917709825, 7, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068457352753154, 1913068398917709825, 8, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068457751212033, 1913068398917709825, 9, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068458078367745, 1913068398917709825, 10, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068458472632322, 1913068398917709825, 11, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068458803982338, 1913068398917709825, 12, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068459131138049, 1913068398917709825, 13, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068459395379202, 1913068398917709825, 14, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068459722534913, 1913068398917709825, 15, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068460053884929, 1913068398917709825, 16, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068460439760897, 1913068398917709825, 0, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068460641087490, 1913068398917709825, 1909447077478428700, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068460905328642, 1913068398917709825, 1909447281355157500, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068461224095745, 1913068398917709825, 1909787651972665300, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068461555445761, 1913068398917709825, 1909788114226909200, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068461815492609, 1913068398917709825, 1909794432769794000, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1913068462075539457, 1913068398917709825, 1909787950200262700, 'test', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1914845273780514817, 1, 5, '********', NULL, NULL);
INSERT INTO "public"."template_materials_mapping" VALUES (1914845273801486338, 1, 6, '********', NULL, NULL);

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS "public"."users";
CREATE TABLE "public"."users" (
  "id" int8 NOT NULL,
  "account" varchar(32) COLLATE "pg_catalog"."default",
  "user_name" varchar(32) COLLATE "pg_catalog"."default",
  "department_id" int8,
  "createuser" varchar(32) COLLATE "pg_catalog"."default",
  "createtime" timestamp(6),
  "updatetime" timestamp(6),
  "pass_word" varchar(50) COLLATE "pg_catalog"."default",
  "nick_name" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."users"."id" IS '主键';
COMMENT ON COLUMN "public"."users"."account" IS '工号';
COMMENT ON COLUMN "public"."users"."user_name" IS '用户名';
COMMENT ON COLUMN "public"."users"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."users"."pass_word" IS '密码';
COMMENT ON COLUMN "public"."users"."nick_name" IS '用户名';
COMMENT ON TABLE "public"."users" IS '用户表';

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO "public"."users" VALUES (20, NULL, '********', NULL, NULL, NULL, '2025-04-24 16:39:23.193854', 'c4ca4238a0b923820dcc509a6f75849b', '陈德利');
INSERT INTO "public"."users" VALUES (3, NULL, 'A配料室', 12, NULL, NULL, '2025-04-24 17:35:09.35549', 'c4ca4238a0b923820dcc509a6f75849b', 'A配料室');
INSERT INTO "public"."users" VALUES (1, NULL, '张三', 9, NULL, NULL, '2025-04-24 16:12:38.006219', 'c4ca4238a0b923820dcc509a6f75849b', '张三');
INSERT INTO "public"."users" VALUES (9, NULL, '2000', NULL, NULL, '2025-04-24 16:14:19.501248', '2025-04-24 16:14:19.501248', 'c4ca4238a0b923820dcc509a6f75849b', '王树财');
INSERT INTO "public"."users" VALUES (11, NULL, '********', 10, NULL, NULL, '2025-04-24 15:34:38.126727', 'c4ca4238a0b923820dcc509a6f75849b', '********');
INSERT INTO "public"."users" VALUES (29, NULL, '20063409', 12, NULL, NULL, '2025-04-22 16:22:26.667517', 'c4ca4238a0b923820dcc509a6f75849b', '20063409');
INSERT INTO "public"."users" VALUES (36, NULL, '20077979', 13, NULL, NULL, '2025-04-23 17:07:11.807212', 'c4ca4238a0b923820dcc509a6f75849b', '20077979');
INSERT INTO "public"."users" VALUES (20081013, NULL, '20081013', 12, NULL, NULL, NULL, 'c4ca4238a0b923820dcc509a6f75849b', '20081013');
INSERT INTO "public"."users" VALUES (12, NULL, '20049623', 10, NULL, NULL, '2025-04-24 15:53:54.635649', 'c4ca4238a0b923820dcc509a6f75849b', '20049623');
INSERT INTO "public"."users" VALUES (99999, '1', 'test', 1, NULL, NULL, NULL, '098f6bcd4621d373cade4e832627b4f6', '测试账号');

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."cost_estimation_id_seq"
OWNED BY "public"."cost_estimation2"."id";
SELECT setval('"public"."cost_estimation_id_seq"', 3, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."ingredient_idingot_result_sort_seq"', 31, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."ingredient_yield_result_sort_seq"', 27, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."material_yield_rates_sort_seq"', 28, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."sort_seq"', 31, true);

-- ----------------------------
-- Primary Key structure for table calculation_result
-- ----------------------------
ALTER TABLE "public"."calculation_result" ADD CONSTRAINT "calculation_result_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table chemical_elements
-- ----------------------------
CREATE INDEX "idx_chemical_elements_symbol" ON "public"."chemical_elements" USING btree (
  "element_symbol" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table chemical_elements
-- ----------------------------
ALTER TABLE "public"."chemical_elements" ADD CONSTRAINT "chemical_elements_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table cost_estimation
-- ----------------------------
ALTER TABLE "public"."cost_estimation" ADD CONSTRAINT "cost_estimation_pkey1" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table cost_estimation2
-- ----------------------------
ALTER TABLE "public"."cost_estimation2" ADD CONSTRAINT "cost_estimation_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table departments
-- ----------------------------
ALTER TABLE "public"."departments" ADD CONSTRAINT "departments_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table execution_standard
-- ----------------------------
ALTER TABLE "public"."execution_standard" ADD CONSTRAINT "execution_standard_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table ingot_yield_rates
-- ----------------------------
ALTER TABLE "public"."ingot_yield_rates" ADD CONSTRAINT "ingot_yield_rates_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table ingredient_idingot_result
-- ----------------------------
ALTER TABLE "public"."ingredient_idingot_result" ADD CONSTRAINT "ingredient_idingot_result_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table ingredient_yield_result
-- ----------------------------
ALTER TABLE "public"."ingredient_yield_result" ADD CONSTRAINT "ingredient_yield_result_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table material_elements
-- ----------------------------
CREATE INDEX "idx_material_elements_material_id" ON "public"."material_elements" USING btree (
  "material_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table material_elements
-- ----------------------------
ALTER TABLE "public"."material_elements" ADD CONSTRAINT "material_elements_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table material_yield_rates
-- ----------------------------
ALTER TABLE "public"."material_yield_rates" ADD CONSTRAINT "material_yield_rates_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table path_materials
-- ----------------------------
ALTER TABLE "public"."path_materials" ADD CONSTRAINT "path_materials_pkey" PRIMARY KEY ("path_id", "raw_material_id");

-- ----------------------------
-- Indexes structure for table process_path
-- ----------------------------
CREATE UNIQUE INDEX "process_paths_pkey" ON "public"."process_path" USING btree (
  "id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table process_path
-- ----------------------------
ALTER TABLE "public"."process_path" ADD CONSTRAINT "process_path_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table process_path_steps
-- ----------------------------
ALTER TABLE "public"."process_path_steps" ADD CONSTRAINT "process_path_steps_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table production_equipments
-- ----------------------------
ALTER TABLE "public"."production_equipments" ADD CONSTRAINT "production_equipments_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table production_factory
-- ----------------------------
ALTER TABLE "public"."production_factory" ADD CONSTRAINT "production_factory_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table purpose_compositions
-- ----------------------------
CREATE UNIQUE INDEX "history_standard_compositions_pkey" ON "public"."purpose_compositions" USING btree (
  "id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table purpose_compositions
-- ----------------------------
ALTER TABLE "public"."purpose_compositions" ADD CONSTRAINT "purpose_compositions_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table standard_compositions
-- ----------------------------
ALTER TABLE "public"."standard_compositions" ADD CONSTRAINT "standard_compositions_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table standard_ingredient_records
-- ----------------------------
CREATE UNIQUE INDEX "standard_materials_records_pkey" ON "public"."standard_ingredient_records" USING btree (
  "id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table standard_ingredient_records
-- ----------------------------
ALTER TABLE "public"."standard_ingredient_records" ADD CONSTRAINT "standard_ingredient_records_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table standard_raw_materials
-- ----------------------------
CREATE UNIQUE INDEX "standard_maintenance_materials_pkey" ON "public"."standard_raw_materials" USING btree (
  "id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table standard_raw_materials
-- ----------------------------
ALTER TABLE "public"."standard_raw_materials" ADD CONSTRAINT "standard_rwa_materials_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table standard_raw_materials_plus
-- ----------------------------
CREATE INDEX "idx_srmp_steel_grade" ON "public"."standard_raw_materials_plus" USING btree (
  "steel_grade" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_srmp_type" ON "public"."standard_raw_materials_plus" USING btree (
  "type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table standard_raw_materials_plus
-- ----------------------------
ALTER TABLE "public"."standard_raw_materials_plus" ADD CONSTRAINT "standard_raw_materials_plus_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table standard_template
-- ----------------------------
ALTER TABLE "public"."standard_template" ADD CONSTRAINT "standard_template_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table steel_grades
-- ----------------------------
ALTER TABLE "public"."steel_grades" ADD CONSTRAINT "steel_grades_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table template_equipment_mapping
-- ----------------------------
ALTER TABLE "public"."template_equipment_mapping" ADD CONSTRAINT "template_equipment_mapping_pkey" PRIMARY KEY ("template_id", "equipment_id");

-- ----------------------------
-- Indexes structure for table template_materials_mapping
-- ----------------------------
CREATE UNIQUE INDEX "template_with_materials_pkey" ON "public"."template_materials_mapping" USING btree (
  "id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table template_materials_mapping
-- ----------------------------
ALTER TABLE "public"."template_materials_mapping" ADD CONSTRAINT "template_materials_mapping_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table users
-- ----------------------------
ALTER TABLE "public"."users" ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");
