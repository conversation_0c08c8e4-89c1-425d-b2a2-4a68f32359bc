package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 标准成分
 * @TableName standard_compositions
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StandardCompositionsVO extends BaseVO {
    /**
     * 主键
     */
    @TableId
    @Schema(description = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 元素名称
     */
    @Schema(description = "元素名称")
    private String elementName;

    /**
     * 最小值
     */
    @Schema(description = "最小值")
    private BigDecimal minValue;

    /**
     * 最大值
     */
    @Schema(description = "最大值")
    private BigDecimal maxValue;

    /**
     * 标准ID
     */
    @Schema(description = "标准ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long standardId;

    /**
     * 是否自定义
     */
    @Schema(description = "是否自定义")
    private String isCustomize;

    /**
     * 
     */
    private String createuser;

    /**
     * 
     */
    private Date createtime;

    /**
     * 
     */
    private Date updatetime;




}