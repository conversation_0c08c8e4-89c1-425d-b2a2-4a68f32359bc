-- 金属料吨水单耗配置接口测试脚本
-- 执行时间：2025-01-14

-- 1. 查看表结构
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'metal_material_water_consumption_config'
ORDER BY ordinal_position;

-- 2. 查看当前表中的数据
SELECT 
    id,
    metal_material_water_consumption,
    method,
    device,
    createuser,
    createtime,
    updatetime
FROM metal_material_water_consumption_config
ORDER BY createtime DESC;

-- 3. 插入测试数据
INSERT INTO metal_material_water_consumption_config 
(metal_material_water_consumption, method, device, createuser, createtime, updatetime) 
VALUES 
(100, '电炉冶炼', '电炉1号', 'test', NOW(), NOW()),
(120, '转炉冶炼', '转炉1号', 'test', NOW(), NOW()),
(90, '电炉冶炼', '电炉2号', 'test', NOW(), NOW()),
(110, '转炉冶炼', '转炉2号', 'test', NOW(), NOW()),
(95, '感应炉冶炼', '感应炉1号', 'test', NOW(), NOW());

-- 4. 验证插入结果
SELECT 
    '插入后数据统计' as check_item,
    COUNT(*) as total_count
FROM metal_material_water_consumption_config;

-- 5. 按冶炼方法统计
SELECT 
    method as 冶炼方法,
    COUNT(*) as 配置数量,
    AVG(metal_material_water_consumption) as 平均单耗,
    MIN(metal_material_water_consumption) as 最小单耗,
    MAX(metal_material_water_consumption) as 最大单耗
FROM metal_material_water_consumption_config
GROUP BY method
ORDER BY 配置数量 DESC;

-- 6. 按冶炼设备统计
SELECT 
    device as 冶炼设备,
    method as 冶炼方法,
    metal_material_water_consumption as 单耗值
FROM metal_material_water_consumption_config
ORDER BY method, device;

-- 7. 模拟分页查询（第1页，每页3条）
SELECT 
    id,
    metal_material_water_consumption,
    method,
    device,
    createuser,
    createtime,
    updatetime
FROM metal_material_water_consumption_config
ORDER BY createtime DESC
LIMIT 3 OFFSET 0;

-- 8. 模拟条件查询（按冶炼方法）
SELECT 
    id,
    metal_material_water_consumption,
    method,
    device
FROM metal_material_water_consumption_config
WHERE method LIKE '%电炉%'
ORDER BY createtime DESC;

-- 9. 模拟条件查询（按冶炼设备）
SELECT 
    id,
    metal_material_water_consumption,
    method,
    device
FROM metal_material_water_consumption_config
WHERE device LIKE '%1号%'
ORDER BY createtime DESC;

-- 10. 模拟复合条件查询
SELECT 
    id,
    metal_material_water_consumption,
    method,
    device
FROM metal_material_water_consumption_config
WHERE method LIKE '%电炉%' 
  AND device LIKE '%1号%'
ORDER BY createtime DESC;

-- 11. 验证数据完整性
SELECT 
    '数据完整性检查' as check_type,
    '总记录数' as check_item,
    COUNT(*) as count
FROM metal_material_water_consumption_config
UNION ALL
SELECT 
    '数据完整性检查',
    '单耗为NULL的记录',
    COUNT(*)
FROM metal_material_water_consumption_config
WHERE metal_material_water_consumption IS NULL
UNION ALL
SELECT 
    '数据完整性检查',
    '方法为空的记录',
    COUNT(*)
FROM metal_material_water_consumption_config
WHERE method IS NULL OR method = ''
UNION ALL
SELECT 
    '数据完整性检查',
    '设备为空的记录',
    COUNT(*)
FROM metal_material_water_consumption_config
WHERE device IS NULL OR device = '';

-- 12. 单耗值分布统计
SELECT 
    CASE 
        WHEN metal_material_water_consumption < 100 THEN '低单耗(<100)'
        WHEN metal_material_water_consumption BETWEEN 100 AND 110 THEN '中单耗(100-110)'
        ELSE '高单耗(>110)'
    END as 单耗区间,
    COUNT(*) as 记录数量,
    GROUP_CONCAT(DISTINCT method) as 涉及方法
FROM metal_material_water_consumption_config
GROUP BY 
    CASE 
        WHEN metal_material_water_consumption < 100 THEN '低单耗(<100)'
        WHEN metal_material_water_consumption BETWEEN 100 AND 110 THEN '中单耗(100-110)'
        ELSE '高单耗(>110)'
    END
ORDER BY 记录数量 DESC;

-- 13. 创建时间分布
SELECT 
    DATE(createtime) as 创建日期,
    COUNT(*) as 创建数量,
    GROUP_CONCAT(DISTINCT createuser) as 创建人员
FROM metal_material_water_consumption_config
GROUP BY DATE(createtime)
ORDER BY 创建日期 DESC;

-- 14. 清理测试数据（可选，谨慎执行）
-- DELETE FROM metal_material_water_consumption_config WHERE createuser = 'test';

-- 15. 验证清理结果（如果执行了清理）
-- SELECT 
--     '清理后数据统计' as check_item,
--     COUNT(*) as remaining_count
-- FROM metal_material_water_consumption_config;

-- 测试脚本执行完成
SELECT '金属料吨水单耗配置接口测试脚本执行完成' as completion_status;
