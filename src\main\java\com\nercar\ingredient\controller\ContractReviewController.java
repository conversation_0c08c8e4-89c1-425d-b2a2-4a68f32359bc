package com.nercar.ingredient.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nercar.ingredient.response.PageDataResult;
import com.nercar.ingredient.response.Result;
import com.nercar.ingredient.domain.dto.ContractReviewQueryDTO;
import com.nercar.ingredient.domain.vo.ContractReviewInfoVO;
import com.nercar.ingredient.domain.vo.ContractReviewOptionVO;
import com.nercar.ingredient.service.ContractReviewApiClient;
import com.nercar.ingredient.service.ContractReviewInfoMergedService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 合同评审信息接口
 */
@Slf4j
@Tag(name = "合同评审信息接口")
@RestController
@RequestMapping("/contractReview")
public class ContractReviewController {

    @Autowired
    private ContractReviewInfoMergedService contractReviewService;

    @Autowired
    private ContractReviewApiClient apiClient;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 根据合同ID查询合同详情
     * @param contractId 合同ID
     * @return 合同详情信息
     */
    @Operation(summary = "根据合同ID查询合同详情")
    @GetMapping("/getContractById/{contractId}")
    public Result<ContractReviewInfoVO> getContractById(@PathVariable("contractId") Long contractId) {
        log.info("接收到查询合同详情请求，合同ID: {}", contractId);
        
        if (contractId == null || contractId <= 0) {
            log.warn("合同ID参数无效: {}", contractId);
            return Result.failed("合同ID参数无效");
        }
        
        ContractReviewInfoVO result = contractReviewService.getContractReviewById(contractId);
        if (result == null) {
            log.warn("未找到合同ID为 {} 的合同信息", contractId);
            return Result.failed("未找到对应的合同信息");
        }
        
        log.info("查询合同详情成功，合同编号: {}, 客户名称: {}", result.getCode(), result.getCustomerName());
        return Result.success(result);
    }

    /**
     * 分页查询合同评审信息
     * @param requestParams 合同评审系统的原始请求参数
     * @return 合同评审系统原始响应
     */
    @Operation(summary = "分页查询合同评审信息")
    @PostMapping("/getContractReviewPage")
    public Object getContractReviewPage(@RequestBody Map<String, Object> requestParams) {
        log.info("接收到分页查询合同评审信息请求，参数: {}", requestParams);

        try {
            // 直接透传请求参数给合同评审系统（不做任何转换）
            String responseJson = apiClient.getHistoricalReviews(requestParams);

            // 直接返回原始JSON响应（不做任何格式转换）
            JsonNode responseNode = objectMapper.readTree(responseJson);
            log.info("分页查询合同评审信息完成，直接返回合同评审系统原始响应");

            return responseNode;

        } catch (Exception e) {
            log.error("调用合同评审系统分页查询失败，查询条件: {}, 错误: {}", requestParams, e.getMessage(), e);

            // 降级方案：使用本地数据
            log.info("使用本地数据作为降级方案");
            // 注意：降级时需要将合同评审格式的参数转换为我们的格式
            ContractReviewQueryDTO queryDTO = convertToLocalQueryFormat(requestParams);
            IPage<ContractReviewInfoVO> localResult = contractReviewService.getContractReviewPage(queryDTO);
            return PageDataResult.success(localResult.getRecords(), (int) localResult.getTotal());
        }
    }

    /**
     * 根据合同ID查询合同详情（POST方式）
     * @param requestParams 合同评审系统的原始请求参数
     * @return 合同评审系统原始响应
     */
    @Operation(summary = "根据合同ID查询合同详情（POST方式）")
    @PostMapping("/getContractByIdPost")
    public Object getContractByIdPost(@RequestBody Map<String, Object> requestParams) {
        log.info("接收到查询合同详情请求（POST），参数: {}", requestParams);

        try {
            // 兼容处理：同时支持contractInfoId和contractId两种参数名
            String contractInfoId = (String) requestParams.get("contractInfoId");
            if (contractInfoId == null) {
                contractInfoId = (String) requestParams.get("contractId");
            }

            if (contractInfoId == null) {
                log.error("未找到合同ID参数，支持的参数名：contractInfoId 或 contractId");
                return Result.failed("缺少合同ID参数");
            }

            // 调用合同评审系统接口
            String responseJson = apiClient.getContractDetail(contractInfoId);

            // 直接返回原始JSON响应（不做任何格式转换）
            JsonNode responseNode = objectMapper.readTree(responseJson);
            log.info("查询合同详情成功，直接返回合同评审系统原始响应");

            return responseNode;

        } catch (Exception e) {
            log.error("调用合同评审系统查询合同详情失败，参数: {}, 错误: {}", requestParams, e.getMessage(), e);

            // 降级方案：使用本地数据
            log.info("使用本地数据作为降级方案");

            // 兼容处理：同时支持contractInfoId和contractId两种参数名
            String contractInfoId = (String) requestParams.get("contractInfoId");
            if (contractInfoId == null) {
                contractInfoId = (String) requestParams.get("contractId");
            }

            if (contractInfoId != null) {
                try {
                    ContractReviewInfoVO localResult = contractReviewService.getContractReviewById(Long.valueOf(contractInfoId));
                    if (localResult != null) {
                        return Result.success(localResult);
                    }
                } catch (NumberFormatException nfe) {
                    log.error("合同ID格式错误: {}", contractInfoId);
                }
            }
            return Result.failed("未找到指定的合同信息");
        }
    }

    // ==================== 下拉框数据接口 ====================

    /**
     * 获取顾客名称下拉框选项
     * @param requestParams 合同评审系统的原始请求参数
     * @return 合同评审系统原始响应
     */
    @Operation(summary = "获取顾客名称下拉框选项")
    @PostMapping("/getCustomerNameOptions")
    public Object getCustomerNameOptions(@RequestBody Map<String, Object> requestParams) {
        log.info("接收到获取顾客名称下拉框选项请求，参数: {}", requestParams);

        try {
            // 从请求参数中获取客户名称
            String customerName = (String) requestParams.getOrDefault("customerName", "");

            // 调用合同评审系统接口
            String responseJson = apiClient.getCustomerList(customerName);

            // 直接返回原始JSON响应（不做任何格式转换）
            JsonNode responseNode = objectMapper.readTree(responseJson);
            log.info("获取顾客名称选项完成，直接返回合同评审系统原始响应");

            return responseNode;

        } catch (Exception e) {
            log.error("调用合同评审系统获取客户列表失败: {}", e.getMessage(), e);

            // 降级方案：使用本地数据
            log.info("使用本地数据作为降级方案");
            List<ContractReviewOptionVO> localOptions = contractReviewService.getCustomerNameOptions();
            return Result.success(localOptions);
        }
    }

    /**
     * 获取钢种下拉框选项
     * @param requestParams 合同评审系统的原始请求参数
     * @return 合同评审系统原始响应
     */
    @Operation(summary = "获取钢种下拉框选项")
    @PostMapping("/getSteelGradeNameOptions")
    public Object getSteelGradeNameOptions(@RequestBody Map<String, Object> requestParams) {
        log.info("接收到获取钢种下拉框选项请求，参数: {}", requestParams);

        try {
            // 从请求参数中获取钢种名称
            String steelGradeName = (String) requestParams.getOrDefault("steelGradeName", "");

            // 调用合同评审系统接口
            String responseJson = apiClient.getSteelGradeList(steelGradeName);

            // 直接返回原始JSON响应（不做任何格式转换）
            JsonNode responseNode = objectMapper.readTree(responseJson);
            log.info("获取钢种选项完成，直接返回合同评审系统原始响应");

            return responseNode;

        } catch (Exception e) {
            log.error("调用合同评审系统获取钢种列表失败: {}", e.getMessage(), e);

            // 降级方案：使用本地数据
            log.info("使用本地数据作为降级方案");
            List<ContractReviewOptionVO> localOptions = contractReviewService.getSteelGradeNameOptions();
            return Result.success(localOptions);
        }
    }

    /**
     * 获取钢类下拉框选项
     * @param requestParams 合同评审系统的原始请求参数
     * @return 合同评审系统原始响应
     */
    @Operation(summary = "获取钢类下拉框选项")
    @PostMapping("/getSteelTypeNameOptions")
    public Object getSteelTypeNameOptions(@RequestBody Map<String, Object> requestParams) {
        log.info("接收到获取钢类下拉框选项请求，参数: {}", requestParams);

        try {
            // 从请求参数中获取钢类名称
            String steelTypeName = (String) requestParams.getOrDefault("steelTypeName", "");

            // 调用合同评审系统接口
            String responseJson = apiClient.getSteelTypeList(steelTypeName);

            // 直接返回原始JSON响应（不做任何格式转换）
            JsonNode responseNode = objectMapper.readTree(responseJson);
            log.info("获取钢类选项完成，直接返回合同评审系统原始响应");

            return responseNode;

        } catch (Exception e) {
            log.error("调用合同评审系统获取钢类列表失败: {}", e.getMessage(), e);

            // 降级方案：使用本地数据
            log.info("使用本地数据作为降级方案");
            List<ContractReviewOptionVO> localOptions = contractReviewService.getSteelTypeNameOptions();
            return Result.success(localOptions);
        }
    }

    // ==================== 文件相关接口 ====================

    /**
     * 获取合同附件列表
     * @param requestParams 合同评审系统的原始请求参数
     * @return 合同评审系统原始响应
     */
    @Operation(summary = "获取合同附件列表")
    @PostMapping("/getContractAttachments")
    public Object getContractAttachments(@RequestBody Map<String, Object> requestParams) {
        log.info("接收到获取合同附件列表请求，参数: {}", requestParams);

        try {
            // 从请求参数中获取合同ID
            String contractId = (String) requestParams.get("id");

            // 调用合同评审系统接口
            String responseJson = apiClient.getFileList(contractId);

            // 直接返回原始JSON响应（不做任何格式转换）
            JsonNode responseNode = objectMapper.readTree(responseJson);
            log.info("获取合同附件列表成功，直接返回合同评审系统原始响应");

            return responseNode;

        } catch (Exception e) {
            log.error("获取合同附件列表失败，参数: {}, 错误: {}", requestParams, e.getMessage(), e);
            return Result.failed("获取合同附件列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件预览链接
     * @param requestParams 合同评审系统的原始请求参数
     * @return 合同评审系统原始响应
     */
    @Operation(summary = "获取文件预览链接")
    @PostMapping("/getFilePreviewUrl")
    public Object getFilePreviewUrl(@RequestBody Map<String, Object> requestParams) {
        log.info("接收到获取文件预览链接请求，参数: {}", requestParams);

        try {
            // 从请求参数中获取文件ID
            Object fileIdObj = requestParams.get("id");
            Long fileId = null;
            if (fileIdObj instanceof Number) {
                fileId = ((Number) fileIdObj).longValue();
            } else if (fileIdObj instanceof String) {
                fileId = Long.valueOf((String) fileIdObj);
            }

            // 调用合同评审系统接口
            String responseJson = apiClient.getFilePreview(fileId);

            // 直接返回原始JSON响应（不做任何格式转换）
            JsonNode responseNode = objectMapper.readTree(responseJson);
            log.info("获取文件预览链接成功，直接返回合同评审系统原始响应");

            return responseNode;

        } catch (Exception e) {
            log.error("获取文件预览链接失败，参数: {}, 错误: {}", requestParams, e.getMessage(), e);
            return Result.failed("获取文件预览链接失败: " + e.getMessage());
        }
    }

    // ==================== 降级时的参数转换方法 ====================

    /**
     * 将合同评审系统的查询参数转换为我们的格式（仅用于降级时）
     * @param requestParams 合同评审系统的请求参数
     * @return 我们的查询DTO
     */
    private ContractReviewQueryDTO convertToLocalQueryFormat(Map<String, Object> requestParams) {
        ContractReviewQueryDTO queryDTO = new ContractReviewQueryDTO();

        // 分页参数转换：current/page → pageNo/pageSize
        Object current = requestParams.get("current");
        Object page = requestParams.get("page");

        if (current instanceof Number) {
            queryDTO.setPageNo(((Number) current).intValue());
        }
        if (page instanceof Number) {
            queryDTO.setPageSize(((Number) page).intValue());
        }

        // 查询条件转换
        queryDTO.setCodeExact((String) requestParams.get("code"));
        queryDTO.setCustomerNameExact((String) requestParams.get("customerName"));

        // 时间范围转换
        String startDate = (String) requestParams.get("startDate");
        String endDate = (String) requestParams.get("endDate");

        if (startDate != null && !startDate.trim().isEmpty()) {
            try {
                queryDTO.setStartTime(LocalDateTime.parse(startDate.replace(" ", "T")));
            } catch (Exception e) {
                log.warn("解析开始时间失败: {}", startDate);
            }
        }

        if (endDate != null && !endDate.trim().isEmpty()) {
            try {
                queryDTO.setEndTime(LocalDateTime.parse(endDate.replace(" ", "T")));
            } catch (Exception e) {
                log.warn("解析结束时间失败: {}", endDate);
            }
        }

        log.debug("降级时参数转换: {} → {}", requestParams, queryDTO);
        return queryDTO;
    }
}
