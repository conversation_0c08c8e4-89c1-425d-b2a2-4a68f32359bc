package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 钢种表
 * @TableName steel_grades
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SteelGradesVO extends BaseVO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 钢种
     */
    private String steelGrade;

    /**
     * 
     */
    private String createuser;

    /**
     * 
     */
    private Date createtime;

    /**
     * 
     */
    private Date updatetime;




}