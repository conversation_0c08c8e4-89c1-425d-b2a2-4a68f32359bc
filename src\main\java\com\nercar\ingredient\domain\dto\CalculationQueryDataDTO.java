package com.nercar.ingredient.domain.dto;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CalculationQueryDataDTO extends BaseDTO{
    //入参应该是钢种名字，标准名字，路径id，目标成分id集合，原料id集合，特殊要求，还有测算id，status，主表id
    /**
     * 测算id
     */
    @Schema(description = "测算id")
    @JsonSerialize(using = ToStringSerializer.class)
    private String calculationId;
    /**
     * 主表id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "主表id")
    private Long standardIngredientRecordsId;
    /**
     * 钢种名字
     */
    @Schema(description = "钢种名字")
    private String steelGrade;
    /**
     * 标准名字
     */
    @Schema(description = "标准名字")
    private String standardName;
    /**
     * 路径id
     */
    @Schema(description = "路径id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long processPathId;
    /**
     * 目标成分id集合
     */
    @Schema(description = "目标成分id集合")
    private List<String> purposeCompositionsIds;
    /**
     * 原料id集合（已废弃，请使用materialWithFixedWeights）
     */
    @Deprecated
    @Schema(description = "原料id集合（已废弃，请使用materialWithFixedWeights）")
    private List<String> standardRawMaterialsIds;

    /**
     * 原料及固定重量集合
     */
    @Schema(description = "原料及固定重量集合")
    private List<MaterialWithFixedWeight> materialWithFixedWeights;
    /**
     * 特殊要求
     */
    @Schema(description = "特殊要求")
    private String specialNotes;
    /**
     * 状态 0代表草稿 1代表历史配料
     */
    @Schema(description = "状态 0代表草稿 1代表历史配料")
    private Integer status;

    /**
     * 金属料吨水单耗配置ID
     */
    @Schema(description = "金属料吨水单耗配置ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long metalMaterialWaterConsumptionConfigId;

    /**
     * 金属料吨水单耗值
     */
    @Schema(description = "金属料吨水单耗值")
    private Integer metalMaterialWaterConsumption;

    /**
     * 目标成分数据
     * 当传入此参数时，系统会先保存目标成分数据，然后继续执行计算流程
     * 与purposeCompositionsIds参数二选一使用
     */
    @Schema(description = "目标成分数据，与purposeCompositionsIds二选一")
    private PurposeCompositionsQueryDTO purposeCompositionsData;

    /**
     * 检查是否包含目标成分数据
     * @return true如果包含有效的目标成分数据
     */
    public boolean hasPurposeCompositionsData() {
        return purposeCompositionsData != null &&
               purposeCompositionsData.getElementList() != null &&
               !purposeCompositionsData.getElementList().isEmpty();
    }

    /**
     * 获取有效的原料ID集合（兼容新旧参数格式）
     * 优先使用新的materialWithFixedWeights，如果为空则使用旧的standardRawMaterialsIds
     */
    public List<String> getEffectiveStandardRawMaterialsIds() {
        if (materialWithFixedWeights != null && !materialWithFixedWeights.isEmpty()) {
            return materialWithFixedWeights.stream()
                .map(MaterialWithFixedWeight::getMaterialId)
                .collect(Collectors.toList());
        }
        return standardRawMaterialsIds;
    }
}
