package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.nercar.ingredient.domain.dto.PathSaveDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 工艺路径表
 * @TableName process_path
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProcessPathVO extends BaseVO {
    /**
     * 主键
     */
    @TableId
    @Schema(description = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 钢种
     */
    @TableField(exist = false)
    @Schema(description = "钢种")
    private String steelGrade;

    @Schema(description = "钢种ID")
    private Long steelGradesId;

    /**
     * 路径名称
     */
    @Schema(description = "路径名称")
    private String pathName;

    /**
     * 用途描述
     */
    @Schema(description = "用途描述")
    private String purpose;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 使用频次
     */
    @Schema(description = "使用频次")
    private Integer frequence;

    /**
     * 成材率id
     */
    @Schema(description = "成材率ID")
    private Long materialYield;

    @Schema(description = "创建人")
    private String createuser;

    @Schema(description = "创建时间")
    private LocalDateTime createtime;

    @Schema(description = "更新时间")
    private LocalDateTime updatetime;

    /**
     * 工艺步骤
     */
    @Schema(description = "工艺步骤")
    private List<PathSaveDTO> steps;
}