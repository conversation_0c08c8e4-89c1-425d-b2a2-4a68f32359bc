package com.nercar.ingredient.domain.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 标准配料记录表
 * @TableName standard_ingredient_records
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StandardIngredientRecordsDTO extends BaseDTO {
    /**
     * 主键
     */
    @TableId
    @Schema(name = "id")
    private Long id;

    /**
     * 状态;0-草稿，1-发布，2-撤销
     */
    @Schema(name = "状态")
    private Integer status;

    /**
     * 用户名称
     */
    @Schema(name = "用户名称")
    private String userName;

    /**
     * 部门ID
     */
    @Schema(name = "部门ID")
    private Long departmentId;

    /**
     * 钢种牌号ID
     */
    @Schema(name = "钢种牌号ID")
    private Long steelGradeId;
    /**
     * 钢种牌号名称
     */
    @Schema(name = "钢种牌号名称")
    private String steelGrade;

    /**
     * 计算流程编号
     */
    @Schema(name = "计算流程编号")
    private String calculationProcessNo;

    /**
     * 执行标准ID
     */
    @Schema(name = "执行标准ID")
    private Long executionStandardId;
    /**
     * 执行标准名称
     */
    @Schema(name = "执行标准名称")
    private String executionStandard;

    /**
     * 原材料总量
     */
    @Schema(name = "原材料总量")
    private BigDecimal rawMaterialTotal;

    /**
     * 总成本
     */
    @Schema(name = "总成本")
    private BigDecimal costPrice;

    /**
     * 配料日期
     */
    @Schema(name = "配料日期")
    private LocalDateTime mixingDate;

    /**
     * 发布日期
     */
    @Schema(name = "发布日期")
    private LocalDateTime releaseDate;

    /**
     * 类别;标准配料、优化配料
     */
    @Schema(name = "类别")
    private String category;

    /**
     * 计算结果ID
     */
    @Schema(name = "计算结果ID集合")
    private List<Long> calculationResultIds;

    /**
     * 目标成分id集合
     */
    @Schema(name = "目标成分id集合")
    private List<Long> purposeCompositionIds;

    /**
     * 配料单成锭率Id集合
     */
    @Schema(name = "配料单成锭率Id集合")
    private List<Long> IdIngotRateIds;

    /**
     * 配料单成材率Id集合
     */
    @Schema(name = "配料单成材率Id集合")
    private List<Long> YieldRateIds;

    /**
     * 工艺路径ID
     */
    @Schema(name = "工艺路径ID")
    private Long processPathId;

    /**
     * 特殊说明
     */
    @Schema(name = "特殊说明")
    private String specialNotes;

    /**
     * 成本测算ID;当时从车本测算发起时，填写此信息
     */
    @Schema(name = "成本测算ID")
    private Long costEstimattionId;

    /**
     * 配料人
     */
    @Schema(name = "配料人")
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    private String createuser;

    /**
     *
     */

    @TableField(value = "createtime",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     *
     */
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;



}