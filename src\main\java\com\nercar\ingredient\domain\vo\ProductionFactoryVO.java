package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 厂表
 * @TableName production_factory
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductionFactoryVO extends BaseVO {
    /**
     * 主键
     */
    @TableId
    @Schema(description = "主键")
    private Long id;

    /**
     * 厂名称;如冶炼厂、生产厂等
     */
    @Schema(description = "厂名称;如冶炼厂、生产厂等")
    private String name;

    /**
     * 图片链接
     */
    @Schema(description = "图片链接")
    private String imagesUrl;

    /**
     * 
     */
    private String createuser;

    /**
     * 
     */
    private Date createtime;

    /**
     * 
     */
    private Date updatetime;




}