package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 原料元素表
 * @TableName material_elements
 */
@TableName(value ="material_elements")
@Data
public class MaterialElements implements Serializable {
    /**
     * 主键ID
     */
    @TableId
    @Schema(description = "id")
    private Long id;

    /**
     * 原料ID
     */
    @Schema(description = "原料ID")
    private Long materialId;

    /**
     * 元素名称
     */
    @Schema(description = "元素名称")
    private String element;

    /**
     * 元素含量%
     */
    @Schema(description = "元素含量%")
    private BigDecimal composition;

    /**
     * 元素排序
     */
    @Schema(description = "元素排序")
    private Integer sort;

    /**
     * 
     */
    private String createuser;

    /**
     * 
     */
    private Date createtime;

    /**
     * 
     */
    private Date updatetime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}