package com.nercar.ingredient.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.po.ProductionEquipments;
import com.nercar.ingredient.mapper.ProductionEquipmentsMapper;
import com.nercar.ingredient.service.ProductionEquipmentsService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【production_equipments(设备表)】的数据库操作Service实现
* @createDate 2025-04-01 13:55:28
*/
@Service
public class ProductionEquipmentsServiceImpl extends ServiceImpl<ProductionEquipmentsMapper, ProductionEquipments>
    implements ProductionEquipmentsService{

}




