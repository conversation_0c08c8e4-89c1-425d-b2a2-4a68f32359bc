package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 标准配料模版表
 * @TableName standard_template
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StandardTemplateVO extends BaseVO {
    /**
     * 主键
     */
    @TableId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 路径名称
     */
    private String pathName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 部门ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long departmentId;

    /**
     * 模板评级：1-5星级
     */
    @Schema(description = "模板评级：1-5星级")
    private Integer rating;

    /**
     *
     */
    private String createuser;

    /**
     * 
     */
    private Date createtime;

    /**
     * 
     */
    private Date updatetime;




}