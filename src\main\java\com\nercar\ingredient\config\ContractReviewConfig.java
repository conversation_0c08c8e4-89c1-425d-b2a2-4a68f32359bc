package com.nercar.ingredient.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 合同评审系统配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "contract-review")
public class ContractReviewConfig {
    
    /**
     * 合同评审系统基础URL
     */
    private String baseUrl;
    
    /**
     * 登录配置
     */
    private Login login = new Login();
    
    /**
     * Token配置
     */
    private Token token = new Token();
    
    /**
     * 接口端点配置
     */
    private Endpoints endpoints = new Endpoints();
    
    @Data
    public static class Login {
        /**
         * 登录用户名
         */
        private String username;
        
        /**
         * 登录密码
         */
        private String password;
    }
    
    @Data
    public static class Token {
        /**
         * Token请求头名称
         */
        private String headerName = "Authorization";
        
        /**
         * Token刷新间隔（分钟）
         */
        private Integer refreshInterval = 30;
        
        /**
         * Token缓存时长（分钟）
         */
        private Integer cacheDuration = 60;
    }
    
    @Data
    public static class Endpoints {
        /**
         * 登录接口
         */
        private String login = "/employee/login";
        
        /**
         * 客户列表接口
         */
        private String customerList = "/sale/getCustomerListByName";
        
        /**
         * 历史评审接口
         */
        private String historicalReviews = "/sale/getHistoricalReviews";
        
        /**
         * 钢种列表接口
         */
        private String steelGradeList = "/sale/getSteelGradeListByName";
        
        /**
         * 钢类列表接口
         */
        private String steelTypeList = "/sale/getSteelTypeListByName";
        
        /**
         * 合同详情接口
         */
        private String contractDetail = "/techCenterStandardSection/getReviewedOrderInfo";
        
        /**
         * 文件列表接口
         */
        private String fileList = "/file/select";
        
        /**
         * 文件预览接口
         */
        private String filePreview = "/file/preview";
    }
}
