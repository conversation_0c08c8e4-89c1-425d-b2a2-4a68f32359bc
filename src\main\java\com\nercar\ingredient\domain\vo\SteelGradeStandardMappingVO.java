package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 钢种标准对应表
 * @TableName steel_grade_standard_mapping
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SteelGradeStandardMappingVO extends BaseVO {
    /**
     * 钢种ID
     */
    private Long steelGradeId;

    /**
     * 标准ID
     */
    private Long standardId;




}