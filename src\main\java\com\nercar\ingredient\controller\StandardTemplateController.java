package com.nercar.ingredient.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nercar.ingredient.domain.dto.StandardRawMaterialsSaveBatchDTO;
import com.nercar.ingredient.domain.dto.StandardTemplateQueryDTO;
import com.nercar.ingredient.domain.dto.StandardTemplatePageDTO;
import com.nercar.ingredient.domain.dto.TemplateMaterialsMappingDelDTO;
import com.nercar.ingredient.domain.dto.TemplateMaterialsMappingSaveBatchDTO;
import com.nercar.ingredient.domain.po.ProcessPath;
import com.nercar.ingredient.domain.po.StandardRawMaterials;
import com.nercar.ingredient.domain.po.StandardTemplate;
import com.nercar.ingredient.domain.po.TemplateMaterialsMapping;
import com.nercar.ingredient.domain.vo.StandardTemplateVO;
import com.nercar.ingredient.response.PageDataResult;
import com.nercar.ingredient.response.Result;
import com.nercar.ingredient.service.ProcessPathService;
import com.nercar.ingredient.service.StandardTemplateService;
import com.nercar.ingredient.service.TemplateMaterialsMappingService;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "标准配料模板")
@RestController
@RequestMapping("/standardTemplate")
public class StandardTemplateController {
    @Resource
    private StandardTemplateService standardTemplateService;

    @Resource
    private TemplateMaterialsMappingService templateMaterialsMappingService;


    @Resource
    private ProcessPathService processPathService;

    @Operation(summary = "标准配料模板查询")
    @PostMapping("/getStandardTemplate")
    public Result<List<StandardTemplateVO>> getStandardTemplate() {
        // 添加按评级降序排序
        LambdaQueryWrapper<StandardTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(StandardTemplate::getRating)
               .orderByDesc(StandardTemplate::getCreatetime);

        List<StandardTemplate> list = standardTemplateService.list(wrapper);
        List<StandardTemplateVO> voList = BeanUtil.copyToList(list, StandardTemplateVO.class);
        return Result.success(voList);
    }

    @Operation(summary = "调试用：查询所有模板记录的ID和基本信息")
    @GetMapping("/debugListAllTemplates")
    public Result<List<StandardTemplate>> debugListAllTemplates() {
        try {
            log.info("=== 调试查询所有模板记录 ===");
            List<StandardTemplate> list = standardTemplateService.list();
            log.info("找到 {} 条记录", list.size());

            for (StandardTemplate template : list) {
                log.info("ID: {}, pathName: {}, remark: {}, rating: {}, createtime: {}",
                         template.getId(), template.getPathName(), template.getRemark(),
                         template.getRating(), template.getCreatetime());
            }

            return Result.success(list);
        } catch (Exception e) {
            log.error("查询模板记录失败", e);
            return Result.failed("查询失败: " + e.getMessage());
        }
    }

    @Operation(summary = "标准配料模板分页查询")
    @PostMapping("/getStandardTemplateWithPaging")
    public PageDataResult<StandardTemplateVO> getStandardTemplateWithPaging(@RequestBody StandardTemplateQueryDTO queryDTO) {
        IPage<StandardTemplateVO> result = standardTemplateService.getStandardTemplateWithPaging(queryDTO);
        return PageDataResult.success(result.getRecords(), (int) result.getTotal());
    }

    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "标准配料模板编辑")
    @PostMapping("/updateStandardTemplate")
    public Result<Boolean> updateStandardTemplate(@RequestBody StandardTemplate standardTemplate) {
        try {
            log.info("=== updateStandardTemplate 开始调试 ===");
            log.info("接收到的StandardTemplate对象: {}", standardTemplate);
            log.info("ID: {}", standardTemplate.getId());
            log.info("pathName: {}", standardTemplate.getPathName());
            log.info("remark: {}", standardTemplate.getRemark());
            log.info("rating字段值: {}", standardTemplate.getRating());
            log.info("rating字段类型: {}", standardTemplate.getRating() != null ?
                     standardTemplate.getRating().getClass().getName() : "null");

            if (standardTemplate.getId() == null) {
                log.error("ID为空，返回失败");
                return Result.failed("id不能为空");
            }

            // 查询更新前的数据
            StandardTemplate beforeUpdate = standardTemplateService.getById(standardTemplate.getId());
            if (beforeUpdate != null) {
                log.info("更新前的数据 - ID: {}, pathName: {}, remark: {}, rating: {}",
                         beforeUpdate.getId(), beforeUpdate.getPathName(),
                         beforeUpdate.getRemark(), beforeUpdate.getRating());
            } else {
                log.warn("未找到ID为 {} 的记录", standardTemplate.getId());
            }

            boolean result = standardTemplateService.updateById(standardTemplate);
            log.info("updateById执行结果: {}", result);

            // 查询更新后的数据
            StandardTemplate afterUpdate = standardTemplateService.getById(standardTemplate.getId());
            if (afterUpdate != null) {
                log.info("更新后的数据 - ID: {}, pathName: {}, remark: {}, rating: {}",
                         afterUpdate.getId(), afterUpdate.getPathName(),
                         afterUpdate.getRemark(), afterUpdate.getRating());
            }

            log.info("=== updateStandardTemplate 调试结束 ===");
            return Result.success(result);

        } catch (Exception e) {
            log.error("updateStandardTemplate执行异常", e);
            return Result.failed("更新失败: " + e.getMessage());
        }
    }
    @Operation(summary = "标准配料模板新增")
    @PostMapping("/saveStandardTemplate")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveStandardTemplate(@RequestBody StandardTemplate standardTemplate) {
        if (StringUtils.isEmpty(standardTemplate.getPathName())) {
            return Result.failed("路径名称不能为空");
        }
        if (StringUtils.isEmpty(standardTemplate.getDevice())) {
            return Result.failed("设备不能为空");
        }
        return Result.success(standardTemplateService.save(standardTemplate));
    }


    @Operation(summary = "根据模板id查询对应的模板原料")
    @PostMapping("/getTemplateMaterialsMapping")
    public PageDataResult<StandardRawMaterials> getStandardRawMaterialsInfo(@RequestBody StandardTemplatePageDTO standardTemplatePageDTO) {
        IPage<StandardRawMaterials> page = standardTemplateService.getStandardRawMaterialsInfo(standardTemplatePageDTO);
        return PageDataResult.success(page.getRecords(), (int) page.getTotal());
    }
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "删除")
    @PostMapping("/deleteTemplateMaterialsMapping")
    public Result<Boolean> deleteTemplateMaterialsMapping(@RequestBody TemplateMaterialsMappingDelDTO templateMaterialsMappingDelDTO) {
        if (StringUtils.isEmpty(templateMaterialsMappingDelDTO.getTemplateId())) {
            return Result.failed("模版TemplateId不能为空");
        }
        if (StringUtils.isEmpty(templateMaterialsMappingDelDTO.getMaterialsId())) {
            return Result.failed("原料MaterialsId不能为空");
        }
        TemplateMaterialsMapping templateMaterialsMapping = templateMaterialsMappingService.lambdaQuery().eq(TemplateMaterialsMapping::getStandardTemplateId, Long.parseLong(templateMaterialsMappingDelDTO.getTemplateId())).eq(TemplateMaterialsMapping::getRawMaterialId, Long.parseLong(templateMaterialsMappingDelDTO.getMaterialsId())).one();
        if (templateMaterialsMapping == null) {
            return Result.failed("模版原料数据不存在");
        }
        return Result.success(templateMaterialsMappingService.removeById(templateMaterialsMapping.getId()));
    }


    @Operation(summary = "根据模板id查询新增的模板原料")
    @PostMapping("/getTemplateMaterialsMappingId")
    public PageDataResult<StandardRawMaterials> getTemplateMaterialsMappingId(@RequestBody StandardTemplatePageDTO standardTemplatePageDTO) {
        IPage<StandardRawMaterials> page = standardTemplateService.getStandardRawMaterialsSaveInfo(standardTemplatePageDTO);
        return PageDataResult.success(page.getRecords(), (int) page.getTotal());
    }



    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "新增模板原料")
    @PostMapping("/getTemplateMaterialsMappingSave")
    public Result<Boolean> getTemplateMaterialsMappingSave(@RequestBody TemplateMaterialsMappingSaveBatchDTO templateMaterialsMappingSaveBatchDTO) {
        if (templateMaterialsMappingSaveBatchDTO.getTemplateId() == null) {
            return Result.failed("模板id不能为空");
        }
        if (templateMaterialsMappingSaveBatchDTO.getMaterialIds().isEmpty()){
            return Result.failed("原料数据不能为空");
        }
        templateMaterialsMappingSaveBatchDTO.getMaterialIds().forEach( materialId -> {
            TemplateMaterialsMapping templateMaterialsMapping=new TemplateMaterialsMapping();
            templateMaterialsMapping.setStandardTemplateId(Long.parseLong(templateMaterialsMappingSaveBatchDTO.getTemplateId()));
            templateMaterialsMapping.setRawMaterialId(Long.parseLong(materialId));
            templateMaterialsMappingService.save(templateMaterialsMapping);
        });
        return Result.success(true);
    }



    @Operation(summary = "工艺路径下拉框")
    @GetMapping("/processPath")
    public Result<List<ProcessPath>> getTemplateMaterialsMapping() {
        return Result.success(processPathService.list());
    }

}
