package com.nercar.ingredient.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.nercar.ingredient.domain.dto.*;
import com.nercar.ingredient.domain.po.*;
import com.nercar.ingredient.domain.vo.*;
import com.nercar.ingredient.response.Result;
import com.nercar.ingredient.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "基本信息接口")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/basicInfo")
public class BasicInfoController {

    @Autowired
    private SteelGradesService steelGradesService;
    @Autowired
    private ExecutionStandardService executionStandardService;
    @Autowired
    private StandardRawMaterialsService standardRawMaterialsService;
    @Autowired
    private DepartmentsService departmentsService;
    @Autowired
    private MaterialYieldRatesService materialYieldRatesService;
    @Autowired
    private ProductionEquipmentsService productionEquipmentsService;
    @Autowired
    private IngotYieldRatesService ingotYieldRatesService;

    @Operation(summary = "获取钢种列表")
    @PostMapping("/getSteelGrades")
    public Result<List<SteelGradesVO>> getSteelGrades(@RequestBody SteelGradesDTO steelGradesDTO) {
        log.info("接收到通过数据传输对象（DTO）获取钢种的请求，DTO: {}", steelGradesDTO); // 添加日志记录

        // 使用 distinct 查询去重
        QueryWrapper<SteelGrades> wrapper = new QueryWrapper<SteelGrades>()
                .select("DISTINCT steel_grade") // 去重字段
                .like(StringUtils.hasLength(steelGradesDTO.getSteelGrade()), "steel_grade", steelGradesDTO.getSteelGrade());

        List<SteelGrades> list = steelGradesService.list(wrapper);
        log.info("钢种查询结果: {}", list); // 添加日志记录

        List<SteelGradesVO> steelGradesVOList = BeanUtil.copyToList(list, SteelGradesVO.class);
        if (ObjectUtils.isEmpty(steelGradesVOList)) {
            log.warn("未找到符合给定条件的钢种");
            return Result.ok();
        }
        return Result.ok(steelGradesVOList);
    }

    @Operation(summary = "获取标准列表")
    @PostMapping("/getExecutionStandards")
    public Result<List<ExecutionStandardVO>> getExecutionStandards(@RequestBody ExecutionStandardDTO executionStandardDTO) {
        log.info("接收到通过数据传输对象（DTO）获取标准的请求，DTO: {}", executionStandardDTO); // 添加日志记录

        // 使用 distinct 查询去重
        QueryWrapper<ExecutionStandard> wrapper = new QueryWrapper<ExecutionStandard>()
                .select("DISTINCT standard_name") // 去重字段
                .like(StringUtils.hasLength(executionStandardDTO.getStandardName()), "standard_name", executionStandardDTO.getStandardName());

        List<ExecutionStandard> list = executionStandardService.list(wrapper);
        log.info("标准查询结果: {}", list); // 添加日志记录

        List<ExecutionStandardVO> executionStandardVOList = BeanUtil.copyToList(list, ExecutionStandardVO.class);
        if (ObjectUtils.isEmpty(executionStandardVOList)) {
            log.warn("未找到符合给定条件的标准");
            return Result.ok();
        }
        return Result.ok(executionStandardVOList);
    }

    @Autowired
    private StandardRawMaterialsPlusService standardRawMaterialsPlusService;

    @Operation(summary = "获取名称列表-plus")
    @PostMapping("/nameList")
    public Result<List<StandardRawMaterialsPlus>> nameList(@RequestBody StandardRawMaterialsDTO standardRawMaterialsDTO) {
        log.info("接收到通过数据传输对象（DTO）获取名称的请求，DTO: {}", standardRawMaterialsDTO); // 添加日志记录

        // 使用 distinct 查询去重
        QueryWrapper<StandardRawMaterialsPlus> wrapper = new QueryWrapper<StandardRawMaterialsPlus>()
                .select("DISTINCT name") // 去重字段
                .like(StringUtils.hasLength(standardRawMaterialsDTO.getName()), "name", standardRawMaterialsDTO.getName());

        List<StandardRawMaterialsPlus> list = standardRawMaterialsPlusService.list(wrapper);
        log.info("名称查询结果: {}", list); // 添加日志记录

//        List<StandardRawMaterialsVO> standardRawMaterialsVOS = BeanUtil.copyToList(list, StandardRawMaterialsVO.class);
        if (ObjectUtils.isEmpty(list)) {
            log.warn("未找到符合给定条件的名称");
            return Result.ok();
        }
        return Result.ok(list);
    }

    @Operation(summary = "获取原料类别列表-plus")
    @PostMapping("/categoryList")
    public Result<List<StandardRawMaterialsPlus>> categoryList(@RequestBody StandardRawMaterialsDTO standardRawMaterialsDTO) {
        log.info("接收到通过数据传输对象（DTO）获取原料类别的请求，DTO: {}", standardRawMaterialsDTO); // 添加日志记录

        // 使用 distinct 查询去重
        QueryWrapper<StandardRawMaterialsPlus> wrapper = new QueryWrapper<StandardRawMaterialsPlus>()
                .select("DISTINCT category") // 去重字段
                .like(StringUtils.hasLength(standardRawMaterialsDTO.getCategory()), "category", standardRawMaterialsDTO.getCategory());

        List<StandardRawMaterialsPlus> list = standardRawMaterialsPlusService.list(wrapper);
        log.info("原料类别查询结果: {}", list); // 添加日志记录

//        List<StandardRawMaterialsVO> standardRawMaterialsVOS = BeanUtil.copyToList(list, StandardRawMaterialsVO.class);
        if (ObjectUtils.isEmpty(list)) {
            log.warn("未找到符合给定条件的类别");
            return Result.ok();
        }
        return Result.ok(list);
    }

    @Operation(summary = "获取部门列表")
    @PostMapping("/departmentList")
    public Result<List<DepartmentsVO>> departmentList(@RequestBody DepartmentsDTO departmentsDTO) {
        log.info("接收到通过数据传输对象（DTO）获取部门的请求，DTO: {}", departmentsDTO); // 添加日志记录

        // 使用 distinct 查询去重
        QueryWrapper<Departments> wrapper = new QueryWrapper<Departments>()
                .select("DISTINCT department_name") // 去重字段
                .like(StringUtils.hasLength(departmentsDTO.getDepartmentName()), "department_name", departmentsDTO.getDepartmentName());

        List<Departments> list = departmentsService.list(wrapper);
        log.info("部门查询结果: {}", list); // 添加日志记录

        List<DepartmentsVO> departmentsVOList = BeanUtil.copyToList(list, DepartmentsVO.class);
        if (ObjectUtils.isEmpty(departmentsVOList)) {
            log.warn("未找到符合给定条件的部门");
            return Result.ok();
        }
        return Result.ok(departmentsVOList);
    }

    @Operation(summary = "获取生产车间列表")
    @PostMapping("/productionDeptList")
    public Result<List<MaterialYieldRatesVO>> productionDeptList(@RequestBody MaterialYieldRatesDTO materialYieldRatesDTO) {
        log.info("接收到通过数据传输对象（DTO）获取生产车间的请求，DTO: {}", materialYieldRatesDTO); // 添加日志记录

        // 使用 distinct 查询去重
        QueryWrapper<MaterialYieldRates> wrapper = new QueryWrapper<MaterialYieldRates>()
                .select("DISTINCT production_dept") // 去重字段
                .like(StringUtils.hasLength(materialYieldRatesDTO.getProductionDept()), "production_dept", materialYieldRatesDTO.getProductionDept());

        List<MaterialYieldRates> list = materialYieldRatesService.list(wrapper);
        log.info("生产车间查询结果: {}", list); // 添加日志记录

        List<MaterialYieldRatesVO> materialYieldRatesVOList = BeanUtil.copyToList(list, MaterialYieldRatesVO.class);
        if (ObjectUtils.isEmpty(materialYieldRatesVOList)) {
            log.warn("未找到符合给定条件的生产车间");
            return Result.ok();
        }
        return Result.ok(materialYieldRatesVOList);
    }

    @Operation(summary = "获取作业线列表")
    @PostMapping("/lineNameList")
    public Result<List<MaterialYieldRatesVO>> lineNameList(@RequestBody MaterialYieldRatesDTO materialYieldRatesDTO) {
        log.info("接收到通过数据传输对象（DTO）获取作业线的请求，DTO: {}", materialYieldRatesDTO); // 添加日志记录

        // 使用 distinct 查询去重
        QueryWrapper<MaterialYieldRates> wrapper = new QueryWrapper<MaterialYieldRates>()
                .select("DISTINCT line_name") // 去重字段
                .like(StringUtils.hasLength(materialYieldRatesDTO.getLineName()), "line_name", materialYieldRatesDTO.getLineName());

        List<MaterialYieldRates> list = materialYieldRatesService.list(wrapper);
        log.info("作业线查询结果: {}", list); // 添加日志记录

        List<MaterialYieldRatesVO> materialYieldRatesVOList = BeanUtil.copyToList(list, MaterialYieldRatesVO.class);
        if (ObjectUtils.isEmpty(materialYieldRatesVOList)) {
            log.warn("未找到符合给定条件的作业线");
            return Result.ok();
        }
        return Result.ok(materialYieldRatesVOList);
    }

    @Operation(summary = "获取供料类别列表")
    @PostMapping("/materialCategoryNameList")
    public Result<List<MaterialYieldRatesVO>> materialCategoryNameList(@RequestBody MaterialYieldRatesDTO materialYieldRatesDTO) {
        log.info("接收到通过数据传输对象（DTO）获取供料类别的请求，DTO: {}", materialYieldRatesDTO); // 添加日志记录

        // 使用 distinct 查询去重
        QueryWrapper<MaterialYieldRates> wrapper = new QueryWrapper<MaterialYieldRates>()
                .select("DISTINCT material_category_name") // 去重字段
                .like(StringUtils.hasLength(materialYieldRatesDTO.getMaterialCategoryName()), "material_category_name", materialYieldRatesDTO.getMaterialCategoryName());

        List<MaterialYieldRates> list = materialYieldRatesService.list(wrapper);
        log.info("供料类别查询结果: {}", list); // 添加日志记录

        List<MaterialYieldRatesVO> materialYieldRatesVOList = BeanUtil.copyToList(list, MaterialYieldRatesVO.class);
        if (ObjectUtils.isEmpty(materialYieldRatesVOList)) {
            log.warn("未找到符合给定条件的供料类别");
            return Result.ok();
        }
        return Result.ok(materialYieldRatesVOList);
    }

    @Operation(summary = "获取钢类列表")
    @PostMapping("/steelClassList")
    public Result<List<MaterialYieldRatesVO>> steelClassList(@RequestBody MaterialYieldRatesDTO materialYieldRatesDTO) {
        log.info("接收到通过数据传输对象（DTO）获取钢类的请求，DTO: {}", materialYieldRatesDTO); // 添加日志记录

        // 使用 distinct 查询去重
        QueryWrapper<MaterialYieldRates> wrapper = new QueryWrapper<MaterialYieldRates>()
                .select("DISTINCT steel_class") // 去重字段
                .like(StringUtils.hasLength(materialYieldRatesDTO.getSteelClass()), "steel_class", materialYieldRatesDTO.getSteelClass());

        List<MaterialYieldRates> list = materialYieldRatesService.list(wrapper);
        log.info("钢类查询结果: {}", list); // 添加日志记录

        List<MaterialYieldRatesVO> materialYieldRatesVOList = BeanUtil.copyToList(list, MaterialYieldRatesVO.class);
        if (ObjectUtils.isEmpty(materialYieldRatesVOList)) {
            log.warn("未找到符合给定条件的钢类");
            return Result.ok();
        }
        return Result.ok(materialYieldRatesVOList);
    }

    @Operation(summary = "获取规格列表")
    @PostMapping("/specificationsList")
    public Result<List<MaterialYieldRatesVO>> specificationsList(@RequestBody MaterialYieldRatesDTO materialYieldRatesDTO) {
        log.info("接收到通过数据传输对象（DTO）获取规格的请求，DTO: {}", materialYieldRatesDTO); // 添加日志记录

        // 使用 distinct 查询去重
        QueryWrapper<MaterialYieldRates> wrapper = new QueryWrapper<MaterialYieldRates>()
                .select("DISTINCT specifications") // 去重字段
                .like(StringUtils.hasLength(materialYieldRatesDTO.getSpecifications()), "specifications", materialYieldRatesDTO.getSpecifications());

        List<MaterialYieldRates> list = materialYieldRatesService.list(wrapper);
        log.info("规格查询结果: {}", list); // 添加日志记录

        List<MaterialYieldRatesVO> materialYieldRatesVOList = BeanUtil.copyToList(list, MaterialYieldRatesVO.class);
        if (ObjectUtils.isEmpty(materialYieldRatesVOList)) {
            log.warn("未找到符合给定条件的规格");
            return Result.ok();
        }
        return Result.ok(materialYieldRatesVOList);
    }

    @Operation(summary = "获取设备名称列表")
    @PostMapping("/equipmentNameList")
    public Result<List<ProductionEquipmentsVO>> equipmentNameList(@RequestBody ProductionEquipmentsDTO productionEquipmentsDTO) {
        log.info("接收到通过数据传输对象（DTO）获取设备名称的请求，DTO: {}", productionEquipmentsDTO); // 添加日志记录

        // 使用 distinct 查询去重
        QueryWrapper<ProductionEquipments> wrapper = new QueryWrapper<ProductionEquipments>()
                .select("DISTINCT equipment_name") // 去重字段
                .like(StringUtils.hasLength(productionEquipmentsDTO.getEquipmentName()), "equipment_name", productionEquipmentsDTO.getEquipmentName());

        List<ProductionEquipments> list = productionEquipmentsService.list(wrapper);
        log.info("设备名称查询结果: {}", list); // 添加日志记录

        List<ProductionEquipmentsVO> equipmentVOList = BeanUtil.copyToList(list, ProductionEquipmentsVO.class);
        if (ObjectUtils.isEmpty(equipmentVOList)) {
            log.warn("未找到符合给定条件的设备名称");
            return Result.ok();
        }
        return Result.ok(equipmentVOList);
    }

    @Operation(summary = "获取工序列表")
    @PostMapping("/processPathList")
    public Result<List<IngotYieldRatesVO>> processPathList(@RequestBody IngotYieldRatesDTO ingotYieldRatesDTO) {
        log.info("接收到通过数据传输对象（DTO）获取工序的请求，DTO: {}", ingotYieldRatesDTO); // 添加日志记录

        // 使用 distinct 查询去重
        QueryWrapper<IngotYieldRates> wrapper = new QueryWrapper<IngotYieldRates>()
                .select("DISTINCT process_path") // 去重字段
                .like(StringUtils.hasLength(ingotYieldRatesDTO.getProcessPath()), "process_path", ingotYieldRatesDTO.getProcessPath());

        List<IngotYieldRates> list = ingotYieldRatesService.list(wrapper);
        log.info("工序查询结果: {}", list); // 添加日志记录

        List<IngotYieldRatesVO> ingotYadRatesVOList = BeanUtil.copyToList(list, IngotYieldRatesVO.class);
        if (ObjectUtils.isEmpty(ingotYadRatesVOList)) {
            log.warn("未找到符合给定条件的工序");
            return Result.ok();
        }
        return Result.ok(ingotYadRatesVOList);
    }

    @Operation(summary = "获取成锭率部门列表")
    @PostMapping("/departmentNameList")
    public Result<List<IngotYieldRatesVO>> departmentNameList(@RequestBody IngotYieldRatesDTO ingotYieldRatesDTO) {
        log.info("接收到通过数据传输对象（DTO）获取成锭率部门的请求，DTO: {}", ingotYieldRatesDTO); // 添加日志记录

        // 使用 distinct 查询去重
        QueryWrapper<IngotYieldRates> wrapper = new QueryWrapper<IngotYieldRates>()
                .select("DISTINCT department_name") // 去重字段
                .like(StringUtils.hasLength(ingotYieldRatesDTO.getDepartmentName()), "department_name", ingotYieldRatesDTO.getDepartmentName());

        List<IngotYieldRates> list = ingotYieldRatesService.list(wrapper);
        log.info("成锭率部门查询结果: {}", list); // 添加日志记录

        List<IngotYieldRatesVO> ingotYieldRatesVOList = BeanUtil.copyToList(list, IngotYieldRatesVO.class);
        if (ObjectUtils.isEmpty(ingotYieldRatesVOList)) {
            log.warn("未找到符合给定条件的部门");
            return Result.ok();
        }
        return Result.ok(ingotYieldRatesVOList);
    }
}