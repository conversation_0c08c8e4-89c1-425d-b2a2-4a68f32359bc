package com.nercar.ingredient.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.ingredient.domain.dto.UsersDTO;
import com.nercar.ingredient.domain.po.Users;
import com.nercar.ingredient.domain.vo.UsersVO;
import com.nercar.ingredient.response.Result;

/**
* <AUTHOR>
* @description 针对表【users(用户表)】的数据库操作Service
* @createDate 2025-04-01 13:55:28
*/
public interface UsersService extends IService<Users> {

    Users login(UsersDTO usersDTO);

    Result<UsersVO> getCurrentUserInfo();

}
