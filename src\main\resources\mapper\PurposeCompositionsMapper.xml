<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.PurposeCompositionsMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.PurposeCompositions">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="standardIngredientRecordId" column="standard_ingredient_record_id" jdbcType="BIGINT"/>
            <result property="elementName" column="element_name" jdbcType="VARCHAR"/>
            <result property="minValue" column="min_value" jdbcType="NUMERIC"/>
            <result property="maxValue" column="max_value" jdbcType="NUMERIC"/>
            <result property="averageValue" column="average_value" jdbcType="NUMERIC"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="createuser" column="createuser" jdbcType="VARCHAR"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,standard_ingredient_record_id,element_name,
        min_value,max_value,average_value,
        code,createuser,createtime,
        updatetime
    </sql>
</mapper>
