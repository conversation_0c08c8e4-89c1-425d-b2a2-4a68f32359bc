package com.nercar.ingredient.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.dto.IngotYieldRatesDTO;
import com.nercar.ingredient.domain.dto.IngotYieldRatesPageDTO;
import com.nercar.ingredient.domain.po.IngotYieldRates;
import com.nercar.ingredient.domain.po.IngredientIdingotResult;
import com.nercar.ingredient.domain.po.StandardRawMaterials;
import com.nercar.ingredient.mapper.IngotYieldRatesMapper;
import com.nercar.ingredient.mapper.IngredientIdingotResultMapper;
import com.nercar.ingredient.service.IngotYieldRatesService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【ingot_yield_rates(成锭率)】的数据库操作Service实现
 * @createDate 2025-04-01 13:55:28
 */
@Service
public class IngotYieldRatesServiceImpl extends ServiceImpl<IngotYieldRatesMapper, IngotYieldRates>
        implements IngotYieldRatesService {

    @Autowired
    private IngredientIdingotResultMapper ingredientIdingotResultMapper;
    @Override
    public IPage<IngotYieldRates> getIngotYieldRatesPage(IngotYieldRatesPageDTO ingotYieldRatesPageDTO) {
        long pageNo = ingotYieldRatesPageDTO.getPageNo() != null ? ingotYieldRatesPageDTO.getPageNo() : 1;
        long pageSize = ingotYieldRatesPageDTO.getPageSize() != null ? ingotYieldRatesPageDTO.getPageSize() : 10;
        QueryWrapper<IngotYieldRates> queryWrapper = new QueryWrapper<>();

        if (StringUtils.isNotEmpty(ingotYieldRatesPageDTO.getProcessPath())) {
            queryWrapper.eq("process_path", ingotYieldRatesPageDTO.getProcessPath());
        }
        if (StringUtils.isNotEmpty(ingotYieldRatesPageDTO.getDepartmentName())) {
            queryWrapper.eq("department_name", ingotYieldRatesPageDTO.getDepartmentName());
        }

        if (StringUtils.isNotEmpty(ingotYieldRatesPageDTO.getCreatetime())) {
            try {
                String createdTimeStr = ingotYieldRatesPageDTO.getCreatetime();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                LocalDate targetDate = LocalDate.parse(createdTimeStr, formatter);
                LocalDateTime startTime = LocalDateTime.of(targetDate, LocalTime.MIN);
                LocalDateTime endTime = LocalDateTime.of(targetDate, LocalTime.MAX);
                queryWrapper.ge("createtime", startTime).le("createtime", endTime);
            } catch (Exception e) {
                throw new RuntimeException("日期格式错误，请输入正确的日期格式（例如：2025-03-13）", e);
            }
        }

        Page<IngotYieldRates> page = new Page<>(pageNo, pageSize);

        queryWrapper.orderByDesc("createtime");

        return this.page(page, queryWrapper);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> saveOrUpdateIngotYieldRate(List<IngotYieldRatesDTO> ingotYieldRatesDTOList,String id) {
        List<Long> ids = new ArrayList<>();
        for (IngotYieldRatesDTO ingotYieldRatesDTO : ingotYieldRatesDTOList) {
            if (Objects.isNull(ingotYieldRatesDTO.getId())) {
                IngotYieldRates ingotYieldRates = IngotYieldRates.builder()
                        .departmentName(ingotYieldRatesDTO.getDepartmentName())
                        .ingotYield(ingotYieldRatesDTO.getIngotYield())
                        .device(ingotYieldRatesDTO.getDevice())
                        .build();
                baseMapper.insert(ingotYieldRates);
//                // 然后查询出来
//                IngotYieldRates one = baseMapper.selectOne(new LambdaQueryWrapper<IngotYieldRates>()
//                        .eq(IngotYieldRates::getDepartmentName, ingotYieldRatesDTO.getDepartmentName())
//                        .eq(IngotYieldRates::getIngotYield, ingotYieldRatesDTO.getIngotYield())
//                        .eq(IngotYieldRates::getDevice, ingotYieldRatesDTO.getDevice())
//                );
                IngredientIdingotResult ingredientIdingotResult = new IngredientIdingotResult();
                ingredientIdingotResult.setStandardIngredientRecordId(Long.parseLong(id));
                ingredientIdingotResult.setIgingotId(ingotYieldRates.getId());
                ingredientIdingotResult.setProcessPath(ingotYieldRatesDTO.getProcessPath());
                ingredientIdingotResult.setDepartmentName(ingotYieldRatesDTO.getDepartmentName());
                ingredientIdingotResult.setIngotYield(ingotYieldRatesDTO.getIngotYield());
                ingredientIdingotResultMapper.insert(ingredientIdingotResult);
                ids.add(ingotYieldRates.getId());
            } else {
                IngotYieldRates ingotYieldRates = IngotYieldRates.builder()
                        .id(ingotYieldRatesDTO.getId())
                        .departmentName(ingotYieldRatesDTO.getDepartmentName())
                        .ingotYield(ingotYieldRatesDTO.getIngotYield())
                        .device(ingotYieldRatesDTO.getDevice())
                        .build();
                baseMapper.updateById(ingotYieldRates);
                IngredientIdingotResult ingredientIdingotResult = new IngredientIdingotResult();
                ingredientIdingotResult.setStandardIngredientRecordId(Long.parseLong(id));
                ingredientIdingotResult.setIgingotId(ingotYieldRates.getId());
                ingredientIdingotResult.setProcessPath(ingotYieldRatesDTO.getProcessPath());
                ingredientIdingotResult.setDepartmentName(ingotYieldRatesDTO.getDepartmentName());
                ingredientIdingotResult.setIngotYield(ingotYieldRatesDTO.getIngotYield());
                ingredientIdingotResultMapper.updateById(ingredientIdingotResult);
                ids.add(ingotYieldRatesDTO.getId());
            }
        }
        return ids;
    }
}
