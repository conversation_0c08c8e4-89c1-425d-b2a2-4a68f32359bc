package com.nercar.ingredient.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.ingredient.domain.dto.PurposeCompositionsQueryDTO;
import com.nercar.ingredient.domain.po.PurposeCompositions;
import com.nercar.ingredient.domain.vo.PurposeCompositionsVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【purpose_compositions(目标成分)】的数据库操作Service
* @createDate 2025-04-01 13:55:28
*/
public interface PurposeCompositionsService extends IService<PurposeCompositions> {

    List<PurposeCompositionsVO> getPurposeCompositions(PurposeCompositionsQueryDTO purposeCompositionsQueryDTO);

    void savePurposeCompositions(PurposeCompositionsQueryDTO purposeCompositionsQueryDTO);

    List<String> updatePurposeCompositions(PurposeCompositionsQueryDTO purposeCompositionsQueryDTO);
}
