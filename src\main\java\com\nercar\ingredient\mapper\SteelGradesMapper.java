package com.nercar.ingredient.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nercar.ingredient.domain.po.ProcessPath;
import com.nercar.ingredient.domain.po.SteelGrades;
import com.nercar.ingredient.domain.vo.ProcessPathVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【steel_grades(钢种表)】的数据库操作Mapper
* @createDate 2025-04-01 13:55:28
* @Entity com.nercar.ingredient.domain.po.SteelGrades
*/
@Mapper
public interface SteelGradesMapper extends BaseMapper<SteelGrades> {

    IPage<ProcessPathVO> searchProcessPath(Page<ProcessPathVO> page, @Param(Constants.WRAPPER) LambdaQueryWrapper<ProcessPathVO> queryWrapper);
}




