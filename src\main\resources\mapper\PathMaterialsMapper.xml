<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.PathMaterialsMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.PathMaterials">
            <id property="pathId" column="path_id" jdbcType="BIGINT"/>
            <id property="rawMaterialId" column="raw_material_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        path_id,raw_material_id
    </sql>
    <delete id="deleteByRawMaterialId">
        DELETE FROM path_materials
        WHERE path_id = #{processPathId} AND raw_material_id = #{rawMaterialId}
    </delete>
</mapper>
