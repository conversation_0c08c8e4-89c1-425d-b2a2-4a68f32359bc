package com.nercar.ingredient.service.impl;

import com.nercar.ingredient.domain.dto.*;
import com.nercar.ingredient.domain.po.CalculationResult;
import com.nercar.ingredient.domain.po.PurposeCompositions;
import com.nercar.ingredient.domain.po.StandardRawMaterials;
import com.nercar.ingredient.domain.vo.NewAlgorithmResponse;
import com.nercar.ingredient.domain.vo.SelectedMaterial;
import com.nercar.ingredient.mapper.PurposeCompositionsMapper;
import com.nercar.ingredient.mapper.StandardRawMaterialsMapper;
import com.nercar.ingredient.service.AlgorithmDataTransformService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 算法数据转换服务实现类
 */
@Service
@Slf4j
public class AlgorithmDataTransformServiceImpl implements AlgorithmDataTransformService {
    
    @Autowired
    private StandardRawMaterialsMapper standardRawMaterialsMapper;
    
    @Autowired
    private PurposeCompositionsMapper purposeCompositionsMapper;
    
    @Override
    public NewAlgorithmRequest transformToNewAlgorithmRequest(CalculationQueryDataDTO dto) {
        log.info("开始转换数据为新算法接口请求格式");
        
        NewAlgorithmRequest request = new NewAlgorithmRequest();
        
        // 1. 设置钢种
        request.setSteelGrade(dto.getSteelGrade());
        log.info("设置钢种: {}", dto.getSteelGrade());
        
        // 2. 设置目标重量（金属料吨水单耗）
        if (dto.getMetalMaterialWaterConsumption() != null) {
            request.setTargetWeight(dto.getMetalMaterialWaterConsumption().doubleValue());
            log.info("设置目标重量: {}", dto.getMetalMaterialWaterConsumption());
        } else {
            log.warn("金属料吨水单耗为空，设置默认值1000");
            request.setTargetWeight(1000.0);
        }
        
        // 3. 构建原料集合
        List<MaterialRequest> materials = buildMaterialRequests(dto.getEffectiveStandardRawMaterialsIds());
        request.setMaterials(materials);
        log.info("构建原料集合，数量: {}", materials.size());
        
        // 4. 构建目标成分
        List<TargetCompRequest> targetComps = buildTargetCompRequests(dto.getPurposeCompositionsIds());
        request.setTargetComp(targetComps);
        log.info("构建目标成分，数量: {}", targetComps.size());
        
        log.info("数据转换完成");
        return request;
    }
    
    /**
     * 构建原料请求列表
     */
    private List<MaterialRequest> buildMaterialRequests(List<String> materialIds) {
        List<MaterialRequest> materials = new ArrayList<>();
        
        if (materialIds == null || materialIds.isEmpty()) {
            log.warn("原料ID列表为空");
            return materials;
        }
        
        for (String materialId : materialIds) {
            try {
                StandardRawMaterials rawMaterial = standardRawMaterialsMapper.selectById(Long.parseLong(materialId));
                if (rawMaterial == null) {
                    log.warn("未找到原料，ID: {}", materialId);
                    continue;
                }
                
                MaterialRequest material = new MaterialRequest();
                material.setName(rawMaterial.getName());
                
                // 设置价格，处理空值
                if (rawMaterial.getPrice() != null) {
                    material.setPrice(rawMaterial.getPrice().doubleValue());
                } else {
                    material.setPrice(0.0);
                    log.warn("原料 {} 价格为空，设置为0", rawMaterial.getName());
                }
                
                // 设置收得率，处理空值
                if (rawMaterial.getYieldRate() != null) {
                    material.setYieldRate(rawMaterial.getYieldRate().doubleValue());
                } else {
                    material.setYieldRate(100.0);
                    log.warn("原料 {} 收得率为空，设置为100", rawMaterial.getName());
                }
                
                // 设置固定重量（基准品味）
                if (rawMaterial.getComposition() != null) {
                    material.setFixedWeight(rawMaterial.getComposition().doubleValue());
                } else {
                    material.setFixedWeight(0.0);
                    log.warn("原料 {} 基准品味为空，设置为0", rawMaterial.getName());
                }
                
                // 构建成分信息
                List<CompositionRequest> compositions = new ArrayList<>();
                if (rawMaterial.getElement() != null && rawMaterial.getComposition() != null) {
                    CompositionRequest comp = new CompositionRequest();
                    comp.setElement(rawMaterial.getElement());
                    comp.setContent(rawMaterial.getComposition().doubleValue());
                    compositions.add(comp);
                }
                material.setCompositions(compositions);
                
                materials.add(material);
                log.debug("添加原料: {}", rawMaterial.getName());
                
            } catch (NumberFormatException e) {
                log.error("原料ID格式错误: {}", materialId, e);
            } catch (Exception e) {
                log.error("处理原料ID {} 时发生异常", materialId, e);
            }
        }
        
        return materials;
    }
    
    /**
     * 构建目标成分请求列表
     */
    private List<TargetCompRequest> buildTargetCompRequests(List<String> purposeCompositionsIds) {
        List<TargetCompRequest> targetComps = new ArrayList<>();
        
        if (purposeCompositionsIds == null || purposeCompositionsIds.isEmpty()) {
            log.warn("目标成分ID列表为空");
            return targetComps;
        }
        
        for (String purposeId : purposeCompositionsIds) {
            try {
                PurposeCompositions purpose = purposeCompositionsMapper.selectById(Long.parseLong(purposeId));
                if (purpose == null) {
                    log.warn("未找到目标成分，ID: {}", purposeId);
                    continue;
                }
                
                TargetCompRequest targetComp = new TargetCompRequest();
                targetComp.setElement(purpose.getElementName());
                
                // 处理空值
                if (purpose.getMinValue() != null) {
                    targetComp.setMinValue(purpose.getMinValue().doubleValue());
                } else {
                    targetComp.setMinValue(0.0);
                }
                
                if (purpose.getMaxValue() != null) {
                    targetComp.setMaxValue(purpose.getMaxValue().doubleValue());
                } else {
                    targetComp.setMaxValue(100.0);
                }
                
                if (purpose.getAverageValue() != null) {
                    targetComp.setTargetValue(purpose.getAverageValue().doubleValue());
                } else {
                    targetComp.setTargetValue(50.0);
                }
                
                targetComps.add(targetComp);
                log.debug("添加目标成分: {}", purpose.getElementName());
                
            } catch (NumberFormatException e) {
                log.error("目标成分ID格式错误: {}", purposeId, e);
            } catch (Exception e) {
                log.error("处理目标成分ID {} 时发生异常", purposeId, e);
            }
        }
        
        return targetComps;
    }
    
    @Override
    public void processNewAlgorithmResponse(NewAlgorithmResponse response, List<CalculationResult> calculationResults) {
        log.info("开始处理新算法接口响应");
        
        if (response == null || response.getData() == null) {
            throw new RuntimeException("新算法接口响应为空");
        }
        
        if (response.getCode() != 200) {
            throw new RuntimeException("新算法接口返回错误: " + response.getMsg());
        }
        
        List<SelectedMaterial> selectedMaterials = response.getData().getSelectedMaterials();
        if (selectedMaterials == null || selectedMaterials.isEmpty()) {
            log.warn("新算法接口返回的选中原料为空");
            return;
        }
        
        log.info("新算法返回选中原料数量: {}, 计算结果数量: {}", selectedMaterials.size(), calculationResults.size());
        
        for (CalculationResult calculationResult : calculationResults) {
            String materialName = calculationResult.getRawMaterialName();
            SelectedMaterial selectedMaterial = findSelectedMaterialByName(selectedMaterials, materialName);
            
            if (selectedMaterial != null) {
                // 临时映射：使用weight作为单耗
                Double weight = selectedMaterial.getWeight();
                Double cost = selectedMaterial.getCost();
                if (weight != null) {
                    calculationResult.setSingleConsume(String.valueOf(weight));
                    calculationResult.setUnit_consumption_tons(weight.floatValue());
                    log.info("更新计算结果，原料: {}, 重量: {}, 成本: {}", materialName, weight, cost);
                } else {
                    log.warn("原料 {} 的重量为空", materialName);
                    calculationResult.setSingleConsume("0");
                    calculationResult.setUnit_consumption_tons(0.0f);
                }
            } else {
                log.warn("未找到匹配的原料: {}", materialName);
                // 设置默认值
                calculationResult.setSingleConsume("0");
                calculationResult.setUnit_consumption_tons(0.0f);
            }
        }
        
        log.info("新算法响应处理完成");
    }
    
    /**
     * 根据原料名称查找选中的原料
     */
    private SelectedMaterial findSelectedMaterialByName(List<SelectedMaterial> materials, String name) {
        if (materials == null || name == null) {
            return null;
        }
        
        return materials.stream()
                .filter(material -> name.equals(material.getName()))
                .findFirst()
                .orElse(null);
    }
}
