package com.nercar.ingredient.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nercar.ingredient.config.ContractReviewConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * 合同评审系统HTTP客户端
 * 封装所有对合同评审系统的HTTP调用
 */
@Slf4j
@Component
public class ContractReviewApiClient {
    
    @Autowired
    private ContractReviewConfig config;
    
    @Autowired
    private ContractReviewTokenManager tokenManager;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 发送POST请求到合同评审系统
     * @param endpoint 接口端点
     * @param requestBody 请求体
     * @return 响应字符串
     */
    public String postRequest(String endpoint, Object requestBody) {
        return sendRequest(endpoint, HttpMethod.POST, requestBody);
    }
    
    /**
     * 发送GET请求到合同评审系统
     * @param endpoint 接口端点
     * @return 响应字符串
     */
    public String getRequest(String endpoint) {
        return sendRequest(endpoint, HttpMethod.GET, null);
    }
    
    /**
     * 发送请求到合同评审系统
     * @param endpoint 接口端点
     * @param method HTTP方法
     * @param requestBody 请求体
     * @return 响应字符串
     */
    private String sendRequest(String endpoint, HttpMethod method, Object requestBody) {
        return sendRequest(endpoint, method, requestBody, false);
    }
    
    /**
     * 发送请求到合同评审系统（支持重试）
     * @param endpoint 接口端点
     * @param method HTTP方法
     * @param requestBody 请求体
     * @param isRetry 是否为重试请求
     * @return 响应字符串
     */
    private String sendRequest(String endpoint, HttpMethod method, Object requestBody, boolean isRetry) {
        try {
            String url = config.getBaseUrl() + endpoint;
            log.info("发送{}请求到合同评审系统: {}", method, url);
            
            // 创建请求头
            HttpHeaders headers = createHeaders();
            
            // 创建请求实体
            HttpEntity<?> requestEntity;
            if (requestBody != null) {
                requestEntity = new HttpEntity<>(requestBody, headers);
                log.debug("请求体: {}", objectMapper.writeValueAsString(requestBody));
            } else {
                requestEntity = new HttpEntity<>(headers);
            }
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(url, method, requestEntity, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                log.info("请求成功，响应状态: {}", response.getStatusCode());
                log.debug("响应内容: {}", response.getBody());
                return response.getBody();
            } else {
                log.warn("请求返回非200状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
                throw new RuntimeException("合同评审系统请求失败，状态码: " + response.getStatusCode());
            }
            
        } catch (HttpClientErrorException e) {
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED && !isRetry) {
                log.warn("Token可能已过期，尝试刷新Token后重试");
                tokenManager.forceRefreshToken();
                return sendRequest(endpoint, method, requestBody, true);
            } else {
                log.error("HTTP请求失败: {}, 状态码: {}, 响应: {}", e.getMessage(), e.getStatusCode(), e.getResponseBodyAsString());
                throw new RuntimeException("合同评审系统请求失败: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            log.error("发送请求到合同评审系统失败: {}", e.getMessage(), e);
            throw new RuntimeException("合同评审系统请求异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建请求头
     * @return HTTP请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set(config.getToken().getHeaderName(), tokenManager.getValidToken());
        return headers;
    }
    
    // ==================== 具体的接口调用方法 ====================
    
    /**
     * 获取客户列表
     * @param customerName 客户名称（可为空）
     * @return 响应字符串
     */
    public String getCustomerList(String customerName) {
        Map<String, String> request = Map.of("customerName", customerName != null ? customerName : "");
        return postRequest(config.getEndpoints().getCustomerList(), request);
    }
    
    /**
     * 获取历史评审列表
     * @param requestParams 查询参数
     * @return 响应字符串
     */
    public String getHistoricalReviews(Map<String, Object> requestParams) {
        return postRequest(config.getEndpoints().getHistoricalReviews(), requestParams);
    }
    
    /**
     * 获取钢种列表
     * @param steelGradeName 钢种名称（可为空）
     * @return 响应字符串
     */
    public String getSteelGradeList(String steelGradeName) {
        Map<String, String> request = Map.of("steelGradeName", steelGradeName != null ? steelGradeName : "");
        return postRequest(config.getEndpoints().getSteelGradeList(), request);
    }
    
    /**
     * 获取钢类列表
     * @param steelTypeName 钢类名称（可为空）
     * @return 响应字符串
     */
    public String getSteelTypeList(String steelTypeName) {
        Map<String, String> request = Map.of("steelTypeName", steelTypeName != null ? steelTypeName : "");
        return postRequest(config.getEndpoints().getSteelTypeList(), request);
    }
    
    /**
     * 获取合同详情
     * @param contractInfoId 合同ID
     * @return 响应字符串
     */
    public String getContractDetail(String contractInfoId) {
        Map<String, String> request = Map.of("contractInfoId", contractInfoId);
        return postRequest(config.getEndpoints().getContractDetail(), request);
    }
    
    /**
     * 获取文件列表
     * @param contractId 合同ID
     * @return 响应字符串
     */
    public String getFileList(String contractId) {
        Map<String, String> request = Map.of("id", contractId);
        return postRequest(config.getEndpoints().getFileList(), request);
    }
    
    /**
     * 获取文件预览链接
     * @param fileId 文件ID
     * @return 响应字符串
     */
    public String getFilePreview(Long fileId) {
        Map<String, Long> request = Map.of("id", fileId);
        return postRequest(config.getEndpoints().getFilePreview(), request);
    }
}
