package com.nercar.ingredient.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 配料单成材率
 * @TableName ingredient_yield_result
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IngredientYieldResultDTO extends BaseDTO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 配料单ID
     */
    private Long standardIngredientRecordId;

    /**
     * 成材率ID
     */
    private Long yieldId;

    /**
     * 生产车间
     */
    private String productionDept;

    /**
     * 作业线
     */
    private String lineName;

    /**
     * 成材率
     */
    private String materialYield;




}