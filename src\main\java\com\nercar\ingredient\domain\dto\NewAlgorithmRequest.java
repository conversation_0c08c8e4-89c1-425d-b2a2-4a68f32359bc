package com.nercar.ingredient.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 新算法接口请求类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class NewAlgorithmRequest {
    /**
     * 钢种
     */
    private String steelGrade;
    
    /**
     * 目标重量（金属料吨水单耗）
     */
    private Double targetWeight;
    
    /**
     * 原料集合
     */
    private List<MaterialRequest> materials;
    
    /**
     * 目标成分
     */
    private List<TargetCompRequest> targetComp;
}
