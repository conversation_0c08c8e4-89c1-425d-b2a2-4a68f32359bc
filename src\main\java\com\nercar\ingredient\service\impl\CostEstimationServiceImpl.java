package com.nercar.ingredient.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.po.CostEstimation;
import com.nercar.ingredient.service.CostEstimationService;
import com.nercar.ingredient.mapper.CostEstimationMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【cost_estimation】的数据库操作Service实现
* @createDate 2025-04-14 14:55:47
*/
@Service
public class CostEstimationServiceImpl extends ServiceImpl<CostEstimationMapper, CostEstimation>
    implements CostEstimationService{

    @Override
    public CostEstimation getCostEstimationById(long l) {
        CostEstimation costEstimation = baseMapper.selectByCostId(l);
        return costEstimation;
    }
}




