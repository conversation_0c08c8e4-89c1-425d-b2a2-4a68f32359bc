package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 用户表
 * @TableName users
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UsersVO extends BaseVO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 工号
     */
    private String account;

    /**
     * 账号
     */
    private String userName;

    /**
     * 用户名称
     */
    private String nickName;
    /**
     * 密码
     */
//    @JsonProperty(value = "passWord")
    private String password;


    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * token
     */
    private String token;

    /**
     * 
     */
    private String createuser;

    /**
     * 
     */
    private Date createtime;

    /**
     * 
     */
    private Date updatetime;




}