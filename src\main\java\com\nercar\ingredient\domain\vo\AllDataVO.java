package com.nercar.ingredient.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AllDataVO extends BaseVO{
    /**
     * 钢种
     */
    @Schema(description = "钢种")
    private String steelGrade;
    /**
     * 标准名称
     */
    @Schema(description = "标准名称")
    private String standardName;
    /**
     * 特殊说明
     */
    @Schema(description = "特殊说明")
    private String specialNotes;

    /**
     * 工艺路径
     */
    @Schema(description = "工艺路径")
    private ProcessPathVO processPath;

    /**
     * 目标成分
     */
    @Schema(description = "目标成分")
    private List<PurposeCompositionsVO> purposeCompositions;

    /**
     * 执行标准的标准成分
     */
    @Schema(description = "执行标准的标准成分")
    private List<StandardCompositionsVO> standardCompositions;

    /**
     * 原料清单
     */
    @Schema(description = "原料清单")
    private List<StandardRawMaterialsVO> standardRawMaterials;

    /**
     * 计算结果（按批次分组）
     */
    @Schema(description = "计算结果（按批次分组）")
    private List<CalculationBatchVO> calculationResult;

    /**
     * 金属料吨水单耗配置ID
     */
    @Schema(description = "金属料吨水单耗配置ID")
    private Long metalMaterialWaterConsumptionConfigId;

    /**
     * 金属料吨水单耗值
     */
    @Schema(description = "金属料吨水单耗值")
    private Integer metalMaterialWaterConsumption;

    /**
     * 冶炼方法
     */
    @Schema(description = "冶炼方法")
    private String method;

    /**
     * 冶炼设备
     */
    @Schema(description = "冶炼设备")
    private String device;

    /**
     * 成锭率集合
     */
    @Schema(description = "成锭率集合")
    private List<IngredientIdingotResultVO> ingredientIdingotResults;

    /**
     * 成材率集合
     */
    @Schema(description = "成材率集合")
    private List<IngredientYieldResultVO> ingredientYieldResults;
}