package com.nercar.ingredient.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CalculationAPIResponse extends BaseModel{

    /**
     * 原料类型
     */
    private String type;
    /**
     * 原料名称
     */
    private String material_name;
    /**
     * 原料单耗
     */
    private Float unit_consumption_tons;
}
