package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 标准原料扩展表
 * @TableName standard_raw_materials_plus
 */
@TableName(value ="standard_raw_materials_plus")
@Data
@Schema(description = "标准原料扩展表")
public class StandardRawMaterialsPlus implements Serializable {
    /**
     * 主键
     */
    @TableId
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @Schema(description = "原料id")
    @JsonSerialize(using = ToStringSerializer.class)
    private String materialId;

    @Schema(description = "计算结果ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @TableField(exist = false)
    private Long calculationResultId;

    @Schema(description = "重量%")
    @TableField(exist = false)
    private BigDecimal wieght;



    @Schema(description = "设备名称")
    @TableField(exist = false)
    private String equipmentName;
    /**
     * 成本
     */
    @Schema(description = "成本")
    @TableField(exist = false)
    private BigDecimal cost;

    /**
     * 
     */
    @Schema(description = "原料名称")
    private String name;

    /**
     * 
     */
    @Schema(description = "部门ID")
    private Long departmentId;

    /**
     * 
     */
    @Schema(description = "生产设备ID")
    private Long productionEquipmentId;

    /**
     * 
     */
    @Schema(description = "成材率")
    private BigDecimal yieldRate;

    /**
     * 
     */
    @Schema(description = "价格")
    private BigDecimal price;

    /**
     * 
     */
    @Schema(description = "是否自定义")
    private String isCustom;

    /**
     * 
     */
    @Schema(description = "类别")
    private String category;

    /**
     * 
     */
    @Schema(description = "优先级")
    private Integer priority;

    /**
     * 
     */
    @Schema(description = "碳含量")
    private Integer cContent;

    /**
     * 
     */
    @Schema(description = "单次消耗")
    private String singleConsume;

    /**
     * 
     */
    @Schema(description = "部门名称")
    private String departmentName;

    /**
     *
     */
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createuser;

    /**
     *
     */
    @TableField(value = "createtime",fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     *
     */
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;





    /**
     * 钢种
     */
    @Schema(description = "钢种")
    private String steelGrade;

    /**
     * 类型(成分上限/成分下限/目标值)
     */
    @Schema(description = "类型(成分上限/成分下限/目标值)")
    private String type;

    // 元素含量字段，使用标准化学元素符号
    @TableField("\"C\"")
    @Schema(description = "C元素含量", name = "C")
    private BigDecimal C;

    @TableField("\"Mn\"")
    @Schema(description = "Mn元素含量", name = "Mn")
    private BigDecimal Mn;

    @TableField("\"Si\"")
    @Schema(description = "Si元素含量", name = "Si")
    private BigDecimal Si;

    @TableField("\"P\"")
    @Schema(description = "P元素含量", name = "P")
    private BigDecimal P;

    @TableField("\"Cr\"")
    @Schema(description = "Cr元素含量", name = "Cr")
    private BigDecimal Cr;

    @TableField("\"V\"")
    @Schema(description = "V元素含量", name = "V")
    private BigDecimal V;

    @TableField("\"Mo\"")
    @Schema(description = "Mo元素含量", name = "Mo")
    private BigDecimal Mo;

    @TableField("\"Ni\"")
    @Schema(description = "Ni元素含量", name = "Ni")
    private BigDecimal Ni;

    @TableField("\"W\"")
    @Schema(description = "W元素含量", name = "W")
    private BigDecimal W;

    @TableField("\"Cu\"")
    @Schema(description = "Cu元素含量", name = "Cu")
    private BigDecimal Cu;

    @TableField("\"Ti\"")
    @Schema(description = "Ti元素含量", name = "Ti")
    private BigDecimal Ti;

    @TableField("\"Nb\"")
    @Schema(description = "Nb元素含量", name = "Nb")
    private BigDecimal Nb;

    @TableField("\"Co\"")
    @Schema(description = "Co元素含量", name = "Co")
    private BigDecimal Co;

    @TableField("\"S\"")
    @Schema(description = "S元素含量", name = "S")
    private BigDecimal S;

    @TableField("\"Sn\"")
    @Schema(description = "Sn元素含量", name = "Sn")
    private BigDecimal Sn;

    @TableField("\"Al\"")
    @Schema(description = "Al元素含量", name = "Al")
    private BigDecimal Al;

    @TableField("\"Fe\"")
    @Schema(description = "Fe元素含量", name = "Fe")
    private BigDecimal Fe;

    @TableField("\"B\"")
    @Schema(description = "B元素含量", name = "B")
    private BigDecimal B;

    @TableField("\"Zr\"")
    @Schema(description = "Zr元素含量", name = "Zr")
    private BigDecimal Zr;

    @TableField("\"La\"")
    @Schema(description = "La元素含量", name = "La")
    private BigDecimal La;

    @TableField("\"Ce\"")
    @Schema(description = "Ce元素含量", name = "Ce")
    private BigDecimal Ce;

    @TableField("\"Ca\"")
    @Schema(description = "Ca元素含量", name = "Ca")
    private BigDecimal Ca;

    @TableField("\"Pb\"")
    @Schema(description = "Pb元素含量", name = "Pb")
    private BigDecimal Pb;

    @TableField("\"Bi\"")
    @Schema(description = "Bi元素含量", name = "Bi")
    private BigDecimal Bi;

    @TableField("\"Sb\"")
    @Schema(description = "Sb元素含量", name = "Sb")
    private BigDecimal Sb;

    @TableField("\"As\"")
    @Schema(description = "As元素含量", name = "As")
    private BigDecimal As;

    @TableField("\"Als\"")
    @Schema(description = "Als元素含量", name = "Als")
    private BigDecimal Als;

    @TableField("\"Ta\"")
    @Schema(description = "Ta元素含量", name = "Ta")
    private BigDecimal Ta;

    @TableField("\"Mg\"")
    @Schema(description = "Mg元素含量", name = "Mg")
    private BigDecimal Mg;

    @TableField("\"Ag\"")
    @Schema(description = "Ag元素含量", name = "Ag")
    private BigDecimal Ag;

    @TableField("\"Hg\"")
    @Schema(description = "Hg元素含量", name = "Hg")
    private BigDecimal Hg;

    @TableField("\"Cd\"")
    @Schema(description = "Cd元素含量", name = "Cd")
    private BigDecimal Cd;

    @TableField("\"Zn\"")
    @Schema(description = "Zn元素含量", name = "Zn")
    private BigDecimal Zn;

    @TableField("\"Te\"")
    @Schema(description = "Te元素含量", name = "Te")
    private BigDecimal Te;

    @TableField("\"Se\"")
    @Schema(description = "Se元素含量", name = "Se")
    private BigDecimal Se;

    @TableField("\"Pr\"")
    @Schema(description = "Pr元素含量", name = "Pr")
    private BigDecimal Pr;

    @TableField("\"Nd\"")
    @Schema(description = "Nd元素含量", name = "Nd")
    private BigDecimal Nd;

    @TableField("\"Sc\"")
    @Schema(description = "Sc元素含量", name = "Sc")
    private BigDecimal Sc;

    @TableField("\"Y\"")
    @Schema(description = "Y元素含量", name = "Y")
    private BigDecimal Y;

    @TableField("\"Hf\"")
    @Schema(description = "Hf元素含量", name = "Hf")
    private BigDecimal Hf;

    @TableField("\"Pcm\"")
    @Schema(description = "Pcm元素含量", name = "Pcm")
    private BigDecimal Pcm;

    @TableField("\"H\"")
    @Schema(description = "H元素含量", name = "H")
    private BigDecimal H;

    @TableField("\"O\"")
    @Schema(description = "O元素含量", name = "O")
    private BigDecimal O;

    @TableField("\"N\"")
    @Schema(description = "N元素含量", name = "N")
    private BigDecimal N;

    /**
     * 物料代码
     */
    @Schema(description = "物料代码")
    private String materialCode;

    /**
     * 标准号
     */
    @Schema(description = "标准号")
    private String standardNo;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}