package com.nercar.ingredient.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 成本测算
 * @TableName cost_estimation
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CostEstimationDTO extends BaseDTO {
    /**
     * 主键
     */
    @TableId
    private Integer id;

    /**
     * 评审编号
     */
    private String estimationNo;

    /**
     * 用户名称
     */
    private String username;

    /**
     * 所属分公司ID
     */
    private Integer companyId;

    /**
     * 钢号ID
     */
    private Integer steelNumberId;

    /**
     * 成品规格
     */
    private String finishedProductSpecification;

    /**
     * 预计订货量
     */
    private String estimatedOrderQuantity;

    /**
     * 报价或测算成本
     */
    private String quotationOrCostEstimation;

    /**
     * 是否锻材
     */
    private String forgedMaterial;

    /**
     * 是否修改标准成本
     */
    private String changeStandardCost;

    /**
     * 长度交货状态ID
     */
    private Integer lengthDeliveryStatusId;

    /**
     * 表面交货状态ID
     */
    private Integer surfaceDeliveryStatusId;

    /**
     * 热处理交货状态ID
     */
    private Integer heatDeliveryStatusId;

    /**
     * 技术标准
     */
    private String technicalStandard;

    /**
     * 工艺路线
     */
    private String processRoute;

    /**
     * 审核状态
     */
    private String approveStatus;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */

    private String updatedBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;



}