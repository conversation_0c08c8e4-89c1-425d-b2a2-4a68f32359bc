package com.nercar.ingredient.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nercar.ingredient.domain.dto.MaterialYieldRatesDTO;
import com.nercar.ingredient.domain.po.MaterialYieldRates;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.ingredient.domain.vo.MaterialYieldRatesVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【material_yield_rates(成材率)】的数据库操作Service
* @createDate 2025-04-07 15:15:46
*/
public interface MaterialYieldRatesService extends IService<MaterialYieldRates> {

    IPage<MaterialYieldRates> selectMaterialYieldRates(MaterialYieldRatesDTO materialYieldRatesDTO);

    List<Long> saveOrUpdateMaterialYieldRate(List<MaterialYieldRatesDTO> materialYieldRatesDTOList, String id);

    void importExcel(MultipartFile file);
}
