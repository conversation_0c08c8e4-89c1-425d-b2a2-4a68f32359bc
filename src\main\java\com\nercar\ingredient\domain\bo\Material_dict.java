package com.nercar.ingredient.domain.bo;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Material_dict extends BaseModel{

    /**
     * 名称
     */
    private String name;

    /**
     * 主元素
     */
    private String element;
    /**
     * 基准品味%
     */
    private float g_rate;

    /**
     * 收得率%
     */
    private float r_rate;


    /**
     * 碳含量
     */
    private float c_content;

    /**
     * 优先级
     */
    private Integer priority;
}
