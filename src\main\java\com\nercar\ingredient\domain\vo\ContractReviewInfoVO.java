package com.nercar.ingredient.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * 合同评审信息VO
 */
@Slf4j
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "合同评审信息VO")
public class ContractReviewInfoVO extends BaseVO {
    
    /**
     * 合同信息主键ID
     */
    @Schema(description = "合同信息主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    
    /**
     * 合同编号
     */
    @Schema(description = "合同编号，如：F0003-2025")
    private String code;
    
    /**
     * 客户名称
     */
    @Schema(description = "客户名称，如：小米公司")
    private String customerName;
    
    /**
     * 客户联系电话
     */
    @Schema(description = "客户联系电话，如：12345678901")
    private String customerPhone;
    
    /**
     * 钢材类型名称
     */
    @Schema(description = "钢材类型名称，如：汽车钢、工具钢、不锈钢")
    private String steelTypeName;
    
    /**
     * 钢材牌号名称
     */
    @Schema(description = "钢材牌号名称，如：N07718、20CrMnTi、304")
    private String steelGradeName;
    
    /**
     * 规格
     */
    @Schema(description = "格式化后的规格显示，如：薄板:长100000mm*宽1000mm（备注）")
    private String specification;
    
    /**
     * 钢材数量
     */
    @Schema(description = "钢材数量")
    private Integer steelNumber;
    
    /**
     * 钢材数量单位
     */
    @Schema(description = "钢材数量单位，如：吨、捆、根、件")
    private String steelNumberUnit;
    
    /**
     * 执行标准名称
     */
    @Schema(description = "执行标准名称，如：GB/T14841-2008、QJ/DT01.13590-2014")
    private String standardName;
    
    /**
     * 技术条件名称
     */
    @Schema(description = "技术条件名称，如：技术中心推荐、技术中心提供、用户提供")
    private String technicalStandardName;
    
    /**
     * 交货状态
     */
    @Schema(description = "交货状态，如：锻造退火车光削皮、热轧、冷拉")
    private String deliveryStatus;
    
    /**
     * 加工用途
     */
    @Schema(description = "加工用途，如：不说明、军工用钢、航空航天")
    private String processingPurpose;
    
    /**
     * 冶炼工艺（内部字段，用于存储JSON字符串）
     */
    @JsonIgnore  // 隐藏原字段，不在JSON中显示
    private String smeltingProcess;
    
    /**
     * 特殊要求说明
     */
    @Schema(description = "特殊要求说明，如：特殊要求测试2")
    private String specialRequirements;
    
    /**
     * 备注信息
     */
    @Schema(description = "备注信息，如：备注测试2")
    private String remark;
    
    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名，如：刘晓斌")
    private String authorName;
    
    /**
     * 业务员姓名
     */
    @Schema(description = "业务员姓名，如：业务员C")
    private String salesmanName;
    
    /**
     * 是否为头材
     */
    @Schema(description = "是否为头材，1-是，0-否")
    private Integer isHead;
    
    /**
     * 是否进行成本测算
     */
    @Schema(description = "是否进行成本测算，true-是，false-否")
    private Boolean isCostCalculation;
    
    /**
     * 是否评审能否生产
     */
    @Schema(description = "是否评审能否生产，true-是，false-否")
    private Boolean isProduce;
    
    /**
     * 是否评审外委厂商
     */
    @Schema(description = "是否评审外委厂商，true-是，false-否")
    private Boolean isOutsourcingFirm;
    
    /**
     * 外委厂商名称
     */
    @Schema(description = "外委厂商名称，如：外委厂商1")
    private String outsourcingName;
    
    /**
     * 外委厂商联系电话
     */
    @Schema(description = "外委厂商联系电话，如：12345678901")
    private String outsourcingPhone;
    
    /**
     * 外委价格
     */
    @Schema(description = "外委价格，单位：元")
    private BigDecimal outsourcingPrice;
    
    /**
     * 推荐路线
     */
    @Schema(description = "推荐路线，0-常规流程，1-特殊流程")
    private Integer recommendRoute;
    
    /**
     * 提交时间
     */
    @Schema(description = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;
    
    /**
     * 记录创建时间
     */
    @Schema(description = "记录创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 记录更新时间
     */
    @Schema(description = "记录更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    // ==================== 动态转换方法 ====================

    /**
     * 获取冶炼工艺字符串数组（用于JSON序列化）
     * 将JSON字符串格式转换为字符串数组格式，与合同评审系统保持一致
     *
     * @return 冶炼工艺字符串数组
     */
    @JsonProperty("smeltingProcess")  // 确保JSON序列化时使用正确的字段名
    @Schema(description = "冶炼工艺，字符串数组格式，如：[\"电炉\",\"LF\",\"真空感应炉\",\"连铸\"]")
    public List<String> getSmeltingProcessList() {
        if (smeltingProcess == null || smeltingProcess.trim().isEmpty()) {
            return Collections.emptyList();
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(smeltingProcess, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            log.warn("解析冶炼工艺JSON字符串失败: {}, 原始值: {}", e.getMessage(), smeltingProcess);
            return Collections.emptyList();
        }
    }

    /**
     * 设置冶炼工艺字符串（用于BeanUtil.copyProperties）
     *
     * @param smeltingProcess 冶炼工艺JSON字符串
     */
    public void setSmeltingProcess(String smeltingProcess) {
        this.smeltingProcess = smeltingProcess;
    }
}
