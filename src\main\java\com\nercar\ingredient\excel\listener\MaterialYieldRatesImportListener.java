package com.nercar.ingredient.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.nercar.ingredient.domain.po.MaterialYieldRates;
import com.nercar.ingredient.excel.MaterialYieldRatesExcel;
import com.nercar.ingredient.service.MaterialYieldRatesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import java.math.BigDecimal;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
public class MaterialYieldRatesImportListener extends AnalysisEventListener<MaterialYieldRatesExcel> {
    
    private static final int BATCH_COUNT = 5000; // 增加批处理大小
    private final List<MaterialYieldRates> list = new ArrayList<>(BATCH_COUNT); // 预分配容量
    private final MaterialYieldRatesService materialYieldRatesService;
    
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 打印表头信息
        log.info("Excel表头信息: {}", headMap);
    }

    @Override
    public void invoke(MaterialYieldRatesExcel data, AnalysisContext context) {
        try {
            MaterialYieldRates entity = new MaterialYieldRates();
            // 直接复制属性
            BeanUtils.copyProperties(data, entity);
            
            list.add(entity);
            
            // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
            if (list.size() >= BATCH_COUNT) {
                saveData();
                list.clear();
            }
        } catch (Exception e) {
            log.error("处理第{}行数据时发生错误", context.readRowHolder().getRowIndex() + 1, e);
            throw new RuntimeException("Excel数据处理失败", e);
        }
    }
    
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        saveData();
        log.info("所有数据解析完成");
    }
    
    private void saveData() {
        if (list.isEmpty()) {
            return;
        }
        
        try {
            materialYieldRatesService.saveBatch(list, BATCH_COUNT);
            log.info("成功保存{}条数据", list.size());
        } catch (Exception e) {
            log.error("批量保存数据时发生错误", e);
            throw new RuntimeException("保存数据失败", e);
        }
    }
}