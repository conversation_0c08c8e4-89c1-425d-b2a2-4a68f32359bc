package com.nercar.ingredient.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 新算法接口配置类
 */
@Configuration
@ConfigurationProperties(prefix = "calculation.new-algorithm")
@Data
public class NewAlgorithmConfig {
    
    /**
     * 新算法接口基础URL
     */
    private String baseUrl = "http://49.4.23.163:1998";
    
    /**
     * 新算法接口端点
     */
    private String endpoint = "/cacluate";
    
    /**
     * 超时时间（毫秒）
     */
    private Integer timeout = 30000;
}
