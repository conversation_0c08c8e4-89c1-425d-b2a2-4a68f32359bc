package com.nercar.ingredient.domain.dto;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SubmitQueryDataDTO  extends BaseDTO{
    /**
     * 主表ID
     */
    @Schema(description = "主表ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private String id;


    /**
     * 成锭率集合
     */
    @Schema(description = "成锭率集合")
    private List<IngotYieldRatesDTO> ingotYieldRatesDTOList;

    /**
     * 成材率集合
     */
    @Schema(description = "成材率集合")
    private List<MaterialYieldRatesDTO> materialYieldRatesDTOList;

}
