package com.nercar.ingredient.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.ingredient.domain.dto.SteelGradesDTO;
import com.nercar.ingredient.domain.po.SteelGrades;
import com.nercar.ingredient.domain.vo.ProcessPathVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【steel_grades(钢种表)】的数据库操作Service
* @createDate 2025-04-01 13:55:28
*/
public interface SteelGradesService extends IService<SteelGrades> {

    IPage<ProcessPathVO> selectPageProcessPath(SteelGradesDTO steelGradesDTO,SteelGrades steelGrades);
}
