package com.nercar.ingredient.response;

import lombok.Setter;

/**
 * API返回码封装类
 */
public enum ResultCode implements IErrorCode {
    SUCCESS(200, "成功"),
    FAILED(400, "失败"),
    UNAUTHORIZED(401, "未登录或token已经过期"),
    PARAM_IS_INVALID(402, "参数无效"),
    FORBIDDEN(403, "没有相关权限"),
    NOT_FOUND(404, "资源不存在"),

    VALIDATE_FAILED(10000, "参数检验失败"),
    DATA_TYPE_ERROR(20000, "数据格式错误"),
    UNKNOWN_ERROR(40000, "未知错误,请联系管理员");

    @Setter
    private int code;
    private String message;

    ResultCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public IErrorCode setMessage(String msg) {
        this.message = msg;
        return this;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
