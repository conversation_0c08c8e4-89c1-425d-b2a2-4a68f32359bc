package com.nercar.ingredient.domain.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 标准配料模版表
 * @TableName standard_template
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StandardTemplateDTO  extends BaseDTO{
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 路径名称
     */
    private String pathName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 模板评级：1-5星级
     */
    @Schema(description = "模板评级：1-5星级")
    private Integer rating;

    /**
     *
     */
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    private String createuser;

    /**
     *
     */
    @TableField(value = "createtime",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     *
     */
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;



}