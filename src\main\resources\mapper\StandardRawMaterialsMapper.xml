<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.StandardRawMaterialsMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.StandardRawMaterials">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="element" column="element" jdbcType="VARCHAR"/>
            <result property="secondaryElement" column="secondary_element" jdbcType="VARCHAR"/>
            <result property="carbonElement" column="carbon_element" jdbcType="VARCHAR"/>
            <result property="departmentId" column="department_id" jdbcType="BIGINT"/>
            <result property="productionEquipmentId" column="production_equipment_id" jdbcType="BIGINT"/>
            <result property="composition" column="composition" jdbcType="NUMERIC"/>
            <result property="yieldRate" column="yield_rate" jdbcType="NUMERIC"/>
            <result property="price" column="price" jdbcType="NUMERIC"/>
            <result property="isCustom" column="is_custom" jdbcType="CHAR"/>
            <result property="category" column="category" jdbcType="VARCHAR"/>
            <result property="materialCode" column="material_code" jdbcType="VARCHAR"/>
            <result property="standardNo" column="standard_no" jdbcType="VARCHAR"/>
            <result property="createuser" column="createuser" jdbcType="VARCHAR"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,element,
        secondary_element,carbon_element,department_id,
        production_equipment_id,composition,yield_rate,
        price,is_custom,category,
        material_code,standard_no,
        createuser,createtime,updatetime
    </sql>
    <select id="selectMaterials" resultType="com.nercar.ingredient.domain.vo.StandardRawMaterialsVO">
        select
            srm.id,srm.name,srm.element,
            srm.secondary_element,srm.carbon_element,srm.department_id,
            srm.production_equipment_id,srm.composition,srm.yield_rate,
            srm.price,srm.is_custom,srm.category,
            srm.material_code,srm.standard_no,
            srm.createuser,srm.createtime,srm.updatetime
        from standard_raw_materials as srm
         ${ew.customSqlSegment}
    </select>
</mapper>
