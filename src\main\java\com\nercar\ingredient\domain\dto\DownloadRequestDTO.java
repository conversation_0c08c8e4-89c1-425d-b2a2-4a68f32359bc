package com.nercar.ingredient.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 下载测算表请求DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "下载测算表请求参数")
public class DownloadRequestDTO extends BaseDTO {
    
    /**
     * 配料记录ID列表（支持字符串格式，避免精度丢失）
     */
    @Schema(description = "配料记录ID列表，支持字符串格式避免精度丢失。单个ID时直接下载Excel，多个ID时下载ZIP压缩包",
            example = "[\"1234567890123456789\"]", required = true)
    private List<String> ids;

    /**
     * 获取Long类型的ID列表
     * @return Long类型的ID列表
     */
    public List<Long> getLongIds() {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }
        return ids.stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }
    
    /**
     * 文件名前缀（可选）
     */
    @Schema(description = "下载文件名前缀，用于批量下载时的ZIP文件命名",
            example = "测算表批量下载", required = false)
    private String fileNamePrefix;

    /**
     * 计算序号列表（可选）
     */
    @Schema(description = "计算序号列表，与ids一一对应。不传则使用最新批次",
            example = "[\"1\", \"2\"]", required = false)
    private List<String> calculationSequences;
}
