package com.nercar.ingredient.domain.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 成锭率
 * @TableName ingot_yield_rates
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IngotYieldRatesDTO extends BaseDTO {
    /**
     * 
     */
    @TableId
    @Schema(description = "主键")
    private Long id;

    /**
     * 工序
     */
    @Schema(description = "工序")
    private String processPath;

    /**
     * 设备
     */
    private String equipments;


    @Schema(description = "设备")
    private String device;


    /**
     * 更新用户
     */
    @Schema(description =  "更新用户")
    private String updateuser;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String departmentName;

    /**
     * 成锭率
     */
    @Schema(description = "成锭率")
    private BigDecimal ingotYield;

    /**
     *
     */
    @Schema(description =  "创建者")
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    private String createuser;

    /**
     *
     */
    @Schema(description =  "创建时间")
    @TableField(value = "createtime",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     *
     */
    @Schema(description =  "更新时间")
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;

    /**
     * 是否自定义(0=系统默认,1=用户自定义)
     */
    @Schema(description = "是否自定义(0=系统默认,1=用户自定义)")
    private Integer isCustom;

}