package com.nercar.ingredient.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

@Slf4j
@Component
public class ImageUtils {

    @Value("${image.upload.path:images}")
    private String imageUploadPath;

    /**
     * 保存图片到本地目录
     * @param file 上传的图片文件
     * @return 图片的访问URL
     */
    public String saveImage(MultipartFile file) throws IOException {
        if (file.isEmpty()) {
            throw new IOException("上传的文件为空");
        }

        // 获取文件名和扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new IOException("文件名为空");
        }

        // 检查文件类型
        String extension = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
        if (!isValidImageExtension(extension)) {
            throw new IOException("不支持的文件类型: " + extension);
        }

        // 创建上传目录
        Path uploadPath = Paths.get(imageUploadPath).toAbsolutePath();
        if (!Files.exists(uploadPath)) {
            try {
                Files.createDirectories(uploadPath);
            } catch (IOException e) {
                log.error("创建目录失败: {}", uploadPath, e);
                throw new IOException("无法创建上传目录");
            }
        }

        // 生成唯一文件名
        String filename = UUID.randomUUID().toString() + extension;
        Path filePath = uploadPath.resolve(filename);

        // 保存文件
        try {
            Files.copy(file.getInputStream(), filePath);
            log.info("文件保存成功: {}", filePath);
            return "/images/" + filename;
        } catch (IOException e) {
            log.error("保存文件失败: {}", filePath, e);
            throw new IOException("保存文件失败: " + e.getMessage());
        }
    }

    private boolean isValidImageExtension(String extension) {
        return extension.matches("\\.(jpg|jpeg|png|gif)$");
    }
}