package com.nercar.ingredient.domain.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IngredientResultIdsDTO extends BaseDTO{

    /**
     * standardIngredientRecordId 配料单ID
     */
    @Schema(description = "配料单ID")
    private Long standardIngredientRecordId;
    /**
     * calculationResultIds 计算结果ID集合
     */
    @Schema(description = "计算结果ID集合")
    private List<String> calculationResultIds;
}
