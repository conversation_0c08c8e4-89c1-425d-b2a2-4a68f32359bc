-- 配料模板评级功能验证脚本
-- 执行时间：2025-01-14

-- 1. 验证rating字段是否正确添加
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default,
    column_comment
FROM information_schema.columns 
WHERE table_name = 'standard_template' 
AND column_name = 'rating';

-- 2. 数据完整性检查
SELECT 
    '总模板数' as check_item,
    COUNT(*) as count
FROM standard_template
UNION ALL
SELECT 
    '有rating值的模板数',
    COUNT(*)
FROM standard_template 
WHERE rating IS NOT NULL
UNION ALL
SELECT 
    'rating为NULL的模板数',
    COUNT(*)
FROM standard_template 
WHERE rating IS NULL;

-- 3. 评级分布统计
SELECT 
    rating,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM standard_template), 2) as percentage
FROM standard_template 
GROUP BY rating 
ORDER BY rating;

-- 4. 按评级排序的模板列表（验证排序功能）
SELECT 
    id,
    path_name,
    department_name,
    rating,
    createtime,
    createuser
FROM standard_template 
ORDER BY rating DESC, createtime DESC
LIMIT 20;

-- 5. 验证评级范围是否合理（应该在1-5之间）
SELECT 
    'rating超出范围的记录数' as check_item,
    COUNT(*) as count
FROM standard_template 
WHERE rating < 1 OR rating > 5;

-- 6. 按部门统计评级分布
SELECT 
    department_name,
    rating,
    COUNT(*) as count
FROM standard_template 
GROUP BY department_name, rating
ORDER BY department_name, rating;

-- 7. 评级统计分析
SELECT 
    'rating统计' as analysis_type,
    MIN(rating) as min_rating,
    MAX(rating) as max_rating,
    ROUND(AVG(rating::numeric), 2) as avg_rating,
    COUNT(DISTINCT rating) as distinct_ratings
FROM standard_template;

-- 8. 高评级模板列表（4星及以上）
SELECT 
    path_name,
    department_name,
    rating,
    remark,
    createtime
FROM standard_template 
WHERE rating >= 4
ORDER BY rating DESC, createtime DESC;

-- 9. 低评级模板列表（2星及以下）
SELECT 
    path_name,
    department_name,
    rating,
    remark,
    createtime
FROM standard_template 
WHERE rating <= 2
ORDER BY rating ASC, createtime DESC;

-- 10. 验证索引是否创建成功
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'standard_template' 
AND indexname LIKE '%rating%';

-- 验证完成提示
SELECT 'SQL验证脚本执行完成' as status;
