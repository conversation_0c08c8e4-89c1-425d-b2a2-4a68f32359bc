package com.nercar.ingredient.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.ingredient.domain.dto.IngotYieldRatesDTO;
import com.nercar.ingredient.domain.dto.IngotYieldRatesPageDTO;
import com.nercar.ingredient.domain.dto.StandardRawMaterialsQueryDTO;
import com.nercar.ingredient.domain.po.IngotYieldRates;
import com.nercar.ingredient.domain.vo.StandardRawMaterialsVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ingot_yield_rates(成锭率)】的数据库操作Service
* @createDate 2025-04-01 13:55:28
*/
public interface IngotYieldRatesService extends IService<IngotYieldRates> {



    IPage<IngotYieldRates> getIngotYieldRatesPage(IngotYieldRatesPageDTO ingotYieldRatesPageDTO);

    List<Long> saveOrUpdateIngotYieldRate(List<IngotYieldRatesDTO> ingotYieldRatesDTOList,String id);
}
