package com.nercar.ingredient.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.dto.ContractReviewQueryDTO;
import com.nercar.ingredient.domain.po.ContractReviewInfoMerged;
import com.nercar.ingredient.domain.vo.ContractReviewInfoVO;
import com.nercar.ingredient.domain.vo.ContractReviewOptionVO;
import com.nercar.ingredient.mapper.ContractReviewInfoMergedMapper;
import com.nercar.ingredient.service.ContractReviewInfoMergedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【contract_review_info_merged(合同评审信息合并表)】的数据库操作Service实现
 * @createDate 2025-07-18 15:30:00
 */
@Slf4j
@Service
public class ContractReviewInfoMergedServiceImpl extends ServiceImpl<ContractReviewInfoMergedMapper, ContractReviewInfoMerged>
    implements ContractReviewInfoMergedService {

    @Override
    public ContractReviewInfoVO getContractReviewById(Long contractId) {
        log.info("根据合同ID查询合同详情（降级方案），contractId: {}", contractId);

        // 这个方法现在只用作降级方案，直接使用本地数据
        return getContractReviewByIdFromLocal(contractId);
    }

    /**
     * 从本地数据库查询合同详情（降级方案）
     */
    private ContractReviewInfoVO getContractReviewByIdFromLocal(Long contractId) {
        LambdaQueryWrapper<ContractReviewInfoMerged> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ContractReviewInfoMerged::getId, contractId)
               .eq(ContractReviewInfoMerged::getStatus, 1);

        ContractReviewInfoMerged entity = baseMapper.selectOne(wrapper);
        if (entity == null) {
            log.warn("本地数据库中也未找到合同ID为 {} 的合同信息", contractId);
            return null;
        }

        return BeanUtil.copyProperties(entity, ContractReviewInfoVO.class);
    }

    @Override
    public IPage<ContractReviewInfoVO> getContractReviewPage(ContractReviewQueryDTO queryDTO) {
        log.info("分页查询合同评审信息（降级方案），查询条件: {}", queryDTO);

        // 这个方法现在只用作降级方案，直接使用本地数据
        return getContractReviewPageFromLocal(queryDTO);
    }

    /**
     * 从本地数据库分页查询（降级方案）
     */
    private IPage<ContractReviewInfoVO> getContractReviewPageFromLocal(ContractReviewQueryDTO queryDTO) {
        LambdaQueryWrapper<ContractReviewInfoMerged> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(Objects.nonNull(queryDTO.getContractId()), ContractReviewInfoMerged::getId, queryDTO.getContractId())
               .like(StringUtils.hasText(queryDTO.getCode()), ContractReviewInfoMerged::getCode, queryDTO.getCode())
               .like(StringUtils.hasText(queryDTO.getCustomerName()), ContractReviewInfoMerged::getCustomerName, queryDTO.getCustomerName())
               .eq(StringUtils.hasText(queryDTO.getCustomerNameExact()), ContractReviewInfoMerged::getCustomerName, queryDTO.getCustomerNameExact())
               .eq(StringUtils.hasText(queryDTO.getSteelGradeNameExact()), ContractReviewInfoMerged::getSteelGradeName, queryDTO.getSteelGradeNameExact())
               .eq(StringUtils.hasText(queryDTO.getSteelTypeNameExact()), ContractReviewInfoMerged::getSteelTypeName, queryDTO.getSteelTypeNameExact())
               .eq(StringUtils.hasText(queryDTO.getCodeExact()), ContractReviewInfoMerged::getCode, queryDTO.getCodeExact())
               .ge(Objects.nonNull(queryDTO.getStartTime()), ContractReviewInfoMerged::getCreateTime, queryDTO.getStartTime())
               .le(Objects.nonNull(queryDTO.getEndTime()), ContractReviewInfoMerged::getCreateTime, queryDTO.getEndTime())
               .eq(ContractReviewInfoMerged::getStatus, 1)
               .orderByDesc(ContractReviewInfoMerged::getCreateTime);

        Page<ContractReviewInfoMerged> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        IPage<ContractReviewInfoMerged> result = baseMapper.selectPage(page, wrapper);

        return result.convert(entity -> BeanUtil.copyProperties(entity, ContractReviewInfoVO.class));
    }

    // ==================== 下拉框数据查询方法实现 ====================

    @Override
    public List<ContractReviewOptionVO> getCustomerNameOptions() {
        log.info("获取顾客名称下拉框选项（降级方案）");

        // 这个方法现在只用作降级方案，直接使用本地数据
        return getCustomerNameOptionsFromLocal();
    }

    /**
     * 从本地数据库获取客户名称选项（降级方案）
     */
    private List<ContractReviewOptionVO> getCustomerNameOptionsFromLocal() {
        QueryWrapper<ContractReviewInfoMerged> wrapper = new QueryWrapper<>();
        wrapper.select("DISTINCT customer_name, COUNT(*) as count")
               .eq("status", 1)
               .isNotNull("customer_name")
               .ne("customer_name", "")
               .groupBy("customer_name")
               .orderBy(true, false, "count");

        List<Map<String, Object>> results = baseMapper.selectMaps(wrapper);
        return results.stream()
                .map(map -> ContractReviewOptionVO.builder()
                        .value((String) map.get("customer_name"))
                        .label((String) map.get("customer_name"))
                        .count(((Number) map.get("count")).longValue())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<ContractReviewOptionVO> getSteelGradeNameOptions() {
        log.info("获取钢种下拉框选项（降级方案）");

        // 这个方法现在只用作降级方案，直接使用本地数据
        return getSteelGradeNameOptionsFromLocal();
    }

    /**
     * 从本地数据库获取钢种选项（降级方案）
     */
    private List<ContractReviewOptionVO> getSteelGradeNameOptionsFromLocal() {
        QueryWrapper<ContractReviewInfoMerged> wrapper = new QueryWrapper<>();
        wrapper.select("DISTINCT steel_grade_name, COUNT(*) as count")
               .eq("status", 1)
               .isNotNull("steel_grade_name")
               .ne("steel_grade_name", "")
               .groupBy("steel_grade_name")
               .orderBy(true, false, "count");

        List<Map<String, Object>> results = baseMapper.selectMaps(wrapper);
        return results.stream()
                .map(map -> ContractReviewOptionVO.builder()
                        .value((String) map.get("steel_grade_name"))
                        .label((String) map.get("steel_grade_name"))
                        .count(((Number) map.get("count")).longValue())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<ContractReviewOptionVO> getSteelTypeNameOptions() {
        log.info("获取钢类下拉框选项（降级方案）");

        // 这个方法现在只用作降级方案，直接使用本地数据
        return getSteelTypeNameOptionsFromLocal();
    }

    /**
     * 从本地数据库获取钢类选项（降级方案）
     */
    private List<ContractReviewOptionVO> getSteelTypeNameOptionsFromLocal() {
        QueryWrapper<ContractReviewInfoMerged> wrapper = new QueryWrapper<>();
        wrapper.select("DISTINCT steel_type_name, COUNT(*) as count")
               .eq("status", 1)
               .isNotNull("steel_type_name")
               .ne("steel_type_name", "")
               .groupBy("steel_type_name")
               .orderBy(true, false, "count");

        List<Map<String, Object>> results = baseMapper.selectMaps(wrapper);
        return results.stream()
                .map(map -> ContractReviewOptionVO.builder()
                        .value((String) map.get("steel_type_name"))
                        .label((String) map.get("steel_type_name"))
                        .count(((Number) map.get("count")).longValue())
                        .build())
                .collect(Collectors.toList());
    }
}
