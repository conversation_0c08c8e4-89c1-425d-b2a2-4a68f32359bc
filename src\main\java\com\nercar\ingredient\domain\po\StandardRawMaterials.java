package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 标准原料
 * @TableName standard_rwa_materials
 */
@TableName(value ="standard_raw_materials")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StandardRawMaterials extends BaseEntity {
    /**
     * 
     */
    @TableId
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 名称
     */
    @Schema(description = "原料名称")
    private String name;

//    /**
//     * 最新价格
//     */
//    private Integer newPrice;
    /**
     * 主元素
     */
    @Schema(description = "主元素")
    private String element;
    /**
     * 基准品味%
     */
    @Schema(description = "基准品味%")
    private BigDecimal composition;

    /**
     * 收得率%
     */
    @Schema(description = "收得率%")
    private BigDecimal yieldRate;

    /**
     * 优先级
     */
    @Schema(description = "优先级")
    private Integer priority;

    /**
     * 碳含量
     */
    @Schema(description = "碳含量")
    private Integer cContent;
    /**
     * 次元素
     */
    @Schema(description = "次元素")
    private String secondaryElement;

    /**
     * C元素
     */
    @Schema(description = "C元素")
    private String carbonElement;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private Long departmentId;

    /**
     * 生产设备ID
     */
    @Schema(description = "生产设备ID")
    private Long productionEquipmentId;

    @TableField(exist = false)
    private String productionEquipmentName;


    /**
     * 价格
     */
    @Schema(description = "价格")
    private BigDecimal price;

    /**
     * 是否自定义
     */
    @Schema(description = "是否自定义")
    private String isCustom;

    /**
     * 原料类别
     */
    @Schema(description = "原料类别")
    private String category;

    /**
     * 单耗
     */
    @Schema(description = "单耗")
    private String singleConsume;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String departmentName;


    /**
     *
     */
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    private String createuser;

    /**
     *
     */
    @TableField(value = "createtime",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     *
     */
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;

    /**
     * 物料代码
     */
    @Schema(description = "物料代码")
    private String materialCode;

    /**
     * 标准号
     */
    @Schema(description = "标准号")
    private String standardNo;

}