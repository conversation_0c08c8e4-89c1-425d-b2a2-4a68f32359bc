package com.nercar.ingredient.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 原料及固定重量数据传输对象
 * 用于计算接口中传递原料ID和用户设置的固定重量
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "原料及固定重量数据传输对象")
public class MaterialWithFixedWeight {
    
    /**
     * 原料ID
     */
    @Schema(description = "原料ID", required = true, example = "1234567890123456789")
    private String materialId;
    
    /**
     * 固定重量
     */
    @Schema(description = "用户设置的固定重量", required = true, example = "100.5")
    private BigDecimal fixedWeight;
}
