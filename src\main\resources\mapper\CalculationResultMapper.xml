<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.ingredient.mapper.CalculationResultMapper">

    <resultMap id="BaseResultMap" type="com.nercar.ingredient.domain.po.CalculationResult">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="standardIngredientRecordId" column="standard_ingredient_record_id" jdbcType="BIGINT"/>
            <result property="rawMaterialId" column="raw_material_id" jdbcType="BIGINT"/>
            <result property="rawMaterialName" column="raw_material_name" jdbcType="VARCHAR"/>
            <result property="purposeCompositionId" column="purpose_composition_id" jdbcType="BIGINT"/>
            <result property="composition" column="composition" jdbcType="NUMERIC"/>
            <result property="recoveryRate" column="recovery_rate" jdbcType="NUMERIC"/>
            <result property="wieght" column="wieght" jdbcType="NUMERIC"/>
            <result property="price" column="price" jdbcType="NUMERIC"/>
            <result property="calculationSequence" column="calculation_sequence" jdbcType="INTEGER"/>
            <result property="createuser" column="createuser" jdbcType="VARCHAR"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,standard_ingredient_record_id,raw_material_id,
        raw_material_name,purpose_composition_id,composition,
        recovery_rate,wieght,price,calculation_sequence,
        createuser,createtime,updatetime
    </sql>
    <select id="selectMaterialsByIds" resultType="com.nercar.ingredient.domain.vo.StandardRawMaterialsVO">
        select DISTINCT
        srm.id,
        cr.id calculation_result_id,
        srm.name,
        srm.composition,
        srm.yield_rate,
        srm.price,
        cr.wieght,
        pc.average_value,
        cr.single_consume
        from standard_raw_materials srm
        inner join calculation_result cr on srm.id = cr.raw_material_id
        left join purpose_compositions pc on cr.purpose_composition_id = pc.id
        where 1=1
        <if test="materialsIdList != null and materialsIdList.size() > 0">
            and cr.raw_material_id in
            <foreach collection="materialsIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="standardIngredientRecordId != null">
            and cr.standard_ingredient_record_id = #{standardIngredientRecordId}
        </if>
    </select>
    <select id="selectByCostId" resultType="com.nercar.ingredient.domain.po.CostEstimation">
        /* 查询成本测算记录，id = ${id} */
        select * from cost_estimation where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectRecentCalculationSequences" resultType="java.lang.Integer">
        /* 查询指定配料记录的计算序号（按时间顺序升序排列） */
        SELECT DISTINCT CAST(calculation_sequence AS INTEGER) as calculation_sequence
        FROM calculation_result
        WHERE standard_ingredient_record_id = #{standardIngredientRecordId}
          AND calculation_sequence IS NOT NULL
          AND calculation_sequence ~ '^[0-9]+$'
        ORDER BY CAST(calculation_sequence AS INTEGER) ASC
        LIMIT #{limit}
    </select>

    <select id="selectBySequence" resultMap="BaseResultMap">
        /* 根据配料记录ID和计算序号查询计算结果 */
        SELECT <include refid="Base_Column_List"/>
        FROM calculation_result
        WHERE standard_ingredient_record_id = #{standardIngredientRecordId}
          AND calculation_sequence = CAST(#{calculationSequence} AS VARCHAR)
        ORDER BY id
    </select>
</mapper>
