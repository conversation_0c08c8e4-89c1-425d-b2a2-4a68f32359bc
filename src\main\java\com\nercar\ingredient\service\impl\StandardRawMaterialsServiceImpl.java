package com.nercar.ingredient.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.dto.MaterialsCategoryDTO;
import com.nercar.ingredient.domain.dto.PathMaterialsQueryDTO;
import com.nercar.ingredient.domain.dto.StandardRawMaterialsDTO;
import com.nercar.ingredient.domain.dto.StandardRawMaterialsQueryDTO;
import com.nercar.ingredient.domain.po.*;
import com.nercar.ingredient.domain.vo.RawMaterialsCategoryVO;
import com.nercar.ingredient.domain.vo.StandardRawMaterialsVO;
import com.nercar.ingredient.mapper.*;
import com.nercar.ingredient.service.StandardRawMaterialsService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StandardRawMaterialsServiceImpl extends ServiceImpl<StandardRawMaterialsMapper, StandardRawMaterials>
        implements StandardRawMaterialsService {

    @Autowired
    private DepartmentsMapper departmentsMapper;
    @Autowired
    private ProductionEquipmentsMapper productionEquipmentsMapper;
    @Autowired
    private PathMaterialsMapper pathMaterialsMapper;
    @Autowired
    private ProcessPathStepsMapper processPathStepsMapper;
    @Autowired
    private TemplateEquipmentMappingMapper templateEquipmentMappingMapper;
    @Autowired
    private StandardTemplateMapper standardTemplateMapper;
    @Autowired
    private TemplateMaterialsMappingMapper templateMaterialsMappingMapper;
    @Autowired
    private StandardRawMaterialsMapper standardRawMaterialsMapper;

    @Override
    public List<StandardRawMaterialsVO> getStandardRawMaterials(List<StandardRawMaterialsDTO> standardRawMaterialsQueryDTOList) {
        //如果传入的集合中的所有元素的name都为空，则直接返回空集合为空，则返回所有数据
        if (standardRawMaterialsQueryDTOList == null||standardRawMaterialsQueryDTOList.stream().allMatch(dto -> StringUtils.isEmpty(dto.getName()))) {
            List<StandardRawMaterials> all = baseMapper.selectList(null);
//            //过滤重复名称
//            List<StandardRawMaterials> collect = all.stream().filter(dto -> StringUtils.hasText(dto.getName())).collect(Collectors.toList());
            return BeanUtil.copyToList(all, StandardRawMaterialsVO.class);
        }

        // 过滤无效 DTO 并提取唯一名称
        List<String> validNames = standardRawMaterialsQueryDTOList.stream()
                .filter(dto -> StringUtils.hasText(dto.getName()))
                .map(StandardRawMaterialsDTO::getName)
                .distinct()
                .collect(Collectors.toList());

        if (validNames.isEmpty()) {
            return Collections.emptyList();
        }
        // 合并查询条件（使用 or 连接多个 like 条件）
        LambdaQueryWrapper<StandardRawMaterials> wrapper = new LambdaQueryWrapper<>();
        validNames.forEach(name ->
                wrapper.or().like(StandardRawMaterials::getName, name)
        );

        List<StandardRawMaterials> results = baseMapper.selectList(wrapper);
        List<StandardRawMaterialsVO> vos = BeanUtil.copyToList(results, StandardRawMaterialsVO.class);
        return vos;
    }


    @Autowired
    private MaterialElementsMapper materialElementsMapper;
    @Override
    public IPage<StandardRawMaterialsVO> selectStandardRawMaterials(StandardRawMaterialsQueryDTO standardRawMaterialsQueryDTO) {
        //1、先判断参数是否为空
        //2、根据departmentName查询部门表，得到ID，由此得出第一个查询条件 departmentId
        Long departmentId = null;
        if (standardRawMaterialsQueryDTO.getDepartmentName().length() > 0){
            LambdaQueryWrapper<Departments> wrapper = new LambdaQueryWrapper<>();
            wrapper.like(Departments::getDepartmentName, standardRawMaterialsQueryDTO.getDepartmentName());
            Departments department = departmentsMapper.selectOne(wrapper);
            if (department != null){
                departmentId = department.getId();
            }
        }
        //3、根据name,category以及departmentId查询standardRawMaterials，得到原料数据
        LambdaQueryWrapper<StandardRawMaterials> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(departmentId), StandardRawMaterials::getDepartmentId, departmentId);
        wrapper.like(Objects.nonNull(standardRawMaterialsQueryDTO.getName()), StandardRawMaterials::getName, standardRawMaterialsQueryDTO.getName());
        wrapper.like(Objects.nonNull(standardRawMaterialsQueryDTO.getCategory()), StandardRawMaterials::getCategory, standardRawMaterialsQueryDTO.getCategory());
        Page<StandardRawMaterialsVO> page = new Page<>(standardRawMaterialsQueryDTO.getPageNo(), standardRawMaterialsQueryDTO.getPageSize());
        IPage<StandardRawMaterialsVO> result=baseMapper.selectMaterials(page, wrapper);
        //4.1、再根据原料数据中的equipmentId 查询productionEquipment表，得到生产设备名称，并赋值给standardRawMaterialsVO
        //4.2、再根据原料数据中的departmentId 查询departments表，得到部门名称，并赋值给standardRawMaterialsVO
        if (result != null){
            List<StandardRawMaterialsVO> records = result.getRecords();
            if (!CollectionUtils.isEmpty(records)){
                records.forEach(vo -> {
                    Long equipmentId = vo.getProductionEquipmentId();
                    Long departmentId1 = vo.getDepartmentId();
                    if (departmentId1 != null){
                        LambdaQueryWrapper<Departments> wrapper1 = new LambdaQueryWrapper<>();
                        wrapper1.eq(Departments::getId, departmentId1);
                        Departments departments = departmentsMapper.selectOne(wrapper1);
                        if (departments != null){
                            vo.setDepartmentName(departments.getDepartmentName());
                        }
                    }
                    if (equipmentId != null){
                        LambdaQueryWrapper<ProductionEquipments> wrapper1 = new LambdaQueryWrapper<>();
                        wrapper1.eq(ProductionEquipments::getId, equipmentId);
                        ProductionEquipments productionEquipment = productionEquipmentsMapper.selectOne(wrapper1);
                        if (productionEquipment != null){
                            vo.setEquipmentName(productionEquipment.getEquipmentName());
                        }
                    }
                    //根据原料id去原料元素表中找到所有元素信息，并赋值给standardRawMaterialsVO
                    Long id = vo.getId();
                    List<MaterialElements> materialElements = materialElementsMapper.selectList(new LambdaQueryWrapper<MaterialElements>().eq(MaterialElements::getMaterialId, id));
                    vo.setMaterialElementsList(materialElements);
                });
            }
        }

        return result;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertOrUpdate(StandardRawMaterialsDTO standardRawMaterialsDTO) {
        if (standardRawMaterialsDTO == null) {
            return false;
        }
        // 获取的参数是部门名称，设备名称以及原料基本信息
        // 1、先根据部门名称查询部门表，得到部门ID，并赋值给standardRawMaterialsDTO
        String departmentName = standardRawMaterialsDTO.getDepartmentName();
        if (departmentName.length() > 0){
            LambdaQueryWrapper<Departments> wrapper = new LambdaQueryWrapper<>();
            wrapper.like(Objects.nonNull(departmentName),Departments::getDepartmentName, departmentName);
            Departments departments = departmentsMapper.selectOne(wrapper);
            if (departments != null){
                standardRawMaterialsDTO.setDepartmentId(departments.getId());
            }
        }
        // 2、再根据设备名称查询设备表，得到设备ID，并赋值给standardRawMaterialsDTO
        String equipmentName = standardRawMaterialsDTO.getEquipmentName();
        if (equipmentName.length() > 0){
            LambdaQueryWrapper<ProductionEquipments> wrapper = new LambdaQueryWrapper<>();
            wrapper.like(Objects.nonNull(equipmentName), ProductionEquipments::getEquipmentName, equipmentName);
            ProductionEquipments productionEquipments = productionEquipmentsMapper.selectOne(wrapper);
            if (productionEquipments != null){
                standardRawMaterialsDTO.setProductionEquipmentId(productionEquipments.getId());
            }
        }
        // 3、根据standardRawMaterialsDTO创建StandardRawMaterials对象，并插入数据库
        StandardRawMaterials standardRawMaterials = BeanUtil.copyProperties(standardRawMaterialsDTO, StandardRawMaterials.class);
        // 4、查询是否有该条数据，如果有则更新，如果没有则插入
            Long id = standardRawMaterials.getId();
            if (id == null){
                baseMapper.insert( standardRawMaterials);
                Long id1 = standardRawMaterials.getId();
                // 5、插入数据成功后，再插入标准元素表
                List<MaterialElements> materialElementsList = standardRawMaterialsDTO.getMaterialElementsList();
                if( materialElementsList != null){
                    for (MaterialElements materialElements : materialElementsList) {
                        materialElementsMapper.insert(materialElements);
                    }
                }
                return save(standardRawMaterials);
            }
            LambdaQueryWrapper<StandardRawMaterials> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Objects.nonNull(id),StandardRawMaterials::getId, id);
            StandardRawMaterials getOne = baseMapper.selectOne(wrapper);
            standardRawMaterials.setId(getOne.getId());
        List<MaterialElements> materialElementsList = standardRawMaterialsDTO.getMaterialElementsList();
        if (materialElementsList != null){
            for (MaterialElements materialElements : materialElementsList) {
                materialElementsMapper.updateById(materialElements);
            }
        }
        return updateById(standardRawMaterials);
    }

    @Override
    public StandardRawMaterialsVO selectById(Long id) {
        // 1、先根据id查询standardRawMaterials表，得到原料数据
        StandardRawMaterials standardRawMaterials = getById(id);
        StandardRawMaterialsVO standardRawMaterialsVO = BeanUtil.copyProperties(standardRawMaterials, StandardRawMaterialsVO.class);
        // 2、再根据原料数据中的equipmentId 查询productionEquipment表，得到生产设备名称，并赋值给standardRawMaterialsVO
        if (standardRawMaterials != null){
            Long equipmentId = standardRawMaterials.getProductionEquipmentId();
            if (equipmentId != null){
                LambdaQueryWrapper<ProductionEquipments> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ProductionEquipments::getId, equipmentId);
                ProductionEquipments productionEquipment = productionEquipmentsMapper.selectOne(wrapper);
                if (productionEquipment != null){
                    standardRawMaterialsVO.setEquipmentName(productionEquipment.getEquipmentName());
                }
            }
        }
        // 3、再根据原料数据中的departmentId 查询departments表，得到部门名称，并赋值给standardRawMaterialsVO
        if (standardRawMaterials != null){
            Long departmentId = standardRawMaterials.getDepartmentId();
            if (departmentId != null){
                LambdaQueryWrapper<Departments> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(Departments::getId, departmentId);
                Departments departments = departmentsMapper.selectOne(wrapper);
                if (departments != null){
                    standardRawMaterialsVO.setDepartmentName(departments.getDepartmentName());
                }
            }
        }
        // 4、根据原料id去查询materialElements表，得到当前原料对应的所有数据，再设置给standardRawMaterialsVO
        List<MaterialElements> materialElements = materialElementsMapper.selectList(new LambdaQueryWrapper<MaterialElements>().eq(MaterialElements::getMaterialId, id));
        standardRawMaterialsVO.setMaterialElementsList(materialElements);
        // 5、返回standardRawMaterialsVO
        return standardRawMaterialsVO;
    }

    @Override
    public List<StandardRawMaterialsVO> getStandardRawMaterialsById(Long id) {
        // 1、拿到的id是自定义保存的路径id,目前没有关联原料，所以要找到设备，根据设备id对应的模板带出来一批原料
        // 2、根据路径id找到所有工序，判断是否是模板，工序类别1代表设备，2代表模板
//        ProcessPathSteps processPathSteps = processPathStepsMapper.selectOne(new LambdaQueryWrapper<ProcessPathSteps>().eq(ProcessPathSteps::getPathId, id));
        LambdaQueryWrapper<ProcessPathSteps> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(ProcessPathSteps::getPathId, id);
        List<ProcessPathSteps> processPathSteps = processPathStepsMapper.selectList(wrapper2);
        // 3、遍历工序判断工序类别是否是设备，1代表设备，2代表模板
        for (ProcessPathSteps processPathStep : processPathSteps) {
            if(processPathStep.getType() == 2){
                //代表是模板，根据当前工序对应的设备id,设备id就是模板id
                Long equipmentId = processPathStep.getEquipmentId();
                // 4、通过设备id找到对应的模板id
                Long templateId=equipmentId;
                // 5、根据模板id找到所有的原料
                // 5.1拿到模板id后先去模板表查询，防止模板被删除后，却依然有记录
                StandardTemplate standardTemplate = standardTemplateMapper.selectOne(new LambdaQueryWrapper<StandardTemplate>().eq(StandardTemplate::getId, templateId));
                // 5.2如果模板不为空，则可以去模板原料对应表中拿到所有的原料id
                if (standardTemplate != null) {
                    List<TemplateMaterialsMapping> templateMaterialsMappings = templateMaterialsMappingMapper.selectList(new LambdaQueryWrapper<TemplateMaterialsMapping>().eq(TemplateMaterialsMapping::getStandardTemplateId, templateId));
                    // 5.4拿到所有的原料id，去标准原料表中拿到所有的原料
                    List<StandardRawMaterialsVO> standardRawMaterials = standardRawMaterialsMapper.selectList(new LambdaQueryWrapper<StandardRawMaterials>().in(StandardRawMaterials::getId, templateMaterialsMappings.stream().map(TemplateMaterialsMapping::getRawMaterialId).collect(Collectors.toList()))).stream().map( standardRawMaterials1 -> {
                        StandardRawMaterialsVO standardRawMaterialsVO = new StandardRawMaterialsVO();
                        BeanUtils.copyProperties(standardRawMaterials1, standardRawMaterialsVO);
                        return standardRawMaterialsVO;
                    }).collect(Collectors.toList());
                    return standardRawMaterials;
                }
            }
        }
        // 1、根据路径id查询path_materials表，找到所有的原料id
        LambdaQueryWrapper<PathMaterials> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PathMaterials::getPathId, id);
        List<PathMaterials> pathMaterials = pathMaterialsMapper.selectList(wrapper);
        if (pathMaterials.isEmpty()){
            return null;
        }
        List<Long> rawMaterialIds = pathMaterials.stream().map(PathMaterials::getRawMaterialId).collect(Collectors.toList());
        // 2、根据所有的原料id查询standardRawMaterials表，得到所有的原料数据
        List<StandardRawMaterials> standardRawMaterials = baseMapper.selectBatchIds(rawMaterialIds);
        // 3、封装数据返回
        List<StandardRawMaterialsVO> result = BeanUtil.copyToList(standardRawMaterials, StandardRawMaterialsVO.class);
        return result;
    }

    @Override
    public List<RawMaterialsCategoryVO> getCustomizePathRawMaterials(MaterialsCategoryDTO materialsCategoryDTO) {
        LambdaQueryWrapper<StandardRawMaterials> wrapper1 = new LambdaQueryWrapper<>();
        if(materialsCategoryDTO.getIsCustom()!=null && materialsCategoryDTO.getCategory()!=null){
            //如果materialsCategoryDTO里边的是否自定义为否，并且category是返回钢，则返回所有不是自定义的且是返回钢的原料
            if (materialsCategoryDTO.getIsCustom().equals("否") && materialsCategoryDTO.getCategory().equals("返回钢")){
                wrapper1.eq(StandardRawMaterials::getIsCustom, "否");
                wrapper1.eq(StandardRawMaterials::getName, "返回钢");
            }
            //如果materialsCategoryDTO里边的是否自定义为是，则返回所有是自定义原料并且类别为category
            else if (materialsCategoryDTO.getIsCustom().equals("是")){
                wrapper1.eq(StandardRawMaterials::getIsCustom, "是");
                wrapper1.eq(StandardRawMaterials::getCategory, materialsCategoryDTO.getCategory());
            }else {
                //如果materialsCategoryDTO里边的是否自定义为否，并且category为空，则返回所有不是自定义的且不是返回钢的原料
                wrapper1.eq(StandardRawMaterials::getIsCustom, "否");
                wrapper1.ne(StandardRawMaterials::getName, "返回钢");
            }
        }
        // 1、查询出原料表中所有的类别并且去重
        List<String> categoryList = baseMapper.selectList(wrapper1)
                .stream().map(StandardRawMaterials::getCategory)
                .distinct().collect(Collectors.toList());
        // 2、遍历类别，根据类别去查询原料名称，并且放到类别对应的集合中
        List<RawMaterialsCategoryVO> result = categoryList.stream().map(category -> {
            // 2.1、根据类别查询原料名称
            LambdaQueryWrapper<StandardRawMaterials> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StandardRawMaterials::getCategory, category);
            List<StandardRawMaterials> standardRawMaterials = baseMapper.selectList(wrapper);

            // 2.2、生成包含关键区分信息的显示名称列表
            List<String> nameList = standardRawMaterials.stream()
                    .map(material -> {
                        // 获取设备名称
                        String equipmentName = getEquipmentName(material.getProductionEquipmentId());

                        // 生成包含关键信息的显示名称
                        return String.format("%s[%s-收得率%.1f%%]",
                                material.getName(),
                                equipmentName,
                                material.getYieldRate() != null ? material.getYieldRate() : 0.0);
                    })
                    .collect(Collectors.toList());

            // 2.3、构建 RawMaterialsCategoryVO 对象
            return RawMaterialsCategoryVO.builder()
                    .category(category)
                    .nameList(nameList)
                    .build();
        }).collect(Collectors.toList());
        return result;
    }

    // 辅助方法：获取设备名称
    private String getEquipmentName(Long equipmentId) {
        if (equipmentId == null) return "未知设备";

        LambdaQueryWrapper<ProductionEquipments> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductionEquipments::getId, equipmentId);
        ProductionEquipments equipment = productionEquipmentsMapper.selectOne(wrapper);

        return equipment != null ? equipment.getEquipmentName() : "未知设备";
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateStandardRawMaterials(PathMaterialsQueryDTO pathMaterialsQueryDTO) {
        // 1、拿到的参数据路径ID以及路径对应的原料ID集合
        Long pathId = pathMaterialsQueryDTO.getPathId();
        List<Long> rawMaterialIds = pathMaterialsQueryDTO.getRawMaterialIds();

        // 2、所以要先根据路径ID去path_materials中间表里面查询所有原料ID
        LambdaQueryWrapper<PathMaterials> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PathMaterials::getPathId, pathId);
        List<PathMaterials> existingPathMaterials = pathMaterialsMapper.selectList(wrapper);

        // 3、然后判断一下返回的集合是否为空，如果不为空，就执行更新操作
        if (!CollectionUtils.isEmpty(existingPathMaterials)) {
            // 4、更新操作就是先把路径ID对应的原料ID删除，然后再插入新的数据
            LambdaQueryWrapper<PathMaterials> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(PathMaterials::getPathId, pathId);
            pathMaterialsMapper.delete(deleteWrapper);
        }

        // 5、插入新的数据
        for (Long rawMaterialId : rawMaterialIds) {
            PathMaterials newPathMaterial = new PathMaterials();
            newPathMaterial.setPathId(pathId);
            newPathMaterial.setRawMaterialId(rawMaterialId);
            pathMaterialsMapper.insert(newPathMaterial);
        }
    }

    @Override
    public List<StandardRawMaterialsVO> getStandardRawMaterialsByTemplateId(Long id) {

        // 1、拿到的id是自定义保存的路径id,目前没有关联原料，所以要找到设备，根据设备id对应的模板带出来一批原料
        // 2、根据路径id找到所有工序，判断是否是模板，工序类别1代表设备，2代表模板
//        ProcessPathSteps processPathSteps = processPathStepsMapper.selectOne(new LambdaQueryWrapper<ProcessPathSteps>().eq(ProcessPathSteps::getPathId, id));
        LambdaQueryWrapper<ProcessPathSteps> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProcessPathSteps::getPathId, id);
        List<ProcessPathSteps> processPathSteps = processPathStepsMapper.selectList(wrapper);
        // 3、遍历工序判断工序类别是否是设备，1代表设备，2代表模板
        for (ProcessPathSteps processPathStep : processPathSteps) {
            if(processPathStep.getType() == 2){
                //代表是模板，根据当前工序对应的设备id,设备id就是模板id
                Long equipmentId = processPathStep.getEquipmentId();
                // 4、通过设备id找到对应的模板id
                Long templateId=equipmentId;
                // 5、根据模板id找到所有的原料
                // 5.1拿到模板id后先去模板表查询，防止模板被删除后，却依然有记录
                StandardTemplate standardTemplate = standardTemplateMapper.selectOne(new LambdaQueryWrapper<StandardTemplate>().eq(StandardTemplate::getId, templateId));
                // 5.2如果模板不为空，则可以去模板原料对应表中拿到所有的原料id
                if (standardTemplate != null) {
                    List<TemplateMaterialsMapping> templateMaterialsMappings = templateMaterialsMappingMapper.selectList(new LambdaQueryWrapper<TemplateMaterialsMapping>().eq(TemplateMaterialsMapping::getStandardTemplateId, templateId));
                    // 5.4拿到所有的原料id，去标准原料表中拿到所有的原料
                    List<StandardRawMaterialsVO> standardRawMaterials = standardRawMaterialsMapper
                            .selectList(new LambdaQueryWrapper<StandardRawMaterials>()
                                    .in(StandardRawMaterials::getId, templateMaterialsMappings.stream()
                                            .map(TemplateMaterialsMapping::getRawMaterialId)
                                            .collect(Collectors.toList())))
                            .stream().map( standardRawMaterials1 -> {
                        StandardRawMaterialsVO standardRawMaterialsVO = new StandardRawMaterialsVO();
                        BeanUtils.copyProperties(standardRawMaterials1, standardRawMaterialsVO);
                        return standardRawMaterialsVO;
                    }).collect(Collectors.toList());
                    return standardRawMaterials;
                }
            }
        }
        return null;
    }

}
