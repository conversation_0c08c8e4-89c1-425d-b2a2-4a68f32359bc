package com.nercar.ingredient.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 最终成分结果类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class CompositionResult {
    /**
     * 元素
     */
    private String element;
    
    /**
     * 成分含量
     */
    private Double composition;
}
