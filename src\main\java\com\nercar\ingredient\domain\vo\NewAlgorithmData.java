package com.nercar.ingredient.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 新算法接口数据类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class NewAlgorithmData {
    /**
     * 原料总重量
     */
    private Double rawMaterialWeight;
    
    /**
     * 结果重量
     */
    private Double resultWeight;
    
    /**
     * 总成本
     */
    private Double totalCost;
    
    /**
     * 选中的原料
     */
    private List<SelectedMaterial> selectedMaterials;
    
    /**
     * 最终成分
     */
    private List<CompositionResult> compositions;
}
