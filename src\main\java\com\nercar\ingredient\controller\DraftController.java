package com.nercar.ingredient.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.nercar.ingredient.domain.dto.StandardIngredientRecordsQueryDTO;
import com.nercar.ingredient.domain.vo.StandardIngredientRecordsVO;
import com.nercar.ingredient.response.PageDataResult;
import com.nercar.ingredient.service.StandardIngredientRecordsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@Tag(name = "草稿箱接口")
@RestController
@RequestMapping("/draft")
public class DraftController {


    @Autowired
    private StandardIngredientRecordsService standardIngredientRecordsService;

    @Operation(summary = "分页查询草稿箱")
    @PostMapping("/getStandardIngredientRecords")
    public PageDataResult<StandardIngredientRecordsVO> getStandardIngredientRecords(@RequestBody StandardIngredientRecordsQueryDTO standardIngredientRecordsQueryDTO) {
        log.info("分页查询草稿箱");
        IPage< StandardIngredientRecordsVO> result =standardIngredientRecordsService.selectDraft(standardIngredientRecordsQueryDTO);
        List<StandardIngredientRecordsVO> records = result.getRecords();
        long total = result.getTotal();
        if (ObjectUtils.isEmpty(records)) {
            log.warn("未找到符合给定条件的草稿"); // 添加日志记录
            return PageDataResult.success(null, 0);
        }
        return PageDataResult.success(records, total);
    }


}
