21:48:19.779 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 70016 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
21:48:19.808 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
21:48:19.809 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
21:48:26.015 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
21:48:27.540 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
21:48:27.541 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
21:48:27.542 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
21:48:27.709 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
21:48:28.135 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
21:48:28.225 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@7a7931a2, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
21:48:28.604 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
21:48:28.636 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
21:48:28.661 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
21:48:28.687 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
21:48:28.710 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
21:48:28.732 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
21:48:28.779 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
21:48:28.790 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
21:48:28.791 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
21:48:28.797 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
21:48:28.824 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
21:48:28.849 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
21:48:28.876 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
21:48:28.905 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
21:48:28.942 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
21:48:28.974 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
21:48:29.114 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
21:48:29.153 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
21:48:29.226 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
21:48:29.257 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
21:48:29.273 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
21:48:29.273 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
21:48:29.285 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
21:48:29.299 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
21:48:29.299 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
21:48:29.306 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
21:48:29.338 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
21:48:29.371 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
21:48:29.583 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
21:48:29.595 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
21:48:29.604 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:11
21:48:31.770 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
21:48:31.804 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 13.091 seconds (process running for 21.499)
21:48:31.814 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
21:49:03.727 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:49:04.793 [http-nio-9090-exec-10] INFO  o.s.a.AbstractOpenApiResource - [getOpenApi,390] - Init duration for springdoc-openapi is: 489 ms
21:49:14.693 [http-nio-9090-exec-3] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
21:51:16.853 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
22:15:21.840 [http-nio-9090-exec-6] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
22:15:37.295 [http-nio-9090-exec-10] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
22:15:38.072 [http-nio-9090-exec-8] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
22:16:24.718 [http-nio-9090-exec-7] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
22:45:16.479 [http-nio-9090-exec-9] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
22:45:28.888 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
22:46:18.009 [http-nio-9090-exec-5] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
22:46:44.691 [http-nio-9090-exec-4] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
22:46:54.837 [http-nio-9090-exec-3] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
22:47:02.980 [http-nio-9090-exec-6] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
22:47:03.637 [http-nio-9090-exec-10] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
22:47:43.573 [http-nio-9090-exec-8] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
22:50:33.660 [http-nio-9090-exec-2] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
22:50:34.571 [http-nio-9090-exec-9] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
22:50:36.840 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
22:51:23.060 [Thread-5] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
22:51:23.546 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 70016 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
22:51:23.546 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
22:51:23.546 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
22:51:24.571 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
22:51:24.572 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:51:24.573 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
22:51:24.596 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:51:24.667 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
22:51:24.695 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@77d1335a, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
22:51:24.713 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
22:51:24.726 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
22:51:24.739 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
22:51:24.753 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
22:51:24.764 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
22:51:24.798 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
22:51:24.838 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
22:51:24.843 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
22:51:24.844 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
22:51:24.854 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
22:51:24.874 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
22:51:24.887 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
22:51:24.901 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
22:51:24.912 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
22:51:24.924 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
22:51:24.937 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
22:51:24.964 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
22:51:24.974 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
22:51:24.993 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
22:51:25.006 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
22:51:25.011 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
22:51:25.013 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
22:51:25.022 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
22:51:25.028 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
22:51:25.030 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
22:51:25.038 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
22:51:25.075 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
22:51:25.149 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
22:51:25.339 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
22:51:25.348 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
22:51:25.362 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:11
22:51:25.891 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
22:51:25.899 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 2.396 seconds (process running for 3795.594)
22:51:25.905 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
22:51:30.066 [http-nio-9090-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:51:30.070 [http-nio-9090-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [logException,247] - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.]
22:51:30.403 [Thread-7] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9090"]
22:51:30.617 [restartedMain] INFO  c.n.i.Application - [logStarting,50] - Starting Application using Java ******** with PID 70016 (E:\fushun\fushun-ingredient\target\classes started by Dell in E:\fushun\fushun-ingredient)
22:51:30.617 [restartedMain] DEBUG c.n.i.Application - [logStarting,51] - Running with Spring Boot v3.3.9, Spring v6.1.17
22:51:30.618 [restartedMain] INFO  c.n.i.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
22:51:31.061 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9090"]
22:51:31.062 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:51:31.062 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.36]
22:51:31.080 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:51:31.119 [restartedMain] DEBUG o.a.i.l.LogFactory - [setImplementation,112] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
22:51:31.136 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@6c9a0648, overflow=false, maxLimit=null, dbType=POSTGRE_SQL, dialect=null, optimizeJoin=true)]}'
22:51:31.146 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CalculationResultMapper.xml]'
22:51:31.155 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\CostEstimationMapper.xml]'
22:51:31.166 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\DepartmentsMapper.xml]'
22:51:31.177 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ExecutionStandardMapper.xml]'
22:51:31.186 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngotYieldRatesMapper.xml]'
22:51:31.194 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientIdingotResultMapper.xml]'
22:51:31.201 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\IngredientYieldResultMapper.xml]'
22:51:31.203 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.PathMaterials".
22:51:31.204 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.PathMaterials ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
22:51:31.209 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PathMaterialsMapper.xml]'
22:51:31.221 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathMapper.xml]'
22:51:31.232 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProcessPathStepsMapper.xml]'
22:51:31.243 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionEquipmentsMapper.xml]'
22:51:31.253 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\ProductionFactoryMapper.xml]'
22:51:31.265 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\PurposeCompositionsMapper.xml]'
22:51:31.277 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardCompositionsMapper.xml]'
22:51:31.293 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardIngredientRecordsMapper.xml]'
22:51:31.305 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardRawMaterialsMapper.xml]'
22:51:31.320 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\StandardTemplateMapper.xml]'
22:51:31.338 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradesMapper.xml]'
22:51:31.344 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.SteelGradeStandardMapping".
22:51:31.345 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.SteelGradeStandardMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
22:51:31.352 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\SteelGradeStandardMappingMapper.xml]'
22:51:31.359 [restartedMain] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,364] - Can not find table primary key in Class: "com.nercar.ingredient.domain.po.TemplateEquipmentMapping".
22:51:31.359 [restartedMain] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,56] - class com.nercar.ingredient.domain.po.TemplateEquipmentMapping ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
22:51:31.368 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateEquipmentMappingMapper.xml]'
22:51:31.378 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\TemplateMaterialsMappingMapper.xml]'
22:51:31.386 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - [debug,49] - Parsed mapper file: 'file [E:\fushun\fushun-ingredient\target\classes\mapper\UsersMapper.xml]'
22:51:31.525 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,155] - Get /************** network interface 
22:51:31.534 [restartedMain] DEBUG c.b.m.c.t.Sequence - [getDatacenterId,159] - Get network interface info: name:wlan2 (Intel(R) Wireless-AC 9560 160MHz)
22:51:31.541 [restartedMain] DEBUG c.b.m.c.t.Sequence - [initLog,103] - Initialization Sequence datacenterId:1 workerId:11
22:51:31.975 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9090"]
22:51:31.983 [restartedMain] INFO  c.n.i.Application - [logStarted,56] - Started Application in 1.398 seconds (process running for 3801.678)
22:51:31.985 [restartedMain] INFO  c.n.i.Application - [main,26] - 
----------------------------------------------------------
	Application is running! Access URLs:
	本地: 	http://localhost:9090/doc.html
	局域网: 	http://*************:9090/
	Knife4j文档: 	http://*************:9090/doc.html
swagger-ui: 	http://*************:9090/swagger-ui.html
----------------------------------------------------------
