package com.nercar.ingredient.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.dto.StandardCompositionsDTO;
import com.nercar.ingredient.domain.dto.StandardCompositionsQueryDTO;
import com.nercar.ingredient.domain.dto.StandardRawMaterialsIdListDTO;
import com.nercar.ingredient.domain.dto.SteelGradesDTO;
import com.nercar.ingredient.domain.po.*;
import com.nercar.ingredient.domain.vo.CalculationResultVO;
import com.nercar.ingredient.domain.vo.StandardCompositionsVO;
import com.nercar.ingredient.mapper.CalculationResultMapper;
import com.nercar.ingredient.mapper.PurposeCompositionsMapper;
import com.nercar.ingredient.mapper.StandardCompositionsMapper;
import com.nercar.ingredient.mapper.StandardRawMaterialsMapper;
import com.nercar.ingredient.service.ExecutionStandardService;
import com.nercar.ingredient.service.StandardCompositionsService;
import com.nercar.ingredient.service.SteelGradeStandardMappingService;
import com.nercar.ingredient.service.SteelGradesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【standard_compositions(标准成分)】的数据库操作Service实现
* @createDate 2025-04-01 13:55:28
*/
@Service
public class StandardCompositionsServiceImpl extends ServiceImpl<StandardCompositionsMapper, StandardCompositions>
    implements StandardCompositionsService{
    @Autowired
    private ExecutionStandardService executionStandardService;
    @Autowired
    private SteelGradesService steelGradesService;
    @Autowired
    private SteelGradeStandardMappingService steelGradeStandardMappingService;
    @Autowired
    private CalculationResultMapper calculationResultMapper;
    @Autowired
    private PurposeCompositionsMapper purposeCompositionsMapper;
    @Autowired
    private StandardRawMaterialsMapper standardRawMaterialsMapper;

    @Override
    public List<StandardCompositionsVO> getStandardCompositions(StandardCompositionsQueryDTO standardCompositionsQueryDTO) {
        if (StringUtils.isEmpty(standardCompositionsQueryDTO.getStandardName())) {
            throw new RuntimeException("请输入标准名称");
        }

        //根据standardName查询执行标准ID
        LambdaQueryWrapper<ExecutionStandard> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.hasLength(standardCompositionsQueryDTO.getStandardName()), ExecutionStandard::getStandardName, standardCompositionsQueryDTO.getStandardName());
        ExecutionStandard executionStandard = executionStandardService.getOne(wrapper);
        if (executionStandard == null) {
            throw new RuntimeException("未找到对应的执行标准，标准名称：" + standardCompositionsQueryDTO.getStandardName());
        }
        Long standardId = executionStandard.getId();
        //根据ID查询一系列标准成分
        LambdaQueryWrapper<StandardCompositions> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(StandardCompositions::getStandardId, standardId);
        List<StandardCompositions> standardCompositions = baseMapper.selectList(wrapper2);
        return BeanUtil.copyToList(standardCompositions, StandardCompositionsVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveStandardCompositions(StandardCompositionsQueryDTO standardCompositionsQueryDTO) {
        // 1、接受的参数是自定义的标准名称，以及这个标准下所对应的标准成分
        String standardName = standardCompositionsQueryDTO.getStandardName();
        if (StringUtils.isEmpty(standardName)) {
            throw new RuntimeException("标准名称不能为空");
        }

        // 2、首先检测标准名称是否存在，如果存在抛出异常提示名称已存在，请重新输入
        LambdaQueryWrapper<ExecutionStandard> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExecutionStandard::getStandardName, standardName);
        ExecutionStandard existingStandard = executionStandardService.getOne(wrapper);
        if (existingStandard != null) {
            throw new RuntimeException("标准名称已存在，请重新输入");
        }

        // 3、之后将标准名称保存到标准表，并且获取标准ID
        ExecutionStandard newStandard = new ExecutionStandard();
        newStandard.setStandardName(standardName);
        executionStandardService.save(newStandard);

        // 通过查询获取刚插入的记录的主键 ID
        LambdaQueryWrapper<ExecutionStandard> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExecutionStandard::getStandardName, standardName);
        ExecutionStandard savedStandard = executionStandardService.getOne(queryWrapper);
        Long standardId = savedStandard.getId();

        // 4、拿到标准ID后，再加上标准成分去保存到标准成分表中
        List<StandardCompositionsDTO> standardCompositionsList = standardCompositionsQueryDTO.getStandardCompositionsList();
        if (standardCompositionsList != null && !standardCompositionsList.isEmpty()) {
            for (StandardCompositionsDTO dto : standardCompositionsList) {
                StandardCompositions standardCompositions = new StandardCompositions();
                standardCompositions.setStandardId(standardId);
                BeanUtil.copyProperties(dto, standardCompositions);
                baseMapper.insert(standardCompositions);
            }
        }

        // 返回执行标准的ID
        return standardId;
    }

    @Override
    public List<StandardCompositionsVO> getSteelGradesCompositions(SteelGradesDTO steelGradesDTO) {
        // 1. 校验钢种参数
        if (StringUtils.isEmpty(steelGradesDTO.getSteelGrade())) {
            throw new RuntimeException("请输入钢种");
        }
        // 2. 根据钢种查询钢种 ID
        LambdaQueryWrapper<SteelGrades> steelGradesWrapper = new LambdaQueryWrapper<>();
        steelGradesWrapper.eq(SteelGrades::getSteelGrade, steelGradesDTO.getSteelGrade());
        SteelGrades steelGrades = steelGradesService.getOne(steelGradesWrapper);
        if (steelGrades == null) {
            throw new RuntimeException("钢种不存在");
        }
        Long steelGradeId = steelGrades.getId();

       // 3. 根据钢种 ID 查询中间表的第一条标准 ID（按标准 ID 升序取第一条）
        LambdaQueryWrapper<SteelGradeStandardMapping> mappingWrapper = new LambdaQueryWrapper<>();
        mappingWrapper.eq(SteelGradeStandardMapping::getSteelGradeId, steelGradeId)
                .orderByAsc(SteelGradeStandardMapping::getStandardId); // 可根据实际需求调整排序字段（如时间字段）
        List<SteelGradeStandardMapping> mappings = steelGradeStandardMappingService.list(mappingWrapper);
        if (mappings == null || mappings.isEmpty()) {
            return null;
        }
        Long standardId = mappings.get(0).getStandardId();

        // 4. 根据标准 ID 查询标准成分（假设此处从执行标准表关联成分，需根据实际表结构调整）
        LambdaQueryWrapper<StandardCompositions> compositionsWrapper = new LambdaQueryWrapper<>();
        compositionsWrapper.eq(StandardCompositions::getStandardId, standardId);
        List<StandardCompositions> compositions = baseMapper.selectList(compositionsWrapper);
        if (compositions == null) {
            throw new RuntimeException("标准成分不存在");
        }
        // 5、返回最新的标准成分，转为VO
        List<StandardCompositionsVO> standardCompositionsVOS = BeanUtil.copyToList(compositions, StandardCompositionsVO.class);
        return standardCompositionsVOS;
    }

    @Override
    public List<StandardCompositionsVO> getStandardCompositionsById(Long id) {
        // 拿到的是执行标准的ID
        // 根据执行标准ID去标准成分表查找一系列标准成分
        LambdaQueryWrapper<StandardCompositions> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardCompositions::getStandardId, id);
        List<StandardCompositions> standardCompositions = baseMapper.selectList(wrapper);
        return BeanUtil.copyToList(standardCompositions, StandardCompositionsVO.class);
    }

    @Override
    public List<CalculationResultVO> getCalculationResult(StandardRawMaterialsIdListDTO standardRawMaterialsIdList) {
        // 1、拿到的数据是一系列的计算结果ID
        List<Long> calculationResultIds = standardRawMaterialsIdList.getIds();
        if (calculationResultIds == null || calculationResultIds.isEmpty()) {
            throw new RuntimeException("计算结果ID列表不能为空");
        }

        // 2、根据计算结果ID列表查询calculation_result表，获取计算结果数据
        List<CalculationResult> calculationResults = calculationResultMapper.selectBatchIds(calculationResultIds);
        if (calculationResults == null || calculationResults.isEmpty()) {
            throw new RuntimeException("未找到对应的计算结果");
        }

        // 3、遍历计算结果，分别查询standard_raw_materials表和purpose_compositions表
        List<CalculationResultVO> resultVOs = new ArrayList<>();
        for (CalculationResult calculationResult : calculationResults) {
            // 4、根据计算结果表中的原料ID查询standard_raw_materials表，获取价格
            StandardRawMaterials rawMaterial = standardRawMaterialsMapper.selectById(calculationResult.getRawMaterialId());
            if (rawMaterial == null) {
                throw new RuntimeException("未找到对应的原料信息");
            }

            // 5、根据计算结果表中的目标成分ID查询purpose_compositions表，获取minValue和maxValue
            PurposeCompositions purposeComposition = purposeCompositionsMapper.selectById(calculationResult.getPurposeCompositionId());
            if (purposeComposition == null) {
                throw new RuntimeException("未找到对应的目标成分信息");
            }
            //计算成本
            BigDecimal cost=BigDecimal.ZERO;
            if(rawMaterial.getPrice()!=null){
                cost=rawMaterial.getPrice().multiply(calculationResult.getWieght());
            }else {
                cost=BigDecimal.ONE.multiply(calculationResult.getWieght());
            }

            // 6、封装到CalculationResultVO中
            CalculationResultVO resultVO = new CalculationResultVO();
            resultVO.setCost(cost);
            resultVO.setSingleConsume(calculationResult.getSingleConsume());
            resultVO.setAverageValue(purposeComposition.getAverageValue());
            resultVO.setId(calculationResult.getId());
            resultVO.setRawMaterialName(rawMaterial.getName());
            resultVO.setPrice(rawMaterial.getPrice());
            resultVO.setMinValue(purposeComposition.getMinValue());
            resultVO.setMaxValue(purposeComposition.getMaxValue());
            resultVO.setWieght(calculationResult.getWieght());
            resultVO.setComposition(calculationResult.getComposition());
            resultVO.setRecoveryRate(calculationResult.getRecoveryRate());
            resultVOs.add(resultVO);
        }
        return resultVOs;
    }

}




