package com.nercar.ingredient.exception;

import com.nercar.ingredient.response.Result;
import com.nercar.ingredient.response.ResultCode;
import com.nercar.ingredient.security.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.resource.NoResourceFoundException;

@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public Result<String> handleErrors(Exception e) {
        try {
            if (e instanceof NoResourceFoundException) {
                return null;
            }
            if (e instanceof BusinessException businessException){
                return Result.failed(businessException);
            }
            log.error("异常信息:- {}", e.getMessage());
            e.printStackTrace();
            return Result.failed(ResultCode.UNKNOWN_ERROR,e.getMessage());
        } finally {
            UserContext.clear();
        }
    }
}
