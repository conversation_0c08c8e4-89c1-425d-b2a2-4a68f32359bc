package com.nercar.ingredient.domain.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 配料模板与原料关联表
 * @TableName template_materials_mapping
 */
@TableName(value ="template_materials_mapping")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TemplateMaterialsMapping extends BaseEntity {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 模板ID
     */
    private Long standardTemplateId;

    /**
     * 原料ID
     */
    private Long rawMaterialId;

    /**
     * 配料人
     */
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    private String createuser;

    /**
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;



}