package com.nercar.ingredient.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.po.ProductionFactory;
import com.nercar.ingredient.mapper.ProductionFactoryMapper;
import com.nercar.ingredient.service.ProductionFactoryService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【production_factory(厂表)】的数据库操作Service实现
* @createDate 2025-04-01 13:55:28
*/
@Service
public class ProductionFactoryServiceImpl extends ServiceImpl<ProductionFactoryMapper, ProductionFactory>
    implements ProductionFactoryService{

}




