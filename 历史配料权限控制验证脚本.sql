-- 历史配料权限控制验证脚本
-- 执行时间：2025-01-14

-- 1. 用户部门数据完整性检查
SELECT 
    '总用户数' as check_item,
    COUNT(*) as count
FROM users
UNION ALL
SELECT 
    '有部门ID的用户数',
    COUNT(*)
FROM users 
WHERE department_id IS NOT NULL
UNION ALL
SELECT 
    '部门ID为NULL的用户数',
    COUNT(*)
FROM users 
WHERE department_id IS NULL;

-- 2. 用户部门关系详细信息
SELECT 
    u.user_name,
    u.department_id,
    d.department_name,
    CASE 
        WHEN u.department_id IS NULL THEN '无部门信息'
        WHEN d.department_name LIKE '%技术中心%' THEN '技术中心用户(全部权限)'
        ELSE '普通用户(部门权限)'
    END as permission_level
FROM users u
LEFT JOIN departments d ON u.department_id = d.id
ORDER BY u.department_id, u.user_name;

-- 3. 技术中心用户识别
SELECT 
    u.user_name,
    u.department_id,
    d.department_name
FROM users u
LEFT JOIN departments d ON u.department_id = d.id
WHERE d.department_name LIKE '%技术中心%';

-- 4. 配料记录按部门分布统计
SELECT 
    d.department_name,
    sir.department_id,
    COUNT(*) as record_count,
    COUNT(DISTINCT sir.user_name) as user_count,
    MIN(sir.mixing_date) as earliest_date,
    MAX(sir.mixing_date) as latest_date
FROM standard_ingredient_records sir
LEFT JOIN departments d ON sir.department_id = d.id
WHERE sir.status = 1  -- 只统计历史配料
GROUP BY d.department_name, sir.department_id
ORDER BY record_count DESC;

-- 5. 用户配料记录分布
SELECT 
    sir.user_name,
    sir.department_id,
    d.department_name,
    COUNT(*) as record_count
FROM standard_ingredient_records sir
LEFT JOIN departments d ON sir.department_id = d.id
WHERE sir.status = 1
GROUP BY sir.user_name, sir.department_id, d.department_name
ORDER BY record_count DESC;

-- 6. 模拟权限控制查询 - 普通用户（张三，部门ID=9）
SELECT 
    '张三用户权限测试' as test_case,
    COUNT(*) as accessible_records
FROM standard_ingredient_records sir
WHERE sir.status = 1
  AND sir.department_id = 9;  -- 张三的部门ID

-- 7. 模拟权限控制查询 - 技术中心用户（假设存在）
SELECT 
    '技术中心用户权限测试' as test_case,
    COUNT(*) as accessible_records
FROM standard_ingredient_records sir
WHERE sir.status = 1;  -- 技术中心用户可以看到所有记录

-- 8. 验证部门权限过滤效果对比
SELECT 
    '权限对比' as comparison_type,
    '所有历史配料记录' as scope,
    COUNT(*) as record_count
FROM standard_ingredient_records 
WHERE status = 1
UNION ALL
SELECT 
    '权限对比',
    '部门ID=9的配料记录',
    COUNT(*)
FROM standard_ingredient_records 
WHERE status = 1 AND department_id = 9
UNION ALL
SELECT 
    '权限对比',
    '部门ID=12的配料记录',
    COUNT(*)
FROM standard_ingredient_records 
WHERE status = 1 AND department_id = 12
UNION ALL
SELECT 
    '权限对比',
    '部门ID=13的配料记录',
    COUNT(*)
FROM standard_ingredient_records 
WHERE status = 1 AND department_id = 13;

-- 9. 检查配料记录中是否存在department_id为NULL的情况
SELECT 
    'department_id为NULL的配料记录数' as check_item,
    COUNT(*) as count
FROM standard_ingredient_records 
WHERE department_id IS NULL
UNION ALL
SELECT 
    'department_id不为NULL的配料记录数',
    COUNT(*)
FROM standard_ingredient_records 
WHERE department_id IS NOT NULL;

-- 10. 验证用户和配料记录的部门一致性
SELECT 
    u.user_name,
    u.department_id as user_dept_id,
    d1.department_name as user_dept_name,
    sir.department_id as record_dept_id,
    d2.department_name as record_dept_name,
    COUNT(*) as record_count,
    CASE 
        WHEN u.department_id = sir.department_id THEN '部门一致'
        ELSE '部门不一致'
    END as consistency_check
FROM users u
JOIN standard_ingredient_records sir ON u.user_name = sir.user_name
LEFT JOIN departments d1 ON u.department_id = d1.id
LEFT JOIN departments d2 ON sir.department_id = d2.id
WHERE sir.status = 1
GROUP BY u.user_name, u.department_id, d1.department_name, sir.department_id, d2.department_name
ORDER BY u.user_name, record_count DESC;

-- 11. 权限控制功能验证总结
SELECT 
    '权限控制验证总结' as summary_item,
    '验证完成' as status,
    NOW() as verification_time;

-- 验证脚本执行完成
SELECT '历史配料权限控制验证脚本执行完成' as completion_status;
