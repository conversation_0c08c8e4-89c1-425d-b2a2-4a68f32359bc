package com.nercar.ingredient.domain.vo;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ContractReviewInfoVO 测试类
 * 主要测试冶炼工艺字段的JSON字符串到数组的转换功能
 */
@SpringBootTest
public class ContractReviewInfoVOTest {

    @Test
    public void testSmeltingProcessConversion() throws Exception {
        // 创建测试对象
        ContractReviewInfoVO vo = new ContractReviewInfoVO();
        
        // 测试1：正常的JSON字符串转换
        String jsonString = "[\"电炉\",\"LF\",\"真空感应炉\",\"连铸\"]";
        vo.setSmeltingProcess(jsonString);
        
        List<String> result = vo.getSmeltingProcessList();
        assertNotNull(result);
        assertEquals(4, result.size());
        assertEquals("电炉", result.get(0));
        assertEquals("LF", result.get(1));
        assertEquals("真空感应炉", result.get(2));
        assertEquals("连铸", result.get(3));
        
        // 测试2：空字符串处理
        vo.setSmeltingProcess("");
        result = vo.getSmeltingProcessList();
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 测试3：null值处理
        vo.setSmeltingProcess(null);
        result = vo.getSmeltingProcessList();
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 测试4：无效JSON字符串处理
        vo.setSmeltingProcess("invalid json");
        result = vo.getSmeltingProcessList();
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 测试5：JSON序列化测试
        vo.setSmeltingProcess("[\"电炉\",\"连铸\"]");
        vo.setId(1001L);
        vo.setCode("F0001-2025");
        vo.setCustomerName("测试客户");
        
        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writeValueAsString(vo);
        
        // 验证JSON中包含字符串数组格式的smeltingProcess
        assertTrue(json.contains("\"smeltingProcess\":[\"电炉\",\"连铸\"]"));
        
        System.out.println("JSON序列化结果:");
        System.out.println(json);
    }
    
    @Test
    public void testComplexSmeltingProcess() {
        ContractReviewInfoVO vo = new ContractReviewInfoVO();
        
        // 测试复杂的冶炼工艺数组
        String complexJson = "[\"电炉\",\"LF精炼\",\"真空感应炉\",\"连铸\",\"轧制\"]";
        vo.setSmeltingProcess(complexJson);
        
        List<String> result = vo.getSmeltingProcessList();
        assertNotNull(result);
        assertEquals(5, result.size());
        
        List<String> expected = Arrays.asList("电炉", "LF精炼", "真空感应炉", "连铸", "轧制");
        assertEquals(expected, result);
    }
}
