package com.nercar.ingredient.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nercar.ingredient.domain.po.PathMaterials;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【path_materials(工艺路径材料关联表)】的数据库操作Mapper
* @createDate 2025-04-01 13:55:28
* @Entity com.nercar.ingredient.domain.po.PathMaterials
*/
@Mapper
public interface PathMaterialsMapper extends BaseMapper<PathMaterials> {

    // 添加自定义删除方法
    int deleteByRawMaterialId(@Param("processPathId") Long pathId, @Param("rawMaterialId") Long rawMaterialId);
}




