package com.nercar.ingredient.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.nercar.ingredient.domain.po.BaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 成锭率
 * @TableName
 */
@ColumnWidth(20) //列宽
@ContentRowHeight(20)//数据行高
@HeadRowHeight(30)//表头高
@Data
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class IngotYieldRatesExcel implements Serializable {
    /**
     * 
     */
    @ExcelProperty("ID")
    private String id;

    /**
     * 工序
     */
    @ExcelProperty("工序")
    private String processPath;

    /**
     * 部门名称
     */
    @ExcelProperty("部门名称")
    private String departmentName;

    @ExcelProperty("设备")
    private String device;


    @ExcelProperty("更新人")
    private String updateuser;

    /**
     * 成锭率
     */
    @ExcelProperty("成锭率")
    private BigDecimal ingotYield;

    /**
     *
     */
    @ExcelProperty("创建用户")
    private String createuser;

    /**
     *
     */
    @ExcelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;


}