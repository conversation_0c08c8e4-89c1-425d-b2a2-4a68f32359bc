package com.nercar.ingredient.constant;

public class SecurityConstants {
    // 鉴权白名单
    public static final String[] WHITE_LIST = {
            "/user/login",
//            "/user/logout",
            "/user/register",
            "/swagger-ui/",
            "/swagger-ui.html",
            "/doc.html",
            "/favicon.ico",
            "/v3/api-docs",
            "/camunda",
            "/webjars",
            "/leave",
            "/flow/diagram",
            "/flow/diagram2",
            "/file/preview",
            "/file/download",
            "/optimizedIngredient/getCostCalculationParameter",  // 接收参数的接口
//            "/ingredientCalculation",  // 配料计算相关的所有接口
//            "/ingredientCalculation/",  // 确保带斜杠的路径也能匹配
//            "/ingredientCalculation/getProcessPath",  // 如果只想放行特定接口，可以具体列出
//            "/ingredientCalculation/calculation",
            "/ingredientCalculation/getCostCalculationParameterIdAndToken",
            "/ingredientCalculation/getCostCalculationParameter",
            "/images/",  // 添加图片访问路径到白名单
    };
}
