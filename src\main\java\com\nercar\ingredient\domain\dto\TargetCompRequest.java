package com.nercar.ingredient.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 目标成分请求类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TargetCompRequest {
    /**
     * 元素
     */
    private String element;
    
    /**
     * 最小值
     */
    private Double minValue;
    
    /**
     * 最大值
     */
    private Double maxValue;
    
    /**
     * 目标值
     */
    private Double targetValue;
}
