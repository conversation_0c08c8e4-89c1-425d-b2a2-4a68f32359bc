package com.nercar.ingredient.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nercar.ingredient.domain.po.StandardRawMaterialsPlus;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【standard_raw_materials_plus(标准原料扩展表)】的数据库操作Mapper
* @createDate 2025-04-22 10:52:00
* @Entity com.nercar.ingredient.domain.po.StandardRawMaterialsPlus
*/
public interface StandardRawMaterialsPlusMapper extends BaseMapper<StandardRawMaterialsPlus> {

    IPage<StandardRawMaterialsPlus> selectMaterials(Page<StandardRawMaterialsPlus> page, @Param(Constants.WRAPPER) LambdaQueryWrapper<StandardRawMaterialsPlus> wrapper);

    Boolean deleteByMaterialId(@Param("materialId") String materialId);

    void updateByMaterialId(StandardRawMaterialsPlus standardRawMaterialsPlus);
}




