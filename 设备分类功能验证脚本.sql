-- 设备分类功能验证脚本
-- 执行时间：2025-01-14

-- 1. 数据完整性检查
SELECT 
    '总设备数' as check_item,
    COUNT(*) as count
FROM production_equipments
UNION ALL
SELECT 
    '有equipment_type的设备数',
    COUNT(*)
FROM production_equipments 
WHERE equipment_type IS NOT NULL
UNION ALL
SELECT 
    '冶炼设备数',
    COUNT(*)
FROM production_equipments 
WHERE equipment_type = '冶炼'
UNION ALL
SELECT 
    '加工设备数',
    COUNT(*)
FROM production_equipments 
WHERE equipment_type = '加工';

-- 2. 按部门分类统计
SELECT 
    d.department_name,
    pe.equipment_type,
    COUNT(*) as equipment_count
FROM production_equipments pe
LEFT JOIN departments d ON pe.department_id = d.id
GROUP BY d.department_name, pe.equipment_type
ORDER BY d.department_name, pe.equipment_type;

-- 3. 验证特冶炼钢厂和电炉炼钢厂的设备都是冶炼类型
SELECT 
    d.department_name,
    pe.equipment_name,
    pe.equipment_type,
    CASE 
        WHEN d.department_name IN ('特冶炼钢厂', '电炉炼钢厂') AND pe.equipment_type = '冶炼' THEN '✓ 正确'
        WHEN d.department_name IN ('特冶炼钢厂', '电炉炼钢厂') AND pe.equipment_type != '冶炼' THEN '✗ 错误'
        WHEN d.department_name NOT IN ('特冶炼钢厂', '电炉炼钢厂') AND pe.equipment_type = '加工' THEN '✓ 正确'
        WHEN d.department_name NOT IN ('特冶炼钢厂', '电炉炼钢厂') AND pe.equipment_type != '加工' THEN '✗ 错误'
        ELSE '? 未知'
    END as validation_result
FROM production_equipments pe
LEFT JOIN departments d ON pe.department_id = d.id
ORDER BY d.department_name, pe.equipment_name;

-- 4. 检查是否有空值或异常值
SELECT 
    'equipment_type为NULL的记录数' as check_item,
    COUNT(*) as count
FROM production_equipments 
WHERE equipment_type IS NULL
UNION ALL
SELECT 
    'equipment_type不是冶炼或加工的记录数',
    COUNT(*)
FROM production_equipments 
WHERE equipment_type NOT IN ('冶炼', '加工');

-- 5. 详细的设备列表（用于人工验证）
SELECT 
    pe.id,
    d.department_name,
    pe.equipment_name,
    pe.type as old_type,
    pe.equipment_type as new_equipment_type,
    pe.createuser,
    pe.createtime
FROM production_equipments pe
LEFT JOIN departments d ON pe.department_id = d.id
ORDER BY d.department_name, pe.equipment_type, pe.equipment_name;
