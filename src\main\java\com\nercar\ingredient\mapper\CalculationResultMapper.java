package com.nercar.ingredient.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nercar.ingredient.domain.po.CalculationResult;
import com.nercar.ingredient.domain.po.CostEstimation;
import com.nercar.ingredient.domain.vo.StandardRawMaterialsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【calculation_result(计算结果表)】的数据库操作Mapper
* @createDate 2025-04-01 13:55:28
* @Entity com.nercar.ingredient.domain.po.CalculationResult
*/
@Mapper
public interface CalculationResultMapper extends BaseMapper<CalculationResult> {

    List<StandardRawMaterialsVO> selectMaterialsByIds(List<Long> materialsIdList,Long standardIngredientRecordId);

    CostEstimation selectByCostId(@Param("id") Long id);

    /**
     * 查询指定配料记录的计算序号
     * @param standardIngredientRecordId 配料记录ID
     * @param limit 限制数量
     * @return 计算序号列表（升序，第1次计算在前）
     */
    List<Integer> selectRecentCalculationSequences(@Param("standardIngredientRecordId") Long standardIngredientRecordId,
                                                  @Param("limit") Integer limit);

    /**
     * 根据配料记录ID和计算序号查询计算结果
     * @param standardIngredientRecordId 配料记录ID
     * @param calculationSequence 计算序号
     * @return 计算结果列表
     */
    List<CalculationResult> selectBySequence(@Param("standardIngredientRecordId") Long standardIngredientRecordId,
                                           @Param("calculationSequence") Integer calculationSequence);
}




