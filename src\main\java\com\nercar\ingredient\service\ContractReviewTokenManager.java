package com.nercar.ingredient.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nercar.ingredient.config.ContractReviewConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 合同评审系统Token管理器
 * 负责Token的获取、缓存、刷新等操作
 */
@Slf4j
@Component
public class ContractReviewTokenManager {
    
    @Autowired
    private ContractReviewConfig config;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 缓存的Token
     */
    private volatile String cachedToken;
    
    /**
     * Token过期时间
     */
    private volatile LocalDateTime tokenExpireTime;
    
    /**
     * 应用启动时获取Token
     */
    @PostConstruct
    public void initToken() {
        log.info("应用启动，初始化合同评审系统Token");
        refreshToken();
    }
    
    /**
     * 定时刷新Token（每30分钟检查一次）
     */
    @Scheduled(fixedRateString = "#{${contract-review.token.refresh-interval:30} * 60 * 1000}")
    public void scheduledRefreshToken() {
        if (isTokenExpired()) {
            log.info("定时检查发现Token即将过期，开始刷新");
            refreshToken();
        }
    }
    
    /**
     * 获取有效的Token
     * @return 有效的Token
     */
    public String getValidToken() {
        if (isTokenExpired()) {
            synchronized (this) {
                // 双重检查锁定
                if (isTokenExpired()) {
                    refreshToken();
                }
            }
        }
        return cachedToken;
    }
    
    /**
     * 检查Token是否过期
     * @return true-已过期，false-未过期
     */
    private boolean isTokenExpired() {
        return cachedToken == null || 
               tokenExpireTime == null || 
               LocalDateTime.now().isAfter(tokenExpireTime.minusMinutes(5)); // 提前5分钟刷新
    }
    
    /**
     * 刷新Token
     */
    private void refreshToken() {
        try {
            log.info("开始刷新合同评审系统Token");
            
            // 构建登录请求
            String loginUrl = config.getBaseUrl() + config.getEndpoints().getLogin();
            
            Map<String, String> loginRequest = new HashMap<>();
            loginRequest.put("username", config.getLogin().getUsername());
            loginRequest.put("password", config.getLogin().getPassword());
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(loginRequest, headers);
            
            // 发送登录请求
            ResponseEntity<String> response = restTemplate.postForEntity(loginUrl, requestEntity, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                // 解析响应获取Token
                JsonNode responseJson = objectMapper.readTree(response.getBody());
                
                if (responseJson.get("success").asBoolean()) {
                    String newToken = responseJson.get("data").get("token").asText();
                    
                    // 更新缓存的Token和过期时间
                    this.cachedToken = newToken;
                    this.tokenExpireTime = LocalDateTime.now().plusMinutes(config.getToken().getCacheDuration());
                    
                    log.info("Token刷新成功，新Token: {}..., 过期时间: {}", 
                            newToken.substring(0, Math.min(8, newToken.length())), tokenExpireTime);
                } else {
                    log.error("登录失败，响应: {}", response.getBody());
                    throw new RuntimeException("合同评审系统登录失败: " + responseJson.get("message").asText());
                }
            } else {
                log.error("登录请求失败，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
                throw new RuntimeException("合同评审系统登录请求失败");
            }
            
        } catch (Exception e) {
            log.error("刷新Token失败: {}", e.getMessage(), e);
            throw new RuntimeException("刷新合同评审系统Token失败", e);
        }
    }
    
    /**
     * 强制刷新Token（用于Token失效时的重试）
     */
    public void forceRefreshToken() {
        log.info("强制刷新Token");
        synchronized (this) {
            this.cachedToken = null;
            this.tokenExpireTime = null;
            refreshToken();
        }
    }
    
    /**
     * 获取Token状态信息（用于监控和调试）
     */
    public String getTokenStatus() {
        if (cachedToken == null) {
            return "Token未初始化";
        }
        
        if (isTokenExpired()) {
            return "Token已过期，过期时间: " + tokenExpireTime;
        }
        
        return "Token有效，过期时间: " + tokenExpireTime;
    }
}
