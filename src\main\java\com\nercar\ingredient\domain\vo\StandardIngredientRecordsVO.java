package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 标准配料记录表
 * @TableName standard_ingredient_records
 */

@Data
@TableName(value ="standard_ingredient_records")
public class StandardIngredientRecordsVO extends BaseVO {
    /**
     * 标准名称
     */
    private String standardName;
    /**
     * 钢种
     */
    private String steelGrade;

    private Integer status;

    /**
     * 主键
     */
    @TableId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 钢种牌号ID
     */
    private Long steelGradeId;

    /**
     * 计算流程编号
     */
    private String calculationProcessNo;

    /**
     * 执行标准ID
     */
    private Long executionStandardId;

    /**
     * 原材料总量
     */
    private BigDecimal rawMaterialTotal;

    /**
     * 总成本
     */
    private BigDecimal costPrice;

    /**
     * 配料日期
     */
    private Date mixingDate;

    /**
     * 发布日期
     */
    private Date releaseDate;

    /**
     * 类别;标准配料、优化配料
     */
    private String category;

    /**
     * 计算结果ID
     */
    private Long calculationResultId;

    /**
     * 工艺路径ID
     */
    private Long processPathId;

    /**
     * 特殊说明
     */
    private String specialNotes;

    /**
     * 成本测算ID;当时从车本测算发起时，填写此信息
     */
    private Long costEstimattionId;

    /**
     * 配料人
     */
    @TableField(value = "createuser",fill = FieldFill.INSERT)
    private String createuser;

    /**
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;




}