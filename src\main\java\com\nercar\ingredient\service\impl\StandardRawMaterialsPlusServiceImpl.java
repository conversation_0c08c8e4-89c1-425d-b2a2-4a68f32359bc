package com.nercar.ingredient.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.dto.ElementDTO;
import com.nercar.ingredient.domain.dto.StandardRawMaterialsPlusDTO;
import com.nercar.ingredient.domain.dto.StandardRawMaterialsQueryDTO;
import com.nercar.ingredient.domain.po.*;
import com.nercar.ingredient.domain.vo.StandardRawMaterialsVO;
import com.nercar.ingredient.mapper.*;
import com.nercar.ingredient.service.StandardRawMaterialsPlusService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import java.util.stream.Stream;

/**
* <AUTHOR>
* @description 针对表【standard_raw_materials_plus(标准原料扩展表)】的数据库操作Service实现
* @createDate 2025-04-22 10:51:59
*/
@Service
public class StandardRawMaterialsPlusServiceImpl extends ServiceImpl<StandardRawMaterialsPlusMapper, StandardRawMaterialsPlus>
    implements StandardRawMaterialsPlusService{

    @Autowired
    private StandardRawMaterialsPlusMapper standardRawMaterialsPlusMapper;
    @Autowired
    private MaterialElementsMapper materialElementsMapper;
    @Autowired
    private DepartmentsMapper departmentsMapper;
    @Autowired
    private ProductionEquipmentsMapper productionEquipmentsMapper;
    @Autowired
    private PathMaterialsMapper pathMaterialsMapper;
    @Autowired
    private ProcessPathStepsMapper processPathStepsMapper;
    @Autowired
    private TemplateEquipmentMappingMapper templateEquipmentMappingMapper;
    @Autowired
    private StandardTemplateMapper standardTemplateMapper;
    @Autowired
    private TemplateMaterialsMappingMapper templateMaterialsMappingMapper;
    @Autowired
    private StandardRawMaterialsMapper standardRawMaterialsMapper;

    @Override
    public IPage<StandardRawMaterialsPlus> selectStandardRawMaterials(StandardRawMaterialsQueryDTO standardRawMaterialsQueryDTO) {
        //1、先判断参数是否为空
        //2、根据departmentName查询部门表，得到ID，由此得出第一个查询条件 departmentId
        Long departmentId = null;
        if (standardRawMaterialsQueryDTO.getDepartmentName().length() > 0){
            LambdaQueryWrapper<Departments> wrapper = new LambdaQueryWrapper<>();
            wrapper.like(Departments::getDepartmentName, standardRawMaterialsQueryDTO.getDepartmentName());
            Departments department = departmentsMapper.selectOne(wrapper);
            if (department != null){
                departmentId = department.getId();
            }
        }
        //3、根据name,category以及departmentId查询standardRawMaterials，得到原料数据
        LambdaQueryWrapper<StandardRawMaterialsPlus> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(departmentId), StandardRawMaterialsPlus::getDepartmentId, departmentId);
        wrapper.like(Objects.nonNull(standardRawMaterialsQueryDTO.getName()), StandardRawMaterialsPlus::getName, standardRawMaterialsQueryDTO.getName());
        wrapper.like(Objects.nonNull(standardRawMaterialsQueryDTO.getCategory()), StandardRawMaterialsPlus::getCategory, standardRawMaterialsQueryDTO.getCategory());
        wrapper.select(
                StandardRawMaterialsPlus::getId,
                StandardRawMaterialsPlus::getMaterialId,
                StandardRawMaterialsPlus::getName,
                StandardRawMaterialsPlus::getDepartmentId,
                StandardRawMaterialsPlus::getProductionEquipmentId,
                StandardRawMaterialsPlus::getYieldRate,
                StandardRawMaterialsPlus::getPrice,
                StandardRawMaterialsPlus::getIsCustom,
                StandardRawMaterialsPlus::getCategory,
                StandardRawMaterialsPlus::getPriority,
                StandardRawMaterialsPlus::getCContent,
                StandardRawMaterialsPlus::getSingleConsume,
                StandardRawMaterialsPlus::getDepartmentName,
                StandardRawMaterialsPlus::getCreateuser,
                StandardRawMaterialsPlus::getCreatetime,
                StandardRawMaterialsPlus::getUpdatetime,
                StandardRawMaterialsPlus::getSteelGrade
        );
        Page<StandardRawMaterialsPlus> page = new Page<>(standardRawMaterialsQueryDTO.getPageNo(), standardRawMaterialsQueryDTO.getPageSize());
        IPage<StandardRawMaterialsPlus> result=baseMapper.selectMaterials(page, wrapper);
        //4.1、再根据原料数据中的equipmentId 查询productionEquipment表，得到生产设备名称，并赋值给standardRawMaterialsVO
        //4.2、再根据原料数据中的departmentId 查询departments表，得到部门名称，并赋值给standardRawMaterialsVO
        if (result != null){
            List<StandardRawMaterialsPlus> records = result.getRecords();
            if (!CollectionUtils.isEmpty(records)){
                records.forEach(vo -> {
                    Long equipmentId = vo.getProductionEquipmentId();
                    Long departmentId1 = vo.getDepartmentId();
                    if (departmentId1 != null){
                        LambdaQueryWrapper<Departments> wrapper1 = new LambdaQueryWrapper<>();
                        wrapper1.eq(Departments::getId, departmentId1);
                        Departments departments = departmentsMapper.selectOne(wrapper1);
                        if (departments != null){
                            vo.setDepartmentName(departments.getDepartmentName());
                        }
                    }
                    if (equipmentId != null){
                        LambdaQueryWrapper<ProductionEquipments> wrapper1 = new LambdaQueryWrapper<>();
                        wrapper1.eq(ProductionEquipments::getId, equipmentId);
                        ProductionEquipments productionEquipment = productionEquipmentsMapper.selectOne(wrapper1);
                        if (productionEquipment != null){
                            vo.setEquipmentName(productionEquipment.getEquipmentName());
                        }
                    }
                });
            }
        }
        return result;
    }

    private boolean hasAnyElementNotNull(ElementDTO dto) {
        return Stream.of(
                        dto.getC(), dto.getMn(), dto.getSi(), dto.getP(), dto.getCr(), dto.getV(),
                        dto.getMo(), dto.getNi(), dto.getW(), dto.getCu(), dto.getTi(), dto.getNb(),
                        dto.getCo(), dto.getS(), dto.getSn(), dto.getAl(), dto.getFe(), dto.getB(),
                        dto.getZr(), dto.getLa(), dto.getCe(), dto.getCa(), dto.getPb(), dto.getBi(),
                        dto.getSb(), dto.getAs(), dto.getAls(), dto.getTa(), dto.getMg(), dto.getAg(),
                        dto.getHg(), dto.getCd(), dto.getZn(), dto.getTe(), dto.getSe(), dto.getPr(),
                        dto.getNd(), dto.getSc(), dto.getY(), dto.getHf(), dto.getPcm(), dto.getH(),
                        dto.getO(), dto.getN())
                .anyMatch(Objects::nonNull);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertOrUpdate(StandardRawMaterialsPlusDTO standardRawMaterialsPlusDTO) {
        if (standardRawMaterialsPlusDTO == null) {
            return false;
        }

        // 新增：检查设备名称是否为空
        String equipmentName2 = standardRawMaterialsPlusDTO.getEquipmentName();
        if (equipmentName2 == null || equipmentName2.trim().isEmpty()) {
            throw new RuntimeException("设备名称不能为空，请选择一个设备！");
        }
        // 获取的参数是部门名称，设备名称以及原料基本信息
        // 1、先根据部门名称查询部门表，得到部门ID，并赋值给standardRawMaterialsDTO
        String departmentName = standardRawMaterialsPlusDTO.getDepartmentName();
        if (departmentName!=null||departmentName.length() > 0){
            LambdaQueryWrapper<Departments> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Objects.nonNull(departmentName),Departments::getDepartmentName, departmentName);
            Departments departments = departmentsMapper.selectOne(wrapper);
            if (departments != null){
                standardRawMaterialsPlusDTO.setDepartmentId(departments.getId());
            }
        }
        // 2、再根据设备名称查询设备表，得到设备ID，并赋值给standardRawMaterialsDTO
        String equipmentName = standardRawMaterialsPlusDTO.getEquipmentName();
        if (equipmentName!=null||equipmentName.length() > 0){
            LambdaQueryWrapper<ProductionEquipments> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Objects.nonNull(equipmentName), ProductionEquipments::getEquipmentName, equipmentName);
            ProductionEquipments productionEquipments = productionEquipmentsMapper.selectOne(wrapper);
            if (productionEquipments != null){
                standardRawMaterialsPlusDTO.setProductionEquipmentId(productionEquipments.getId());
            }
        }
        // 3、根据standardRawMaterialsDTO创建StandardRawMaterials对象，并插入数据库
//        StandardRawMaterials standardRawMaterials = BeanUtil.copyProperties(standardRawMaterialsDTO, StandardRawMaterials.class);
        // 4、查询是否有该条数据，如果有则更新，如果没有则插入
        Long id = standardRawMaterialsPlusDTO.getId();
        if (id == null) {
            List<ElementDTO> elementList = standardRawMaterialsPlusDTO.getElementList();
            //目前elementList集合保存的数据是一个type然后是一系列的元素
            //需要判断elementList集合是否为空，如果为空则抛出异常，但是type是一定不会为空的，所以要判断的其实是其他元素值
            //而且必须type为目标值的时候，元素值至少要有一个元素
            if (CollectionUtils.isEmpty(elementList)) {
                throw new RuntimeException("元素列表不能为空，请至少填写一组元素数据！");
            }
            boolean hasValidTargetValue = elementList.stream()
                    .filter(e -> "目标值".equals(e.getType()))
                    .anyMatch(this::hasAnyElementNotNull);
            if (!hasValidTargetValue) {
                throw new RuntimeException("目标值类型的元素不能全为空，请至少填写一个有效元素值！");
            }
            // 生成原料id - 修改这部分
            String materialId = UUID.randomUUID().toString().replace("-", "").substring(0, 16);

            for (ElementDTO elementDTO : elementList) {
                StandardRawMaterialsPlus standardRawMaterialsPlus = new StandardRawMaterialsPlus();
                BeanUtils.copyProperties(standardRawMaterialsPlusDTO, standardRawMaterialsPlus);
                BeanUtils.copyProperties(elementDTO, standardRawMaterialsPlus);
                // 设置原料id - 使用字符串类型
                standardRawMaterialsPlus.setMaterialId(materialId);
                StandardRawMaterials standardRawMaterials = new StandardRawMaterials();

                //先判断type是不是目标值，如果是目标值再进行转换
                if(elementDTO.getType().equals("目标值")) {
                    //把standardRawMaterialsPlus复制给standardRawMaterials
                    BeanUtils.copyProperties(standardRawMaterialsPlus, standardRawMaterials);

                    //复制完成之后，遍历standardRawMaterialsPlus中的所有元素，找出目标值最大的前两个
                    Map<String, BigDecimal> elementMap = new HashMap<>();
                    // 获取所有元素字段及其值

                    elementMap.put("C", standardRawMaterialsPlus.getC());
                    elementMap.put("Mn", standardRawMaterialsPlus.getMn());
                    elementMap.put("Si", standardRawMaterialsPlus.getSi());
                    elementMap.put("P", standardRawMaterialsPlus.getP());
                    elementMap.put("Cr", standardRawMaterialsPlus.getCr());
                    elementMap.put("V", standardRawMaterialsPlus.getV());
                    elementMap.put("Mo", standardRawMaterialsPlus.getMo());
                    elementMap.put("Ni", standardRawMaterialsPlus.getNi());
                    elementMap.put("W", standardRawMaterialsPlus.getW());
                    elementMap.put("Cu", standardRawMaterialsPlus.getCu());
                    elementMap.put("Ti", standardRawMaterialsPlus.getTi());
                    elementMap.put("Nb", standardRawMaterialsPlus.getNb());
                    elementMap.put("Co", standardRawMaterialsPlus.getCo());
                    elementMap.put("S", standardRawMaterialsPlus.getS());
                    elementMap.put("Sn", standardRawMaterialsPlus.getSn());
                    elementMap.put("Al", standardRawMaterialsPlus.getAl());
                    elementMap.put("Fe", standardRawMaterialsPlus.getFe());
                    elementMap.put("B", standardRawMaterialsPlus.getB());
                    elementMap.put("Zr", standardRawMaterialsPlus.getZr());
                    elementMap.put("La", standardRawMaterialsPlus.getLa());
                    elementMap.put("Ce", standardRawMaterialsPlus.getCe());
                    elementMap.put("Ca", standardRawMaterialsPlus.getCa());
                    elementMap.put("Pb", standardRawMaterialsPlus.getPb());
                    elementMap.put("Bi", standardRawMaterialsPlus.getBi());
                    elementMap.put("Sb", standardRawMaterialsPlus.getSb());
                    elementMap.put("As", standardRawMaterialsPlus.getAs());
                    elementMap.put("Als", standardRawMaterialsPlus.getAls());
                    elementMap.put("Ta", standardRawMaterialsPlus.getTa());
                    elementMap.put("Mg", standardRawMaterialsPlus.getMg());
                    elementMap.put("Ag", standardRawMaterialsPlus.getAg());
                    elementMap.put("Hg", standardRawMaterialsPlus.getHg());
                    elementMap.put("Cd", standardRawMaterialsPlus.getCd());
                    elementMap.put("Zn", standardRawMaterialsPlus.getZn());
                    elementMap.put("Te", standardRawMaterialsPlus.getTe());
                    elementMap.put("Se", standardRawMaterialsPlus.getSe());
                    elementMap.put("Pr", standardRawMaterialsPlus.getPr());
                    elementMap.put("Nd", standardRawMaterialsPlus.getNd());
                    elementMap.put("Sc", standardRawMaterialsPlus.getSc());
                    elementMap.put("Y", standardRawMaterialsPlus.getY());
                    elementMap.put("Hf", standardRawMaterialsPlus.getHf());
                    elementMap.put("Pcm", standardRawMaterialsPlus.getPcm());
                    elementMap.put("H", standardRawMaterialsPlus.getH());
                    elementMap.put("O", standardRawMaterialsPlus.getO());
                    elementMap.put("N", standardRawMaterialsPlus.getN());

                    // 过滤掉null值并按值大小排序
                    List<Map.Entry<String, BigDecimal>> sortedElements = elementMap.entrySet()
                        .stream()
                        .filter(entry -> entry.getValue() != null)
                        .sorted(Map.Entry.<String, BigDecimal>comparingByValue().reversed())
                        .collect(Collectors.toList());

                    if (!sortedElements.isEmpty()) {
                        // 设置主元素和基准品位
                        Map.Entry<String, BigDecimal> primaryElement = sortedElements.get(0);
                        standardRawMaterials.setElement(primaryElement.getKey());
                        standardRawMaterials.setComposition(primaryElement.getValue());

                        // 设置次元素（如果存在）
                        if (sortedElements.size() > 1) {
                            Map.Entry<String, BigDecimal> secondaryElement = sortedElements.get(1);
                            standardRawMaterials.setSecondaryElement(secondaryElement.getKey());
                        }
                    }
                    //设置C元素含量
                    if(standardRawMaterialsPlus.getC()!=null){
                        standardRawMaterials.setCarbonElement(standardRawMaterialsPlus.getC().toString());
                        standardRawMaterials.setCContent(standardRawMaterialsPlus.getC().intValue());
                    }

                    // 保存到原料表中 - 修复：根据名称和设备ID删除
                    standardRawMaterialsMapper.delete(new LambdaQueryWrapper<StandardRawMaterials>()
                        .eq(StandardRawMaterials::getName, standardRawMaterials.getName())
                        .eq(StandardRawMaterials::getProductionEquipmentId, standardRawMaterials.getProductionEquipmentId()));
                    standardRawMaterialsMapper.insert(standardRawMaterials);
                }
                baseMapper.insert(standardRawMaterialsPlus);
            }
            return true;
        } else {
            List<ElementDTO> elementList = standardRawMaterialsPlusDTO.getElementList();
            if (CollectionUtils.isEmpty(elementList)) {
                throw new RuntimeException("元素列表不能为空，请至少填写一组元素数据！");
            }
            boolean hasValidTargetValue = elementList.stream()
                    .filter(e -> "目标值".equals(e.getType()))
                    .anyMatch(this::hasAnyElementNotNull);
            if (!hasValidTargetValue) {
                throw new RuntimeException("目标值类型的元素不能全为空，请至少填写一个有效元素值！");
            }
            for (ElementDTO elementDTO : elementList) {
                StandardRawMaterialsPlus standardRawMaterialsPlus = new StandardRawMaterialsPlus();
                // 设置基本信息
                standardRawMaterialsPlus.setMaterialId(standardRawMaterialsPlusDTO.getMaterialId());
                standardRawMaterialsPlus.setName(standardRawMaterialsPlusDTO.getName());
                standardRawMaterialsPlus.setDepartmentId(standardRawMaterialsPlusDTO.getDepartmentId());
                standardRawMaterialsPlus.setProductionEquipmentId(standardRawMaterialsPlusDTO.getProductionEquipmentId());
                standardRawMaterialsPlus.setYieldRate(standardRawMaterialsPlusDTO.getYieldRate());
                standardRawMaterialsPlus.setPrice(standardRawMaterialsPlusDTO.getPrice());
                standardRawMaterialsPlus.setIsCustom(standardRawMaterialsPlusDTO.getIsCustom());
                standardRawMaterialsPlus.setCategory(standardRawMaterialsPlusDTO.getCategory());
                standardRawMaterialsPlus.setPriority(standardRawMaterialsPlusDTO.getPriority());
                standardRawMaterialsPlus.setCContent(standardRawMaterialsPlusDTO.getCContent());
                standardRawMaterialsPlus.setSingleConsume(standardRawMaterialsPlusDTO.getSingleConsume());
                standardRawMaterialsPlus.setDepartmentName(standardRawMaterialsPlusDTO.getDepartmentName());
                standardRawMaterialsPlus.setSteelGrade(standardRawMaterialsPlusDTO.getSteelGrade());
                // 修复：添加缺失的字段设置
                standardRawMaterialsPlus.setMaterialCode(standardRawMaterialsPlusDTO.getMaterialCode());
                standardRawMaterialsPlus.setStandardNo(standardRawMaterialsPlusDTO.getStandardNo());

                // 设置元素含量
                standardRawMaterialsPlus.setType(elementDTO.getType());
                standardRawMaterialsPlus.setC(elementDTO.getC());
                standardRawMaterialsPlus.setMn(elementDTO.getMn());
                standardRawMaterialsPlus.setSi(elementDTO.getSi());
                standardRawMaterialsPlus.setP(elementDTO.getP());
                standardRawMaterialsPlus.setCr(elementDTO.getCr());
                standardRawMaterialsPlus.setV(elementDTO.getV());
                standardRawMaterialsPlus.setMo(elementDTO.getMo());
                standardRawMaterialsPlus.setNi(elementDTO.getNi());
                standardRawMaterialsPlus.setW(elementDTO.getW());
                standardRawMaterialsPlus.setCu(elementDTO.getCu());
                standardRawMaterialsPlus.setTi(elementDTO.getTi());
                standardRawMaterialsPlus.setNb(elementDTO.getNb());
                standardRawMaterialsPlus.setCo(elementDTO.getCo());
                standardRawMaterialsPlus.setS(elementDTO.getS());
                standardRawMaterialsPlus.setSn(elementDTO.getSn());
                standardRawMaterialsPlus.setAl(elementDTO.getAl());
                standardRawMaterialsPlus.setFe(elementDTO.getFe());
                standardRawMaterialsPlus.setB(elementDTO.getB());
                standardRawMaterialsPlus.setZr(elementDTO.getZr());
                standardRawMaterialsPlus.setLa(elementDTO.getLa());
                standardRawMaterialsPlus.setCe(elementDTO.getCe());
                standardRawMaterialsPlus.setCa(elementDTO.getCa());
                standardRawMaterialsPlus.setPb(elementDTO.getPb());
                standardRawMaterialsPlus.setBi(elementDTO.getBi());
                standardRawMaterialsPlus.setSb(elementDTO.getSb());
                standardRawMaterialsPlus.setAs(elementDTO.getAs());
                standardRawMaterialsPlus.setAls(elementDTO.getAls());
                standardRawMaterialsPlus.setTa(elementDTO.getTa());
                standardRawMaterialsPlus.setMg(elementDTO.getMg());
                standardRawMaterialsPlus.setAg(elementDTO.getAg());
                standardRawMaterialsPlus.setHg(elementDTO.getHg());
                standardRawMaterialsPlus.setCd(elementDTO.getCd());
                standardRawMaterialsPlus.setZn(elementDTO.getZn());
                standardRawMaterialsPlus.setTe(elementDTO.getTe());
                standardRawMaterialsPlus.setSe(elementDTO.getSe());
                standardRawMaterialsPlus.setPr(elementDTO.getPr());
                standardRawMaterialsPlus.setNd(elementDTO.getNd());
                standardRawMaterialsPlus.setSc(elementDTO.getSc());
                standardRawMaterialsPlus.setY(elementDTO.getY());
                standardRawMaterialsPlus.setHf(elementDTO.getHf());
                standardRawMaterialsPlus.setPcm(elementDTO.getPcm());
                standardRawMaterialsPlus.setH(elementDTO.getH());
                standardRawMaterialsPlus.setO(elementDTO.getO());
                standardRawMaterialsPlus.setN(elementDTO.getN());

                baseMapper.updateByMaterialId(standardRawMaterialsPlus);
                StandardRawMaterials standardRawMaterials = new StandardRawMaterials();
                //先判断type是不是目标值，如果是目标值再进行转换
                if(elementDTO.getType().equals("目标值")) {
                    //把standardRawMaterialsPlus复制给standardRawMaterials
                    BeanUtils.copyProperties(standardRawMaterialsPlus, standardRawMaterials);

                    //复制完成之后，遍历standardRawMaterialsPlus中的所有元素，找出目标值最大的前两个
                    Map<String, BigDecimal> elementMap = new HashMap<>();
                    // 获取所有元素字段及其值
                    elementMap.put("C", standardRawMaterialsPlus.getC());
                    elementMap.put("Mn", standardRawMaterialsPlus.getMn());
                    elementMap.put("Si", standardRawMaterialsPlus.getSi());
                    elementMap.put("P", standardRawMaterialsPlus.getP());
                    elementMap.put("Cr", standardRawMaterialsPlus.getCr());
                    elementMap.put("V", standardRawMaterialsPlus.getV());
                    elementMap.put("Mo", standardRawMaterialsPlus.getMo());
                    elementMap.put("Ni", standardRawMaterialsPlus.getNi());
                    elementMap.put("W", standardRawMaterialsPlus.getW());
                    elementMap.put("Cu", standardRawMaterialsPlus.getCu());
                    elementMap.put("Ti", standardRawMaterialsPlus.getTi());
                    elementMap.put("Nb", standardRawMaterialsPlus.getNb());
                    elementMap.put("Co", standardRawMaterialsPlus.getCo());
                    elementMap.put("S", standardRawMaterialsPlus.getS());
                    elementMap.put("Sn", standardRawMaterialsPlus.getSn());
                    elementMap.put("Al", standardRawMaterialsPlus.getAl());
                    elementMap.put("Fe", standardRawMaterialsPlus.getFe());
                    elementMap.put("B", standardRawMaterialsPlus.getB());
                    elementMap.put("Zr", standardRawMaterialsPlus.getZr());
                    elementMap.put("La", standardRawMaterialsPlus.getLa());
                    elementMap.put("Ce", standardRawMaterialsPlus.getCe());
                    elementMap.put("Ca", standardRawMaterialsPlus.getCa());
                    elementMap.put("Pb", standardRawMaterialsPlus.getPb());
                    elementMap.put("Bi", standardRawMaterialsPlus.getBi());
                    elementMap.put("Sb", standardRawMaterialsPlus.getSb());
                    elementMap.put("As", standardRawMaterialsPlus.getAs());
                    elementMap.put("Als", standardRawMaterialsPlus.getAls());
                    elementMap.put("Ta", standardRawMaterialsPlus.getTa());
                    elementMap.put("Mg", standardRawMaterialsPlus.getMg());
                    elementMap.put("Ag", standardRawMaterialsPlus.getAg());
                    elementMap.put("Hg", standardRawMaterialsPlus.getHg());
                    elementMap.put("Cd", standardRawMaterialsPlus.getCd());
                    elementMap.put("Zn", standardRawMaterialsPlus.getZn());
                    elementMap.put("Te", standardRawMaterialsPlus.getTe());
                    elementMap.put("Se", standardRawMaterialsPlus.getSe());
                    elementMap.put("Pr", standardRawMaterialsPlus.getPr());
                    elementMap.put("Nd", standardRawMaterialsPlus.getNd());
                    elementMap.put("Sc", standardRawMaterialsPlus.getSc());
                    elementMap.put("Y", standardRawMaterialsPlus.getY());
                    elementMap.put("Hf", standardRawMaterialsPlus.getHf());
                    elementMap.put("Pcm", standardRawMaterialsPlus.getPcm());
                    elementMap.put("H", standardRawMaterialsPlus.getH());
                    elementMap.put("O", standardRawMaterialsPlus.getO());
                    elementMap.put("N", standardRawMaterialsPlus.getN());

                    // 过滤掉null值并按值大小排序
                    List<Map.Entry<String, BigDecimal>> sortedElements = elementMap.entrySet()
                            .stream()
                            .filter(entry -> entry.getValue() != null)
                            .sorted(Map.Entry.<String, BigDecimal>comparingByValue().reversed())
                            .collect(Collectors.toList());

                    if (!sortedElements.isEmpty()) {
                        // 设置主元素和基准品位
                        Map.Entry<String, BigDecimal> primaryElement = sortedElements.get(0);
                        standardRawMaterials.setElement(primaryElement.getKey());
                        standardRawMaterials.setComposition(primaryElement.getValue());

                        // 设置次元素（如果存在）
                        if (sortedElements.size() > 1) {
                            Map.Entry<String, BigDecimal> secondaryElement = sortedElements.get(1);
                            standardRawMaterials.setSecondaryElement(secondaryElement.getKey());
                        }
                    }
                    //设置C元素含量
                    if(standardRawMaterialsPlus.getC()!=null){
                        standardRawMaterials.setCarbonElement(standardRawMaterialsPlus.getC().toString());
                    }else {
                        standardRawMaterials.setCarbonElement(null);
                    }

                    // 保存到原料表中 - 修复：根据名称和设备ID查询
                    LambdaQueryWrapper<StandardRawMaterials> queryWrapper = new LambdaQueryWrapper<StandardRawMaterials>()
                        .eq(StandardRawMaterials::getName, standardRawMaterials.getName())
                        .eq(StandardRawMaterials::getProductionEquipmentId, standardRawMaterials.getProductionEquipmentId());
                    
                    StandardRawMaterials standardRawMaterials1 = standardRawMaterialsMapper.selectOne(queryWrapper);
                    
                    if (standardRawMaterials1 != null) {
                        // 更新现有记录
                        Long id1 = standardRawMaterials1.getId();
                        BeanUtils.copyProperties(standardRawMaterials, standardRawMaterials1);
                        standardRawMaterials1.setId(id1);
                        standardRawMaterialsMapper.updateById(standardRawMaterials1);
                    } else {
                        // 如果不存在，则插入新记录
                        standardRawMaterialsMapper.insert(standardRawMaterials);
                    }
                }
            }
            return true;
        }
    }

    @Override
    public StandardRawMaterialsPlusDTO selectById(String id) {
        // 1、先根据id查询standardRawMaterials表，得到原料数据
        LambdaQueryWrapper<StandardRawMaterialsPlus> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(StandardRawMaterialsPlus::getMaterialId, id);
        List<StandardRawMaterialsPlus> standardRawMaterialsPluses = standardRawMaterialsPlusMapper.selectList(wrapper2);
        StandardRawMaterialsPlus standardRawMaterialsPlus = standardRawMaterialsPluses.get(0);
        // 2、再根据原料数据中的equipmentId 查询productionEquipment表，得到生产设备名称，并赋值给standardRawMaterialsVO
        if (standardRawMaterialsPlus != null){
            Long equipmentId = standardRawMaterialsPlus.getProductionEquipmentId();
            if (equipmentId != null){
                LambdaQueryWrapper<ProductionEquipments> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ProductionEquipments::getId, equipmentId);
                ProductionEquipments productionEquipment = productionEquipmentsMapper.selectOne(wrapper);
                if (productionEquipment != null){
                    standardRawMaterialsPlus.setEquipmentName(productionEquipment.getEquipmentName());
                }
            }
        }
        // 3、再根据原料数据中的departmentId 查询departments表，得到部门名称，并赋值给standardRawMaterialsVO
        if (standardRawMaterialsPlus != null){
            Long departmentId = standardRawMaterialsPlus.getDepartmentId();
            if (departmentId != null){
                LambdaQueryWrapper<Departments> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(Departments::getId, departmentId);
                Departments departments = departmentsMapper.selectOne(wrapper);
                if (departments != null){
                    standardRawMaterialsPlus.setDepartmentName(departments.getDepartmentName());
                }
            }
        }
         // 5、返回standardRawMaterialsVO
        StandardRawMaterialsPlusDTO standardRawMaterialsPlusDTO = new StandardRawMaterialsPlusDTO();
        BeanUtils.copyProperties(standardRawMaterialsPlus, standardRawMaterialsPlusDTO);
        List<ElementDTO> elementDTOS = new ArrayList<>();
        for (StandardRawMaterialsPlus rawMaterialsPlus : standardRawMaterialsPluses) {
            ElementDTO elementDTO = new ElementDTO();
            BeanUtils.copyProperties(rawMaterialsPlus, elementDTO);
            elementDTOS.add(elementDTO);
        }
        standardRawMaterialsPlusDTO.setElementList(elementDTOS);
        return standardRawMaterialsPlusDTO;
    }

    @Override
    public Boolean deleteById(String l) {
        List<StandardRawMaterialsPlus> standardRawMaterialsPluses = standardRawMaterialsPlusMapper.selectList(
            new LambdaQueryWrapper<StandardRawMaterialsPlus>().eq(StandardRawMaterialsPlus::getMaterialId, l));
        
        if (!standardRawMaterialsPluses.isEmpty()) {
            StandardRawMaterialsPlus firstRecord = standardRawMaterialsPluses.get(0);
            String name = firstRecord.getName();
            Long equipmentId = firstRecord.getProductionEquipmentId();
            
            // 根据名称和设备ID删除旧表记录 - 修复删除逻辑
            standardRawMaterialsMapper.delete(new LambdaQueryWrapper<StandardRawMaterials>()
                .eq(StandardRawMaterials::getName, name)
                .eq(StandardRawMaterials::getProductionEquipmentId, equipmentId));
        }
        
        Boolean bool = standardRawMaterialsPlusMapper.deleteByMaterialId(l);
        return bool;
    }
}