package com.nercar.ingredient.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.ingredient.domain.po.PathMaterials;
import com.nercar.ingredient.mapper.PathMaterialsMapper;
import com.nercar.ingredient.service.PathMaterialsService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【path_materials(工艺路径材料关联表)】的数据库操作Service实现
* @createDate 2025-04-01 13:55:28
*/
@Service
public class PathMaterialsServiceImpl extends ServiceImpl<PathMaterialsMapper, PathMaterials>
    implements PathMaterialsService{

}




