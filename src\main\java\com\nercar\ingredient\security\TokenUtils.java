//package com.nercar.ingredient.security;
//
//public class TokenUtils {
//    public static boolean isValidToken(String token) {
//        if (token != null){
//            return token.equals("1000");
//        }
//        return false;
//    }
//    // private static final String TOKEN_PREFIX = "Bearer ";
//
//    // 模拟3个用户
//    // private static final Map<String, CurrentUser> userMap = new ConcurrentHashMap<>();
//    //
//    // static {
//    //     userMap.put("1000", new CurrentUser("1000", "admin", "1000"));
//    //     userMap.put("1001", new CurrentUser("1001", "guest", "1001"));
//    //     userMap.put("1002", new CurrentUser("1002", "user", "1002"));
//    // }
//
//    // public static String GetToken(HttpServletRequest request) {
//    //
//    //     String authorization = request.getHeader((HttpHeaders.AUTHORIZATION));
//    //     if (authorization != null && authorization.startsWith(TOKEN_PREFIX)) {
//    //         return authorization.substring(7);
//    //     }
//    //     return null;
//    // }
//    //
//    // public static void SetUserContext(HttpServletRequest request) {
//    //
//    //     String authorization = request.getHeader("Authorization");
//    //     if (authorization != null && authorization.startsWith(TOKEN_PREFIX)) {
//    //
//    //         String token = authorization.substring(7);
//    //
//    //         if (userMap.containsKey(token)) {
//    //             CurrentUser loginUser = userMap.get(token);
//    //             UserContext.setCurrentUser(loginUser);
//    //         }
//    //     }
//    // }
//}
