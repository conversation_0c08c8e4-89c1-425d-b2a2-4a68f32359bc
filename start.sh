#!/bin/bash
#这里可替换为你自己的执行程序，其他代码无需更改
APP_NAME=fushun-ingredient-0.0.1-SNAPSHOT.jar
# 指定JDK的绝对路径
JAVA_HOME=/usr/local/jdk-17.0.8
JAVA_CMD=$JAVA_HOME/bin/java

cd `dirname $0`
#使用说明，用来提示输入参数
usage() {
    echo "Usage: sh 执行脚本.sh [start|stop|restart|status]"
    exit 1
}
#检查程序是否在运行
is_exist(){
  # pid=`ps -ef|grep $APP_NAME|grep -v grep|awk '{print $2}' `
  pid=`ps -ef|grep $APP_NAME|grep -v grep|grep -v kill|awk '{print $2}'`
  #如果不存在返回1，存在返回0
  if [ -z "${pid}" ]; then
   return 1
  else
    return 0
  fi
}
 
#启动方法
start(){
  is_exist
  if [ $? -eq "0" ]; then
    echo "${APP_NAME} is already running. pid=${pid} ."
  else
    # 使用绝对路径启动Java应用
    nohup $JAVA_CMD -Xms64m -Xmx128m -Duser.timezone=Asia/Shanghai -Dfile.encoding=UTF-8 -Dspring.profiles.active=prod -jar $APP_NAME > /dev/null 2>&1 &
    echo "${APP_NAME} is start success"
    #tail -200f $APP_NAME.log
    #tail -f fileserver-web.out
  fi
}
 
#停止方法
stop(){
  is_exist
  if [ $? -eq "0" ]; then
    kill -9 $pid
    echo "${APP_NAME} is  stoped"
  else
    echo "${APP_NAME} is not running"
  fi
}
 
#输出运行状态
status(){
  is_exist
  if [ $? -eq "0" ]; then
    echo "${APP_NAME} is running. Pid is ${pid}"
  else
    echo "${APP_NAME} is NOT running."
  fi
}
 
#重启
restart(){
  stop
  start
}
 
#根据输入参数，选择执行对应方法，不输入则执行使用说明
case "$1" in
  "start")
    start
    ;;
  "stop")
    stop
    ;;
  "status")
    status
    ;;
  "restart")
    restart
    ;;
  *)
    usage
    ;;
esac