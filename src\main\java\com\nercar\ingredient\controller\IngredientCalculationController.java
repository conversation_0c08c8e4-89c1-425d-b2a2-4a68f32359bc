package com.nercar.ingredient.controller;


import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nercar.ingredient.domain.dto.*;
import com.nercar.ingredient.domain.po.*;
import com.nercar.ingredient.domain.vo.*;
import com.nercar.ingredient.mapper.CostEstimationMapper;
import com.nercar.ingredient.mapper.UsersMapper;
import com.nercar.ingredient.response.PageDataResult;
import com.nercar.ingredient.response.Result;
import com.nercar.ingredient.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Tag(name = "配料计算接口")
@RestController
@RequestMapping("/ingredientCalculation")
public class IngredientCalculationController {

    @Autowired
    private SteelGradesService steelGradesService;
    @Autowired
    private ExecutionStandardService executionStandardService;
    @Autowired
    private ProcessPathService processPathService;
    @Autowired
    private ProcessPathStepsService processPathStepsService;
    @Autowired
    private PurposeCompositionsService purposeCompositionsService;
    @Autowired
    private StandardCompositionsService standardCompositionsService;
    @Autowired
    private StandardIngredientRecordsService standardIngredientRecordsService;
    @Autowired
    private StandardRawMaterialsService standardRawMaterialsService;
    @Autowired
    private IngotYieldRatesService ingotYieldRatesService;
    @Autowired
    private MaterialYieldRatesService materialYieldRatesService;
    @Autowired
    private CostEstimationService costEstimationService;
    @Autowired
    private CalculationResultService calculationResultService;

    @Value("${remote-service.user-auth.base-url}")
    private String userAuthBaseUrl;

    @Value("${remote-service.user-auth.endpoints.token}")
    private String userTokenEndpoint;



    @Operation(summary = "分页查询工艺路径")
    @PostMapping("/getProcessPath")
    public PageDataResult<ProcessPathVO> getProcessPaths(@RequestBody SteelGradesDTO steelGradesDTO) {
        log.info("接收到通过数据传输对象（DTO）获取工艺路径的请求，DTO: {}", steelGradesDTO); // 添加日志记录
        SteelGrades one =null;
        if(steelGradesDTO.getSteelGrade().length()>0){
            LambdaQueryWrapper<SteelGrades> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like(StringUtils.hasLength(steelGradesDTO.getSteelGrade()), SteelGrades::getSteelGrade, steelGradesDTO.getSteelGrade());
            one = steelGradesService.getOne(queryWrapper);
        }
        IPage<ProcessPathVO> result = steelGradesService.selectPageProcessPath(steelGradesDTO,one);
        List<ProcessPathVO> records = result.getRecords();
        //遍历集合去查询对应的工序
        for (ProcessPathVO processPathVO : records) {
            //查找当前路径对应的工序以及设备信息
            List<PathSaveDTO> processPathSteps=processPathStepsService.selectProcessPathSteps(processPathVO);
            processPathVO.setSteps(processPathSteps);
        }
        long total = result.getTotal();
        log.info("工艺路径查询结果: {}", records); // 添加日志记录
        if (ObjectUtils.isEmpty(records)) {
            log.warn("未找到符合给定条件的工艺路径"); // 添加日志记录
            return PageDataResult.success(null, 0);
        }
        return PageDataResult.success(records, total);
    }

    @Operation(summary="获取推荐工艺路径")
    @PostMapping("/recommend")
    public Result<List<ProcessPathVO>> recommend() {
        // 1、查询使用频率最高的前两个路径
        List<ProcessPathVO> records = processPathService.selectRecommend();
        //遍历集合去查询对应的工序
        for (ProcessPathVO processPathVO : records) {
            //查找当前路径对应的工序以及设备信息
            List<PathSaveDTO> processPathSteps=processPathStepsService.selectProcessPathSteps(processPathVO);
            processPathVO.setSteps(processPathSteps);
        }
        log.info("工艺路径查询结果: {}", records); // 添加日志记录
        if (ObjectUtils.isEmpty(records)) {
            log.warn("未找到符合给定条件的工艺路径"); // 添加日志记录
            return Result.ok();
        }
        return Result.success(records);
    }


    @Operation(summary = "保存工艺路径")
    @PostMapping("/saveProcessPath")
    public Result<String> saveProcessPath(@RequestBody ProcessPathDTO processPathDTO) {
        log.info("接收到保存工艺路径的请求，DTO: {}", processPathDTO);
        Long id = processPathService.saveProcessPath(processPathDTO);
        return Result.ok(id.toString());
    }




    @Operation(summary = "标准成分检索")
    @PostMapping( "/getStandardCompositions")
    public Result<List<StandardCompositionsVO>> getStandardCompositions(@RequestBody StandardCompositionsQueryDTO standardCompositionsQueryDTO) {
        List<StandardCompositionsVO> list=standardCompositionsService.getStandardCompositions(standardCompositionsQueryDTO);
        if (ObjectUtils.isEmpty(list)) {
            log.warn("未找到符合给定条件的标准成分"); // 添加日志记录
            return Result.ok(list);
        }
        return Result.ok(list);
    }

    @Operation(summary = "自定义标准保存标准成分")
    @PostMapping("/saveStandardCompositions")
    public Result<String> saveStandardCompositions(@RequestBody StandardCompositionsQueryDTO standardCompositionsQueryDTO ) {
        log.info("接收到保存标准成分的请求，DTO: {}", standardCompositionsQueryDTO);
        Long id = standardCompositionsService.saveStandardCompositions(standardCompositionsQueryDTO);
        return Result.ok(id.toString());
    }

    @Operation(summary = "根据自定义保存标准ID去回显标准成分")
    @PostMapping("/getStandardCompositionsById/{id}")
    public Result<List<StandardCompositionsVO>> getStandardCompositionsById(@PathVariable("id") Long id) {
        List<StandardCompositionsVO> list=standardCompositionsService.getStandardCompositionsById(id);
        if (ObjectUtils.isEmpty(list)) {
            log.warn("未找到符合给定条件的标准成分"); // 添加日志记录
            return Result.ok();
        }
        return Result.ok(list);
    }

    @Operation( summary = "保存或者更新目标成分")
    @PostMapping("/updatePurposeCompositions")
    public Result<List> updatePurposeCompositions(@RequestBody PurposeCompositionsQueryDTO purposeCompositionsQueryDTO) {
        log.info("接收到修改目标成分的请求，DTO: {}", purposeCompositionsQueryDTO);
        List<String> ids = purposeCompositionsService.updatePurposeCompositions(purposeCompositionsQueryDTO);
        return Result.ok(ids);
    }

    @Operation( summary = "分页查询历史配料记录")
    @PostMapping("/getStandardIngredientRecords")
    public PageDataResult<StandardIngredientRecordsVO> getStandardIngredientRecords(@RequestBody  SteelGradesDTO steelGradesDTO) {
        log.info("接收到分页查询历史配料记录的请求，DTO: {}", steelGradesDTO);
        Long steelGradeId = null;
        if(steelGradesDTO.getSteelGrade().length()>0){
            LambdaQueryWrapper<SteelGrades> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like(StringUtils.hasLength(steelGradesDTO.getSteelGrade()), SteelGrades::getSteelGrade, steelGradesDTO.getSteelGrade());
            SteelGrades steelGrade = steelGradesService.getOne(queryWrapper);
            steelGradeId = steelGrade.getId();
        }
        LambdaQueryWrapper<StandardIngredientRecords> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(steelGradeId),StandardIngredientRecords::getSteelGradeId, steelGradeId)
               .eq(Objects.nonNull(steelGradesDTO.getProcessPathId()), StandardIngredientRecords::getProcessPathId, steelGradesDTO.getProcessPathId());
        IPage<StandardIngredientRecordsVO> result = standardIngredientRecordsService.selectPage(steelGradesDTO, wrapper);
        List<StandardIngredientRecordsVO> records = new ArrayList<>();
        List<StandardIngredientRecordsVO> records1 = result.getRecords();
        if(records1!=null){
            records.addAll(records1);
        }
        long total = result.getTotal();
        if (ObjectUtils.isEmpty(records)) {
            log.warn("未找到符合给定条件的历史配料记录 - 钢种: {}, 工艺路径ID: {}",
                    steelGradesDTO.getSteelGrade(), steelGradesDTO.getProcessPathId());
            return PageDataResult.success(records, 0);
        }
        return PageDataResult.success(records, total);
    }

    @Operation( summary = "查询原料清单")
    @PostMapping( "/getStandardRawMaterials")
    public Result<List<StandardRawMaterialsVO>> getStandardRawMaterials(@RequestBody List<StandardRawMaterialsDTO> standardRawMaterialsQueryDTOList) {
        List<StandardRawMaterialsVO> list= standardRawMaterialsService.getStandardRawMaterials(standardRawMaterialsQueryDTOList);
        if (ObjectUtils.isEmpty(list)) {
            log.warn("未找到符合给定条件的原料"); // 添加日志记录
            return Result.ok();
        }
        return Result.ok(list);
    }

    @Operation(summary="选择原料")
    @PostMapping("/getCustomizePathRawMaterials")
    public Result<List<RawMaterialsCategoryVO>> getCustomizePathRawMaterials(@RequestBody MaterialsCategoryDTO materialsCategoryDTO) {
        List<RawMaterialsCategoryVO> list=standardRawMaterialsService.getCustomizePathRawMaterials(materialsCategoryDTO);
        if (ObjectUtils.isEmpty(list)) {
            log.warn("未找到符合给定条件的原料"); // 添加日志记录
            return Result.ok();
        }
        return Result.ok(list);
    }



    @Operation(summary = "钢种成分检索最新标准成分")
    @PostMapping( "/getSteelGradesCompositions")
    public Result<List<StandardCompositionsVO>> getSteelGradesCompositions(@RequestBody SteelGradesDTO steelGradesDTO) {
        List<StandardCompositionsVO> list=new ArrayList<>();
        List<StandardCompositionsVO> steelGradesCompositions = standardCompositionsService.getSteelGradesCompositions(steelGradesDTO);
        if(steelGradesCompositions!=null){
            list.addAll(steelGradesCompositions);
        }
        if (ObjectUtils.isEmpty(list)) {
            log.warn("未找到符合给定条件的标准成分"); // 添加日志记录
            return Result.ok(list);
        }
        return Result.ok(list);
    }


    @Operation(summary="自定义工艺路径-树结构")
    @PostMapping("/tree")
    public Result<List<DepartmentsVO>> resourceTree() {
        List<DepartmentsVO> list = processPathService.getTree();
        if (ObjectUtils.isEmpty(list)) {
            log.warn("未找到符合给定条件的工艺路径"); // 添加日志记录
            return Result.failed("未找到符合给定条件的工艺路径");
        }
        return Result.ok(list);
    }


    @Operation(summary="选择已有路径后会根据路径ID查询对应的原料清单")
    @GetMapping("/getStandardRawMaterialsByPathId/{id}")
    public Result<List<StandardRawMaterialsVO>> getStandardRawMaterialsById(@PathVariable("id") Long id) {
        List<StandardRawMaterialsVO> list= standardRawMaterialsService.getStandardRawMaterialsById(id);
        if (ObjectUtils.isEmpty(list)) {
            log.warn("未找到符合给定条件的标准成分"); // 添加日志记录
            return Result.ok();
        }
        return Result.ok(list);
    }

    @Operation(summary="自定义路径根据ID找到模板查询原料")
    @GetMapping("/getStandardRawMaterialsByTemplateId/{id}")
    public Result<List<StandardRawMaterialsVO>> getStandardRawMaterialsByTemplateId(@PathVariable("id") Long id) {
        List<StandardRawMaterialsVO> list= standardRawMaterialsService.getStandardRawMaterialsByTemplateId(id);
        if (ObjectUtils.isEmpty(list)) {
            log.warn("未找到符合给定条件的标准成分"); // 添加日志记录
            return Result.ok();
        }
        return Result.ok(list);
    }



    //入参应该是钢种名字，标准名字，路径id，目标成分id集合，原料id集合，特殊要求,状态

    @Operation(summary = "计算", description = "执行配料计算或保存草稿。" +
        "支持两种使用方式：" +
        "1. 传统方式：先调用保存目标成分接口，再传入purposeCompositionsIds进行计算；" +
        "2. 新方式：直接传入purposeCompositionsData，系统自动保存目标成分后计算。" +
        "当status=0时保存草稿（执行数据保存但不调用算法接口），" +
        "当status=1时执行完整计算流程。")
    @PostMapping("/calculation")
    @Transactional(rollbackFor = Exception.class)
    public Result<CalculationResultDataVO> calculation(@RequestBody CalculationQueryDataDTO calculationQueryDataDTO){
//        log.info("Current User: {}", UserContext.getCurrentUser());

        // 参数验证增强
        if (calculationQueryDataDTO.getStatus() == null) {
            calculationQueryDataDTO.setStatus(1); // 默认为历史配料状态
        }

        log.info("接收到计算请求，状态: {}, 是否包含目标成分数据: {}",
                 calculationQueryDataDTO.getStatus(),
                 calculationQueryDataDTO.hasPurposeCompositionsData());

        CalculationResultDataVO calculationResultDataVO=calculationResultService.calculation(calculationQueryDataDTO);
        return Result.ok(calculationResultDataVO);
    }



    @Operation(summary="保存或者更新主表")
    @PostMapping("/saveOrUpdateMainTable")
    public Result<String> saveOrUpdateMainTable(@RequestBody StandardIngredientRecordsDTO standardIngredientRecordsDTO) {
        log.info("接收到保存或者更新主表的请求，DTO: {}", standardIngredientRecordsDTO);
        standardIngredientRecordsService.saveOrUpdateMainTable(standardIngredientRecordsDTO);
        return Result.ok();
    }

    @Operation(summary="保存或者更新所选路径对应的原料清单")
    @PostMapping("/saveOrUpdateStandardRawMaterials")
    public Result<String> saveOrUpdateStandardRawMaterials(@RequestBody PathMaterialsQueryDTO pathMaterialsQueryDTO) {
        log.info("接收到保存或者更新所选路径对应的原料清单的请求，DTO: {}", pathMaterialsQueryDTO);
        standardRawMaterialsService.saveOrUpdateStandardRawMaterials(pathMaterialsQueryDTO);
        return Result.ok();
    }


    @Operation(summary="查询计算结果")
    @PostMapping("/getCalculationResult")
    public Result<List<CalculationResultVO>> getCalculationResult(@RequestBody StandardRawMaterialsIdListDTO standardRawMaterialsIdList) {

        List<CalculationResultVO> list=standardCompositionsService.getCalculationResult(standardRawMaterialsIdList);
        if (ObjectUtils.isEmpty(list)) {
            log.warn("未找到符合给定条件的标准成分"); // 添加日志记录
            return Result.ok();
        }
        return Result.ok(list);
    }


    @Operation(summary = "提交")
    @PostMapping("/submit")
    public Result<SubmitResVO> submit( @RequestBody SubmitQueryDataDTO submitQueryDataDTO) {
        String id = submitQueryDataDTO.getId();
        SubmitResVO submitResVO = new SubmitResVO();
        // 初始化列表
        submitResVO.setIngotYieldRatesIds(new ArrayList<>());
        submitResVO.setMaterialYieldRatesIds(new ArrayList<>());
        List<IngotYieldRatesDTO> ingotYieldRatesDTOList=submitQueryDataDTO.getIngotYieldRatesDTOList();
        List<Long> ingotYieldRatesIds = ingotYieldRatesService.saveOrUpdateIngotYieldRate(ingotYieldRatesDTOList,id);
        List<String> ingotYieldRatesIds1 = new ArrayList<>();
        ingotYieldRatesIds1= submitResVO.getIngotYieldRatesIds();
        for (Long ingotYieldRatesId : ingotYieldRatesIds) {
            ingotYieldRatesIds1.add(String.valueOf(ingotYieldRatesId));
        }
        submitResVO.setIngotYieldRatesIds(ingotYieldRatesIds1);
        List<MaterialYieldRatesDTO> materialYieldRatesDTOList=submitQueryDataDTO.getMaterialYieldRatesDTOList();
        List<Long> materialYieldRatesIds = materialYieldRatesService.saveOrUpdateMaterialYieldRate(materialYieldRatesDTOList,id);
        List<String> materialYieldRatesIds1 = submitResVO.getMaterialYieldRatesIds();
        for (Long materialYieldRatesId : materialYieldRatesIds) {
            materialYieldRatesIds1.add(String.valueOf(materialYieldRatesId));
        }
        submitResVO.setMaterialYieldRatesIds(materialYieldRatesIds1);
        HttpResVo httpResVo = standardIngredientRecordsService.sendData(Long.parseLong(submitQueryDataDTO.getId()));
        log.info("提交结果: {}", httpResVo);
        return Result.success(submitResVO);
    }

//    @Operation(summary="查询成锭率以及成材率")
//    @
//    @Operation(summary="编辑或者保存成锭率")
//    @PostMapping("/saveOrUpdateIngotYieldRate")
//    public Result<List<Long>> saveOrUpdateIngotYieldRate(@RequestBody List<IngotYieldRatesDTO> ingotYieldRatesDTOList) {
//        log.info("接收到保存成锭率的请求，DTO: {}", ingotYieldRatesDTOList);
//        List<Long> ids = ingotYieldRatesService.saveOrUpdateIngotYieldRate(ingotYieldRatesDTOList);
//        return Result.ok(ids);
//    }


//    @Operation(summary="编辑或者保存成材率")
//    @PostMapping("/saveOrUpdateMaterialYieldRate")
//    public Result<List<Long>> saveOrUpdateMaterialYieldRate(@RequestBody List<MaterialYieldRatesDTO> materialYieldRatesDTOList) {
//        log.info("接收到保存成材率的请求，DTO: {}", materialYieldRatesDTOList);
//        List<Long> ids = materialYieldRatesService.saveOrUpdateMaterialYieldRate(materialYieldRatesDTOList);
//        return Result.ok(ids);
//    }



    @Operation(summary="预览测算表")
    @GetMapping("/previewIngredient/{id}")
    public void previewIngredient(@PathVariable("id") Long id,
                                 @RequestParam(value = "sequence", required = false)
                                 String calculationSequence,
                                 HttpServletResponse response) {
        standardIngredientRecordsService.previewDownload(id, calculationSequence, response);
    }
    @Operation(summary="生成测算表")
    @PostMapping("/excel/download/{id}")
    public void download(@PathVariable("id") Long id,
                        @RequestParam(value = "sequence", required = false) String calculationSequence,
                        HttpServletResponse response) {
        standardIngredientRecordsService.download(id, calculationSequence, response);
    }

    @Operation(summary="生成测算表（支持单个/批量）")
    @PostMapping("/excel/download")
    public void downloadBatch(@RequestBody DownloadRequestDTO request, HttpServletResponse response) {
        log.info("接收到下载测算表请求，原始请求: {}", request);

        if (request == null) {
            log.error("请求对象为空");
            throw new IllegalArgumentException("请求对象不能为空");
        }

        if (request.getIds() == null || request.getIds().isEmpty()) {
            log.error("ID列表为空或null");
            throw new IllegalArgumentException("ID列表不能为空");
        }

        log.info("解析到的ID列表: {}, 数量: {}", request.getIds(), request.getIds().size());

        List<Long> longIds = request.getLongIds();
        List<String> sequences = request.getCalculationSequences();

        if (longIds.size() == 1) {
            // 单个文件下载
            Long id = longIds.get(0);
            String sequence = (sequences != null && !sequences.isEmpty()) ? sequences.get(0) : null;
            log.info("单个文件下载，ID: {} (原始字符串: {}), 批次: {}", id, request.getIds().get(0), sequence);
            standardIngredientRecordsService.download(id, sequence, response);
            log.info("单个文件下载完成，ID: {}, 批次: {}", id, sequence);
        } else {
            // 多个文件，生成压缩包
            if (sequences != null && sequences.size() == longIds.size()) {
                // 使用指定批次
                log.info("批量文件下载（指定批次），ID列表: {}, 批次列表: {}", longIds, sequences);
                standardIngredientRecordsService.batchDownloadWithSequences(longIds, sequences, response);
                log.info("批量文件下载完成（指定批次），ID列表: {}", longIds);
            } else {
                // 使用默认批次（向后兼容）
                log.info("批量文件下载（默认批次），ID列表: {} (原始字符串: {})", longIds, request.getIds());
                standardIngredientRecordsService.batchDownload(longIds, response);
                log.info("批量文件下载完成（默认批次），ID列表: {}", longIds);
            }
        }
    }

    @Operation(summary="接收成本测算参数")
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/getCostCalculationParameter")
    public Result<Boolean> getCostCalculationParameter(@RequestBody CostEstimation costEstimation) {
        log.info("接收到成本测算参数的请求，DTO: {}", costEstimation);
        log.info("成本测算参数详情 - 测算ID: {}, 用户: {}, 合同ID: {}",
                 costEstimation.getId(), costEstimation.getUsername(), costEstimation.getContractId());
        //拿到参数后直接存入表格
//        //存入表格  ThreadLocal
//        UserContext.setCurrentUser(new CurrentUser(costEstimation.getUserId(),costEstimation.getUsername(),"",costEstimation.getDepartmentId(),costEstimation.getId().toString()));

        Long id = costEstimation.getId();
        if(costEstimationService.getById(id)==null){
            costEstimation.setNum(1);
            costEstimation.setCreatedTime(LocalDateTime.now()); // 创建时间戳
            costEstimation.setUpdateTime(LocalDateTime.now());
            return Result.ok(costEstimationService.saveOrUpdate(costEstimation));
        }

        costEstimation.setNum(2);
        costEstimation.setUpdateTime(LocalDateTime.now()); // 更新时间戳
        return Result.ok(costEstimationService.updateById(costEstimation));
    }


    @Autowired
    private UsersMapper usersMapper;
    @Autowired
    private CostEstimationMapper costEstimationMapper;
    @Operation(summary="接收前端测算id和token")
    @PostMapping("/getCostCalculationParameterIdAndToken")
    public Result<CostEstimation> getCostCalculationParameterIdAndToken(@RequestBody CalculationIdAndTokenDTO calculationIdAndTokenDTO) {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        try {
            httpClient = HttpClients.createDefault();
            // 使用配置的URL
            HttpPost httpPost = new HttpPost(userAuthBaseUrl + userTokenEndpoint);

            // 设置请求头
            httpPost.setHeader("Authorization", calculationIdAndTokenDTO.getToken());

            // 执行请求
            response = httpClient.execute(httpPost);

            // 获取响应内容
            String responseString = EntityUtils.toString(response.getEntity());
            ObjectMapper mapper = new ObjectMapper();

            // 配置ObjectMapper以处理日期时间
            mapper.findAndRegisterModules();

            HttpResVo httpResVo = mapper.readValue(responseString, HttpResVo.class);

            if (httpResVo.getCode() == 200) {
                Map<String, Object> userData = (Map<String, Object>) httpResVo.getData();
                Users user = new Users();
                user.setId(Long.valueOf(userData.get("id").toString()));
                user.setUserName((String) userData.get("userName"));
                user.setNickName((String) userData.get("nickName"));
                String password = (String) userData.get("passWord");
                user.setPassWord(DigestUtils.md5DigestAsHex(password.getBytes()));
//                user.setDepartmentId(Long.valueOf(userData.get("departmentId").toString()));
                String calculationId = calculationIdAndTokenDTO.getCalculationId();
                CostEstimation costEstimation1 = costEstimationMapper.selectById(Long.valueOf(calculationId));
                costEstimation1.setUserIds(user.getId());
                costEstimationMapper.updateById(costEstimation1);
                // 修复用户查询和更新逻辑，避免重复数据和ID冲突
                handleUserIdConflict(user);
                // 使用Sa-Token登录并生成token
                StpUtil.login(user.getId());
                String localToken = StpUtil.getTokenValue();

                // 存储用户信息到Sa-Token
                StpUtil.getSession().set("userName", user.getUserName());
//                StpUtil.getSession().set("departmentId", user.getDepartmentId());

                CostEstimation costEstimation = costEstimationService.getBaseMapper().selectById(Long.parseLong(calculationIdAndTokenDTO.getCalculationId()));
                if (costEstimation != null) {
                    costEstimation.setToken(localToken);
                    costEstimation.setNickName(user.getNickName());

                    // 如果前端传递了合同ID，则更新到数据库中
                    if (calculationIdAndTokenDTO.getContractId() != null && !calculationIdAndTokenDTO.getContractId().trim().isEmpty()) {
                        costEstimation.setContractId(calculationIdAndTokenDTO.getContractId());
                        costEstimationService.updateById(costEstimation);
                        log.info("更新成本测算合同ID，测算ID: {}, 合同ID: {}", calculationIdAndTokenDTO.getCalculationId(), calculationIdAndTokenDTO.getContractId());
                    }

                    // 临时写死合同ID，用于测试（TODO: 后续从实际业务流程中获取）
                    if (costEstimation.getContractId() == null || costEstimation.getContractId().trim().isEmpty()) {
                        costEstimation.setContractId("1001"); // 写死返回测试合同ID
                        log.info("临时设置合同ID为测试数据: 1001");
                    }

                    log.info("返回成本测算信息，测算ID: {}, 合同ID: {}", costEstimation.getId(), costEstimation.getContractId());
                    return Result.success(costEstimation);
                }
                return Result.success(costEstimation);
            }
            return Result.failed("token失效");
        } catch (Exception e) {
            log.error("请求失败", e);
            return Result.failed("请求异常: " + e.getMessage());
        } finally {
            // 关闭资源
            try {
                if (response != null) {
                    response.close();
                }
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                log.error("关闭资源失败", e);
            }
        }
    }

    /**
     * 处理用户ID冲突的方法
     * 当外部系统的用户ID与本地已存在的用户ID冲突时，修改本地用户的ID
     * @param newUser 外部系统传来的用户信息
     */
    @Transactional(rollbackFor = Exception.class, isolation = org.springframework.transaction.annotation.Isolation.SERIALIZABLE)
    private void handleUserIdConflict(Users newUser) {
        try {
            log.info("开始处理用户ID冲突，外部用户ID: {}, 用户名: {}", newUser.getId(), newUser.getUserName());

            // 1. 检查用户名是否存在（优先处理）
            LambdaQueryWrapper<Users> nameWrapper = new LambdaQueryWrapper<>();
            nameWrapper.eq(Users::getUserName, newUser.getUserName());
            List<Users> usersByName = usersMapper.selectList(nameWrapper);

            if (!usersByName.isEmpty()) {
                // 用户名存在，更新现有用户信息
                Users existingUser = usersByName.get(0);
                log.info("用户名已存在，更新用户信息，用户名: {}, 现有ID: {}", newUser.getUserName(), existingUser.getId());
                newUser.setId(existingUser.getId()); // 使用现有用户的ID
                usersMapper.updateById(newUser);
                return;
            }

            // 2. 检查ID是否冲突
            LambdaQueryWrapper<Users> idWrapper = new LambdaQueryWrapper<>();
            idWrapper.eq(Users::getId, newUser.getId());
            List<Users> usersById = usersMapper.selectList(idWrapper);

            if (!usersById.isEmpty()) {
                // ID冲突，修改本地用户ID
                Users conflictUser = usersById.get(0);
                Long originalId = conflictUser.getId();
                Long newLocalId = generateSafeUserId();

                log.info("检测到用户ID冲突，原ID: {}, 冲突用户名: {}, 新分配ID: {}",
                        originalId, conflictUser.getUserName(), newLocalId);

                // 清除可能的Sa-Token会话
                try {
                    StpUtil.logout(originalId);
                    log.info("已清除冲突用户的Sa-Token会话，用户ID: {}", originalId);
                } catch (Exception e) {
                    log.warn("清除用户会话失败，用户ID: {}, 错误: {}", originalId, e.getMessage());
                }

                // 修改本地用户ID - 根据用户名更新ID
                LambdaUpdateWrapper<Users> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(Users::getUserName, conflictUser.getUserName())  // WHERE user_name = 冲突用户名
                             .set(Users::getId, newLocalId);  // SET id = 新ID
                int updateCount = usersMapper.update(null, updateWrapper);

                if (updateCount == 0) {
                    log.error("用户ID修改失败，没有找到用户名为 {} 的用户", conflictUser.getUserName());
                    throw new RuntimeException("用户ID修改失败，用户不存在");
                }

                // 验证修改是否成功
                Users verifyUser = usersMapper.selectById(originalId);
                if (verifyUser != null) {
                    log.error("用户ID修改失败，ID {} 仍然存在", originalId);
                    throw new RuntimeException("用户ID修改失败，无法解决冲突");
                }

                log.info("用户ID冲突已解决，原用户 {} 的ID从 {} 修改为 {}",
                        conflictUser.getUserName(), originalId, newLocalId);
            }

            // 3. 插入新用户前再次验证ID可用性
            Users finalCheck = usersMapper.selectById(newUser.getId());
            if (finalCheck != null) {
                log.error("插入前检查发现ID {} 仍然被占用，用户名: {}", newUser.getId(), finalCheck.getUserName());
                throw new RuntimeException("用户ID仍然被占用，无法插入新用户");
            }

            usersMapper.insert(newUser);
            log.info("新用户插入成功，用户名: {}, ID: {}", newUser.getUserName(), newUser.getId());

        } catch (Exception e) {
            log.error("处理用户ID冲突失败，用户名: {}, ID: {}", newUser.getUserName(), newUser.getId(), e);
            throw new RuntimeException("用户ID冲突处理失败: " + e.getMessage());
        }
    }

    /**
     * 生成安全的用户ID，避免与现有ID冲突
     * @return 新的用户ID
     */
    private Long generateSafeUserId() {
        try {
            // 使用时间戳基础生成ID，避免与现有小ID冲突
            Long baseId = System.currentTimeMillis() % 100000000; // 取后8位
            if (baseId < 100000) {
                baseId += 100000; // 确保ID足够大，避免与现有小ID冲突
            }

            Long newId = baseId;
            int attempts = 0;
            int maxAttempts = 100;

            // 循环检查直到找到可用ID
            while (usersMapper.selectById(newId) != null && attempts < maxAttempts) {
                newId++;
                attempts++;
                log.debug("ID {} 已被占用，尝试下一个ID，尝试次数: {}", newId - 1, attempts);
            }

            if (attempts >= maxAttempts) {
                // 如果尝试次数过多，使用更大的基数
                newId = System.currentTimeMillis() + 1000000;
                log.warn("生成安全ID尝试次数过多，使用更大基数: {}", newId);
            }

            // 最终验证
            if (usersMapper.selectById(newId) != null) {
                throw new RuntimeException("无法生成安全的用户ID，ID " + newId + " 仍然被占用");
            }

            log.info("生成新的安全用户ID: {}, 尝试次数: {}", newId, attempts);
            return newId;

        } catch (Exception e) {
            log.error("生成安全用户ID失败", e);
            throw new RuntimeException("生成安全用户ID失败: " + e.getMessage());
        }
    }

}
