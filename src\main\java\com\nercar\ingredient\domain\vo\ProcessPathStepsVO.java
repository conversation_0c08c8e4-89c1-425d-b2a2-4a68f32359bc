package com.nercar.ingredient.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 工序表
 * @TableName process_path_steps
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProcessPathStepsVO extends BaseVO {
    /**
     * 主键
     */
    @TableId
    @Schema(description = "主键")
    private Long id;

    /**
     * 步骤编号
     */
    @Schema(description = "步骤编号")
    private String stepNumber;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID")
    private Long equipmentId;
    /**
     * 工序类别，1代表设备，2代表模板
     */
    @Schema(description = "工序类别，1代表设备，2代表模板")
    private Integer type;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 关联的工艺路径ID
     */
    @Schema(description = "关联的工艺路径ID")
    private Long pathId;

    /**
     * 
     */
    private Date createtime;

    /**
     * 
     */
    private Date updatetime;




}