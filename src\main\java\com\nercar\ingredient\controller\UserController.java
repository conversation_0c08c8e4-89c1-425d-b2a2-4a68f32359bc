package com.nercar.ingredient.controller;


import cn.dev33.satoken.stp.StpUtil;
import com.nercar.ingredient.domain.bo.CurrentUser;
import com.nercar.ingredient.domain.dto.UsersDTO;
import com.nercar.ingredient.domain.po.Users;
import com.nercar.ingredient.domain.vo.UsersVO;
import com.nercar.ingredient.mapper.DepartmentsMapper;
import com.nercar.ingredient.response.Result;
import com.nercar.ingredient.security.UserContext;
import com.nercar.ingredient.service.UsersService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Tag(name = "用户接口")
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UsersService usersService;

    @Autowired
    private DepartmentsMapper departmentsMapper;


    @PostMapping("/login")
    @Operation(summary = "登录")
    public Result<UsersVO> login(@RequestBody UsersDTO usersDTO) {
        log.info("用户登录：{}", usersDTO);

        // 1. 验证登录
        Users users = usersService.login(usersDTO);

        // 2. 使用Sa-Token登录
        StpUtil.login(users.getId());
        // 将用户名存储到 Sa-Token 的 Session 中
        StpUtil.getSession().set("userName", users.getUserName());
        String token = StpUtil.getTokenValue();

        // 3. 返回用户信息和token
        UsersVO usersVO = new UsersVO();
        BeanUtils.copyProperties(users, usersVO);
        usersVO.setToken(token);  // Sa-Token的token

        return Result.success(usersVO);
    }

    @Operation(summary = "获取当前登录用户信息")
    @GetMapping("/info")
    public Result<UsersVO> info() {
        return usersService.getCurrentUserInfo();
    }

    @PostMapping("/logout")
    @Operation(summary = "退出登录")
    public Result<String> logout() {
        try {
            // 获取当前用户信息
            CurrentUser currentUser = UserContext.getCurrentUser();
            if (currentUser != null) {
                log.info("用户退出登录：{}", currentUser.getUsername());
                // 清除用户上下文
                StpUtil.logout();  // 使用Sa-Token的登出方法
                UserContext.clear();
                StpUtil.kickoutByTokenValue("token");
                return Result.success("退出成功");
            }
            return Result.success("用户未登录");
        } catch (Exception e) {
            log.error("退出登录失败", e);
            return Result.failed("退出失败：" + e.getMessage());
        }
    }




}
